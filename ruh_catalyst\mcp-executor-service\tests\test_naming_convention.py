#!/usr/bin/env python3
"""
Test script to verify the container naming convention fix.
"""

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_naming_convention():
    """Test the container naming convention logic."""
    logger.info("=== Testing Container Naming Convention ===")
    
    # Test parameters from the logs
    mcp_id = "35441857-f358-4595-9993-33abd9afee06"
    user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"
    container_id_from_api = "035378d0-ae4e-4e4f-a23b-9982d16d2d8f"
    
    logger.info(f"MCP ID: {mcp_id}")
    logger.info(f"User ID: {user_id}")
    logger.info(f"Container ID from API: {container_id_from_api}")
    
    # APPROACH 1: Use API container ID (what we tried before)
    approach1 = container_id_from_api
    logger.info(f"❌ Approach 1 (API ID): {approach1}")
    
    # APPROACH 2: Use {mcp_id}_{user_id} convention (recommended)
    approach2 = f"{mcp_id}_{user_id}"
    logger.info(f"✅ Approach 2 (Standard naming): {approach2}")
    
    # Analysis
    logger.info("\n📊 Analysis:")
    logger.info("- API returns a unique container ID for tracking")
    logger.info("- But actual Docker container name follows {mcp_id}_{user_id} pattern")
    logger.info("- This allows consistent naming regardless of API response")
    logger.info("- Multiple requests with same mcp_id + user_id = same container")
    
    return approach2


def test_log_comparison():
    """Compare with actual log data."""
    logger.info("\n=== Log Comparison ===")
    
    # From the actual error logs
    failed_container = "035378d0-ae4e-4e4f-a23b-9982d16d2d8f"
    mcp_id = "35441857-f358-4595-9993-33abd9afee06"
    user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"
    
    logger.info(f"❌ Failed to find: {failed_container}")
    
    # What we should be looking for
    expected_container = f"{mcp_id}_{user_id}"
    logger.info(f"✅ Should look for: {expected_container}")
    
    # Verify they're different
    if failed_container != expected_container:
        logger.info("🎯 Confirmed: We were looking for the wrong container name!")
        return True
    else:
        logger.warning("🤔 Container names match - issue might be elsewhere")
        return False


def test_multiple_scenarios():
    """Test multiple scenarios to verify the approach."""
    logger.info("\n=== Multiple Scenarios ===")
    
    scenarios = [
        {
            "name": "Scenario 1 (from logs)",
            "mcp_id": "35441857-f358-4595-9993-33abd9afee06",
            "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
            "api_container_id": "035378d0-ae4e-4e4f-a23b-9982d16d2d8f"
        },
        {
            "name": "Scenario 2 (hypothetical)",
            "mcp_id": "12345678-1234-1234-1234-123456789012",
            "user_id": "87654321-4321-4321-4321-210987654321",
            "api_container_id": "abcdef12-3456-7890-abcd-ef1234567890"
        }
    ]
    
    for scenario in scenarios:
        logger.info(f"\n{scenario['name']}:")
        logger.info(f"  MCP ID: {scenario['mcp_id']}")
        logger.info(f"  User ID: {scenario['user_id']}")
        logger.info(f"  API Container ID: {scenario['api_container_id']}")
        
        # Standard naming convention
        standard_name = f"{scenario['mcp_id']}_{scenario['user_id']}"
        logger.info(f"  ✅ Standard container name: {standard_name}")
        
        # Verify uniqueness
        logger.info(f"  🔍 Unique per user+mcp: {len(standard_name) > 0}")
    
    return True


def main():
    """Main test function."""
    logger.info("Starting Container Naming Convention Tests")
    
    # Test 1: Basic naming convention
    expected_name = test_naming_convention()
    
    # Test 2: Log comparison
    success2 = test_log_comparison()
    
    # Test 3: Multiple scenarios
    success3 = test_multiple_scenarios()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY:")
    logger.info("="*60)
    logger.info(f"Container naming convention: ✅ PASS")
    logger.info(f"Log comparison: {'✅ PASS' if success2 else '❌ FAIL'}")
    logger.info(f"Multiple scenarios: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    logger.info(f"\n🎯 RECOMMENDED APPROACH:")
    logger.info(f"Use container name: {expected_name}")
    logger.info(f"This follows the {'{mcp_id}_{user_id}'} pattern")
    
    if success2 and success3:
        logger.info("🎉 Container naming convention fix is ready!")
        logger.info("🚀 This should resolve the 'container not found' errors!")
    else:
        logger.warning("🤔 Some tests failed - review the approach")


if __name__ == "__main__":
    main()
