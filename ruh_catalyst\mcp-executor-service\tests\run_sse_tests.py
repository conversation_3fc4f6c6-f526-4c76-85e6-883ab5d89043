#!/usr/bin/env python3
"""
Test runner script for SSE connection tests.
This script provides an easy way to run the SSE connection validation tests
with proper setup and configuration.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.test_sse_connection import SSEConnectionTester

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)


async def run_sse_tests():
    """Run SSE connection tests with proper setup."""
    logger.info("🚀 Starting SSE Connection Test Suite")
    logger.info("=" * 60)
    
    # Check environment
    logger.info("Checking test environment...")
    
    # Check if Kaf<PERSON> is configured
    try:
        from app.config.config import settings
        logger.info(f"Kafka Bootstrap Servers: {settings.kafka_bootstrap_servers}")
        logger.info(f"Consumer Topic: {settings.kafka_consumer_topic}")
        logger.info(f"Results Topic: {settings.kafka_results_topic}")
    except Exception as e:
        logger.warning(f"Could not load Kafka settings: {e}")
        logger.warning("Some tests may fail if Kafka is not properly configured")
    
    # Run the tests
    tester = SSEConnectionTester()
    
    try:
        results = await tester.run_all_tests()
        
        # Print final summary
        logger.info("\n" + "=" * 60)
        logger.info("FINAL TEST RESULTS")
        logger.info("=" * 60)
        
        passed_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            logger.info(f"  {test_name}: {status}")
        
        logger.info("=" * 60)
        logger.info(f"SUMMARY: {passed_count}/{total_count} tests passed")
        
        if passed_count == total_count:
            logger.info("🎉 ALL SSE TESTS PASSED! The SSE functionality is working correctly.")
            return 0
        else:
            logger.error(f"💥 {total_count - passed_count} tests failed!")
            logger.error("Please check the logs above for details on failed tests.")
            return 1
            
    except Exception as e:
        logger.error(f"Test suite failed with error: {e}", exc_info=True)
        return 1


def main():
    """Main entry point."""
    logger.info("SSE Connection Test Runner")
    logger.info("This script validates that SSE connections work correctly")
    logger.info("after the SSH Docker client implementation.\n")
    
    try:
        exit_code = asyncio.run(run_sse_tests())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test run interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
