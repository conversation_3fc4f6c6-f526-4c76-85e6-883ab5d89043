# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: workflow.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'workflow.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0eworkflow.proto\x12\x08workflow\x1a google/protobuf/field_mask.proto\"\x13\n\x05Owner\x12\n\n\x02id\x18\x01 \x01(\t\"\xe1\x02\n\x15\x43reateWorkflowRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x15\n\rworkflow_data\x18\x03 \x01(\t\x12\x1e\n\x05owner\x18\x04 \x01(\x0b\x32\x0f.workflow.Owner\x12\x10\n\x08user_ids\x18\x05 \x03(\t\x12/\n\nowner_type\x18\x06 \x01(\x0e\x32\x1b.workflow.WorkflowOwnerType\x12\x30\n\nvisibility\x18\x07 \x01(\x0e\x32\x1c.workflow.WorkflowVisibility\x12,\n\x08\x63\x61tegory\x18\x08 \x01(\x0e\x32\x1a.workflow.WorkflowCategory\x12\x0c\n\x04tags\x18\t \x03(\t\x12(\n\x06status\x18\n \x01(\x0e\x32\x18.workflow.WorkflowStatus\x12\x13\n\x0bstart_nodes\x18\x0b \x03(\t\"\xf8\x03\n\x15UpdateWorkflowRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12/\n\x0bupdate_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x15\n\rworkflow_data\x18\x05 \x01(\t\x12\x1e\n\x05owner\x18\x06 \x01(\x0b\x32\x0f.workflow.Owner\x12\x10\n\x08user_ids\x18\x07 \x03(\t\x12\x30\n\nvisibility\x18\x08 \x01(\x0e\x32\x1c.workflow.WorkflowVisibility\x12,\n\x08\x63\x61tegory\x18\t \x01(\x0e\x32\x1a.workflow.WorkflowCategory\x12\x0c\n\x04tags\x18\n \x03(\t\x12(\n\x06status\x18\x0b \x01(\x0e\x32\x18.workflow.WorkflowStatus\x12\x13\n\x0bstart_nodes\x18\x0c \x03(\t\x12\x0f\n\x07version\x18\r \x01(\t\x12\x17\n\nis_updated\x18\x0e \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0fis_customizable\x18\x0f \x01(\x08H\x01\x88\x01\x01\x12\x1e\n\x16is_changes_marketplace\x18\x10 \x01(\x08\x42\r\n\x0b_is_updatedB\x12\n\x10_is_customizable\"B\n\x12GetWorkflowRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_user_id\"C\n\x15\x44\x65leteWorkflowRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x1e\n\x05owner\x18\x02 \x01(\x0b\x32\x0f.workflow.Owner\"\xd3\x02\n\x14ListWorkflowsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x31\n\x08\x63\x61tegory\x18\x03 \x01(\x0e\x32\x1a.workflow.WorkflowCategoryH\x00\x88\x01\x01\x12-\n\x06status\x18\x04 \x01(\x0e\x32\x18.workflow.WorkflowStatusH\x01\x88\x01\x01\x12\x35\n\nvisibility\x18\x05 \x01(\x0e\x32\x1c.workflow.WorkflowVisibilityH\x02\x88\x01\x01\x12\n\n\x02id\x18\x06 \x01(\t\x12\x13\n\x06search\x18\x07 \x01(\tH\x03\x88\x01\x01\x12\x0c\n\x04tags\x18\x08 \x03(\t\x12\x14\n\x07user_id\x18\t \x01(\tH\x04\x88\x01\x01\x42\x0b\n\t_categoryB\t\n\x07_statusB\r\n\x0b_visibilityB\t\n\x07_searchB\n\n\x08_user_id\"\x91\x02\n\x1cListWorkflowsByUserIdRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x31\n\x08\x63\x61tegory\x18\x04 \x01(\x0e\x32\x1a.workflow.WorkflowCategoryH\x00\x88\x01\x01\x12-\n\x06status\x18\x05 \x01(\x0e\x32\x18.workflow.WorkflowStatusH\x01\x88\x01\x01\x12\x35\n\nvisibility\x18\x06 \x01(\x0e\x32\x1c.workflow.WorkflowVisibilityH\x02\x88\x01\x01\x42\x0b\n\t_categoryB\t\n\x07_statusB\r\n\x0b_visibility\"\xce\x04\n\x08Workflow\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x14\n\x0cworkflow_url\x18\x04 \x01(\t\x12\x13\n\x0b\x62uilder_url\x18\x05 \x01(\t\x12\x10\n\x08owner_id\x18\x06 \x01(\t\x12\x10\n\x08user_ids\x18\x07 \x03(\t\x12\x12\n\nowner_type\x18\x08 \x01(\t\x12!\n\x14workflow_template_id\x18\t \x01(\tH\x00\x88\x01\x01\x12\x1e\n\x11template_owner_id\x18\n \x01(\tH\x01\x88\x01\x01\x12\x10\n\x03url\x18\x0b \x01(\tH\x02\x88\x01\x01\x12\x13\n\x0bis_imported\x18\x0c \x01(\x08\x12\x17\n\x0f\x65xecution_count\x18\r \x01(\x05\x12\x0f\n\x07version\x18\x0e \x01(\t\x12\x12\n\nvisibility\x18\x0f \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x10 \x01(\t\x12\x0c\n\x04tags\x18\x11 \x03(\t\x12\x0e\n\x06status\x18\x12 \x01(\t\x12\x12\n\ncreated_at\x18\x13 \x01(\t\x12\x12\n\nupdated_at\x18\x14 \x01(\t\x12\x13\n\x0bstart_nodes\x18\x15 \x03(\t\x12\x17\n\x0f\x61vailable_nodes\x18\x16 \x03(\t\x12\x12\n\nis_updated\x18\x17 \x01(\x08\x12\x17\n\x0fis_customizable\x18\x18 \x01(\x08\x12\x1e\n\x16is_changes_marketplace\x18\x19 \x01(\x08\x42\x17\n\x15_workflow_template_idB\x14\n\x12_template_owner_idB\x06\n\x04_url\"`\n\x16\x43reateWorkflowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12$\n\x08workflow\x18\x03 \x01(\x0b\x32\x12.workflow.Workflow\":\n\x16UpdateWorkflowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\":\n\x16\x44\x65leteWorkflowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"l\n\x10WorkflowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x08workflow\x18\x03 \x01(\x0b\x32\x12.workflow.WorkflowH\x00\x88\x01\x01\x42\x0b\n\t_workflow\"\x81\x01\n\x15ListWorkflowsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12%\n\tworkflows\x18\x02 \x03(\x0b\x32\x12.workflow.Workflow\x12\r\n\x05total\x18\x03 \x01(\x05\x12\x0c\n\x04page\x18\x04 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x05 \x01(\x05\"\xc6\x03\n\x10WorkflowTemplate\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x14\n\x0cworkflow_url\x18\x04 \x01(\t\x12\x13\n\x0b\x62uilder_url\x18\x05 \x01(\t\x12\x17\n\x0f\x65xecution_count\x18\x06 \x01(\x05\x12\x10\n\x08owner_id\x18\x07 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x08 \x01(\t\x12\x0c\n\x04tags\x18\t \x03(\t\x12\x0f\n\x07version\x18\n \x01(\t\x12\x0e\n\x06status\x18\x0b \x01(\t\x12\x12\n\ncreated_at\x18\x0c \x01(\t\x12\x12\n\nupdated_at\x18\r \x01(\t\x12\x13\n\x0bstart_nodes\x18\x0e \x03(\t\x12\x17\n\x0f\x61vailable_nodes\x18\x0f \x03(\t\x12\x11\n\tuse_count\x18\x10 \x01(\x05\x12\x16\n\x0e\x61verage_rating\x18\x11 \x01(\x02\x12\x12\n\nvisibility\x18\x12 \x01(\t\x12\x15\n\x08is_added\x18\x13 \x01(\x08H\x00\x88\x01\x01\x12\x17\n\x0fis_customizable\x18\x14 \x01(\x08\x12\x1a\n\x12source_workflow_id\x18\x15 \x01(\tB\x0b\n\t_is_added\"B\n\x12GetTemplateRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_user_id\"e\n\x13GetTemplateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x08template\x18\x03 \x01(\x0b\x32\x1a.workflow.WorkflowTemplate\"\x89\x01\n!CreateWorkflowFromTemplateRequest\x12\x13\n\x0btemplate_id\x18\x01 \x01(\t\x12\x1e\n\x05owner\x18\x02 \x01(\x0b\x32\x0f.workflow.Owner\x12/\n\nowner_type\x18\x03 \x01(\x0e\x32\x1b.workflow.WorkflowOwnerType\"F\n\"CreateWorkflowFromTemplateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\'\n\x18GetWorkflowsByIdsRequest\x12\x0b\n\x03ids\x18\x01 \x03(\t\"s\n\x19GetWorkflowsByIdsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\tworkflows\x18\x03 \x03(\x0b\x32\x12.workflow.Workflow\x12\r\n\x05total\x18\x04 \x01(\x05\"V\n\x1fToggleWorkflowVisibilityRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x1e\n\x05owner\x18\x02 \x01(\x0b\x32\x0f.workflow.Owner\"j\n ToggleWorkflowVisibilityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12$\n\x08workflow\x18\x03 \x01(\x0b\x32\x12.workflow.Workflow\"\xd6\x01\n\x1dUpdateWorkflowSettingsRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x1e\n\x05owner\x18\x02 \x01(\x0b\x32\x0f.workflow.Owner\x12\x17\n\nis_updated\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12-\n\x06status\x18\x04 \x01(\x0e\x32\x18.workflow.WorkflowStatusH\x01\x88\x01\x01\x12\x1e\n\x16is_changes_marketplace\x18\x05 \x01(\x08\x42\r\n\x0b_is_updatedB\t\n\x07_status\"B\n\x1eUpdateWorkflowSettingsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x91\x01\n\x1cListTemplatesByUserIdRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x31\n\x08\x63\x61tegory\x18\x04 \x01(\x0e\x32\x1a.workflow.WorkflowCategoryH\x00\x88\x01\x01\x42\x0b\n\t_category\"\xb9\x03\n\x13MarketplaceWorkflow\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x11\n\timage_url\x18\x04 \x01(\t\x12\x14\n\x0cworkflow_url\x18\x05 \x01(\t\x12\x13\n\x0b\x62uilder_url\x18\x06 \x01(\t\x12\x13\n\x0bstart_nodes\x18\x07 \x03(\t\x12\x17\n\x0f\x61vailable_nodes\x18\x08 \x03(\t\x12\x10\n\x08owner_id\x18\t \x01(\t\x12\x11\n\tuse_count\x18\n \x01(\x05\x12\x17\n\x0f\x65xecution_count\x18\x0b \x01(\x05\x12\x16\n\x0e\x61verage_rating\x18\x0c \x01(\x02\x12\x10\n\x08\x63\x61tegory\x18\r \x01(\t\x12\x0c\n\x04tags\x18\x0e \x03(\t\x12\x0f\n\x07version\x18\x0f \x01(\t\x12\x0e\n\x06status\x18\x10 \x01(\t\x12\x12\n\nvisibility\x18\x11 \x01(\t\x12\x12\n\ncreated_at\x18\x12 \x01(\t\x12\x12\n\nupdated_at\x18\x13 \x01(\t\x12\x1b\n\x13workflow_definition\x18\x14 \x01(\t\x12\x17\n\x0fis_customizable\x18\x15 \x01(\x08\"\xf9\x01\n\x1eGetMarketplaceWorkflowsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x13\n\x06search\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x31\n\x08\x63\x61tegory\x18\x04 \x01(\x0e\x32\x1a.workflow.WorkflowCategoryH\x01\x88\x01\x01\x12\x0c\n\x04tags\x18\x05 \x03(\t\x12\x14\n\x07sort_by\x18\x06 \x01(\tH\x02\x88\x01\x01\x12\x17\n\nvisibility\x18\x07 \x01(\tH\x03\x88\x01\x01\x42\t\n\x07_searchB\x0b\n\t_categoryB\n\n\x08_sort_byB\r\n\x0b_visibility\"\xaa\x02\n\x1fGetMarketplaceWorkflowsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x30\n\tworkflows\x18\x03 \x03(\x0b\x32\x1d.workflow.MarketplaceWorkflow\x12\r\n\x05total\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x11\n\tpage_size\x18\x06 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x07 \x01(\x05\x12\x10\n\x08has_next\x18\x08 \x01(\x08\x12\x10\n\x08has_prev\x18\t \x01(\x08\x12\x16\n\tnext_page\x18\n \x01(\x05H\x00\x88\x01\x01\x12\x16\n\tprev_page\x18\x0b \x01(\x05H\x01\x88\x01\x01\x42\x0c\n\n_next_pageB\x0c\n\n_prev_page\"1\n#GetMarketplaceWorkflowDetailRequest\x12\n\n\x02id\x18\x01 \x01(\t\"y\n$GetMarketplaceWorkflowDetailResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12/\n\x08workflow\x18\x03 \x01(\x0b\x32\x1d.workflow.MarketplaceWorkflow\"2\n\x19\x44iscoverComponentsRequest\x12\x15\n\rforce_refresh\x18\x01 \x01(\x08\"K\n\x0eVisibilityRule\x12\x12\n\nfield_name\x18\x01 \x01(\t\x12\x13\n\x0b\x66ield_value\x18\x02 \x01(\t\x12\x10\n\x08operator\x18\x03 \x01(\t\"L\n\x0fRequirementRule\x12\x12\n\nfield_name\x18\x01 \x01(\t\x12\x13\n\x0b\x66ield_value\x18\x02 \x01(\t\x12\x10\n\x08operator\x18\x03 \x01(\t\"\xd8\x03\n\x0e\x43omponentInput\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x02 \x01(\t\x12\x0c\n\x04info\x18\x03 \x01(\t\x12\x12\n\ninput_type\x18\x04 \x01(\t\x12\x13\n\x0binput_types\x18\x05 \x03(\t\x12\x10\n\x08required\x18\x06 \x01(\x08\x12\x11\n\tis_handle\x18\x07 \x01(\x08\x12\x0f\n\x07is_list\x18\x08 \x01(\x08\x12\x19\n\x11real_time_refresh\x18\t \x01(\x08\x12\x10\n\x08\x61\x64vanced\x18\n \x01(\x08\x12\r\n\x05value\x18\x0b \x01(\t\x12\x0f\n\x07options\x18\x0c \x03(\t\x12\x32\n\x10visibility_rules\x18\r \x03(\x0b\x32\x18.workflow.VisibilityRule\x12\x18\n\x10visibility_logic\x18\x0e \x01(\t\x12\x34\n\x11requirement_rules\x18\x0f \x03(\x0b\x32\x19.workflow.RequirementRule\x12\x19\n\x11requirement_logic\x18\x10 \x01(\t\x12\x17\n\x0f\x63redential_type\x18\x11 \x01(\t\x12\x19\n\x11use_credential_id\x18\x12 \x01(\x08\x12\x15\n\rcredential_id\x18\x13 \x01(\t\"q\n\x0f\x43omponentOutput\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x02 \x01(\t\x12\x13\n\x0boutput_type\x18\x03 \x01(\t\x12\x15\n\rsemantic_type\x18\x04 \x01(\t\x12\x0e\n\x06method\x18\x05 \x01(\t\"g\n\x07MCPInfo\x12\x11\n\tserver_id\x18\x01 \x01(\t\x12\x13\n\x0bserver_path\x18\x02 \x01(\t\x12\x11\n\ttool_name\x18\x03 \x01(\t\x12\x0f\n\x07tool_id\x18\x04 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x05 \x01(\t\"\xc2\x02\n\tComponent\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x05 \x01(\t\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\x12(\n\x06inputs\x18\x08 \x03(\x0b\x32\x18.workflow.ComponentInput\x12*\n\x07outputs\x18\t \x03(\x0b\x32\x19.workflow.ComponentOutput\x12\x10\n\x08is_valid\x18\n \x01(\x08\x12\x0c\n\x04\x62\x65ta\x18\x0b \x01(\x08\x12\x19\n\x11requires_approval\x18\x0c \x01(\x08\x12\x0c\n\x04path\x18\r \x01(\t\x12#\n\x08mcp_info\x18\x0e \x01(\x0b\x32\x11.workflow.MCPInfo\"E\n\x1a\x44iscoverComponentsResponse\x12\'\n\ncomponents\x18\x01 \x03(\x0b\x32\x13.workflow.Component\"\x99\x01\n\x0bRequestNode\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12-\n\x04\x64\x61ta\x18\x03 \x03(\x0b\x32\x1f.workflow.RequestNode.DataEntry\x12\t\n\x01x\x18\x04 \x01(\x01\x12\t\n\x01y\x18\x05 \x01(\x01\x1a+\n\tDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"e\n\x0bRequestEdge\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0e\n\x06target\x18\x03 \x01(\t\x12\x14\n\x0csourceHandle\x18\x04 \x01(\t\x12\x14\n\x0ctargetHandle\x18\x05 \x01(\t\"\xd2\x01\n\x17ValidateWorkflowRequest\x12$\n\x05nodes\x18\x01 \x03(\x0b\x32\x15.workflow.RequestNode\x12$\n\x05\x65\x64ges\x18\x02 \x03(\x0b\x32\x15.workflow.RequestEdge\x12\x15\n\rworkflow_name\x18\x03 \x01(\t\x12\x18\n\x0bworkflow_id\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x0c\x65xecution_id\x18\x05 \x01(\tH\x01\x88\x01\x01\x42\x0e\n\x0c_workflow_idB\x0f\n\r_execution_id\"v\n\x0cMissingField\x12\x0f\n\x07node_id\x18\x01 \x01(\t\x12\x12\n\nnode_label\x18\x02 \x01(\t\x12\x12\n\ninput_name\x18\x03 \x01(\t\x12\x1a\n\x12input_display_name\x18\x04 \x01(\t\x12\x11\n\tis_handle\x18\x05 \x01(\x08\"\x9c\x01\n\x18ValidateWorkflowResponse\x12\x10\n\x08is_valid\x18\x01 \x01(\x08\x12.\n\x0emissing_fields\x18\x02 \x03(\x0b\x32\x16.workflow.MissingField\x12\x0e\n\x06\x65rrors\x18\x03 \x03(\t\x12\x10\n\x08warnings\x18\x04 \x03(\t\x12\x12\n\x05\x65rror\x18\x05 \x01(\tH\x00\x88\x01\x01\x42\x08\n\x06_error\"K\n\x13RateWorkflowRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x0e\n\x06rating\x18\x03 \x01(\x02\"P\n\x14RateWorkflowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x16\n\x0e\x61verage_rating\x18\x03 \x01(\x02\":\n\x12UseWorkflowRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"J\n\x13UseWorkflowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\tuse_count\x18\x03 \x01(\x05\"\xca\x02\n\x0fWorkflowVersion\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0bworkflow_id\x18\x02 \x01(\t\x12\x16\n\x0eversion_number\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\x14\n\x0cworkflow_url\x18\x06 \x01(\t\x12\x13\n\x0b\x62uilder_url\x18\x07 \x01(\t\x12\x13\n\x0bstart_nodes\x18\x08 \x03(\t\x12\x17\n\x0f\x61vailable_nodes\x18\t \x03(\t\x12\x10\n\x08\x63\x61tegory\x18\n \x01(\t\x12\x0c\n\x04tags\x18\x0b \x03(\t\x12\x11\n\tchangelog\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x17\n\x0fis_customizable\x18\x0e \x01(\x08\x12\x12\n\ncreated_at\x18\x0f \x01(\t\x12\x12\n\nis_current\x18\x10 \x01(\x08\"\x85\x01\n\x1bListWorkflowVersionsRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x11\n\x04page\x18\x03 \x01(\x05H\x00\x88\x01\x01\x12\x16\n\tpage_size\x18\x04 \x01(\x05H\x01\x88\x01\x01\x42\x07\n\x05_pageB\x0c\n\n_page_size\"\xbb\x01\n\x1cListWorkflowVersionsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x08versions\x18\x03 \x03(\x0b\x32\x19.workflow.WorkflowVersion\x12\r\n\x05total\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x06 \x01(\x05\x12\x1a\n\x12\x63urrent_version_id\x18\x07 \x01(\t\"U\n\x19GetWorkflowVersionRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x12\n\nversion_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\"j\n\x1aGetWorkflowVersionResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x07version\x18\x03 \x01(\x0b\x32\x19.workflow.WorkflowVersion\"X\n\x1cSwitchWorkflowVersionRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x12\n\nversion_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\"y\n\x1dSwitchWorkflowVersionResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x36\n\x13new_current_version\x18\x03 \x01(\x0b\x32\x19.workflow.WorkflowVersion\"D\n\x1cPullUpdatesFromSourceRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"o\n\x1dPullUpdatesFromSourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x10updated_workflow\x18\x03 \x01(\x0b\x32\x12.workflow.Workflow\">\n\x16\x43heckForUpdatesRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"\xee\x01\n\x17\x43heckForUpdatesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x13\n\x0bhas_updates\x18\x03 \x01(\x08\x12\x1f\n\x12source_workflow_id\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x0clast_updated\x18\x05 \x01(\tH\x01\x88\x01\x01\x12 \n\x13source_last_updated\x18\x06 \x01(\tH\x02\x88\x01\x01\x42\x15\n\x13_source_workflow_idB\x0f\n\r_last_updatedB\x16\n\x14_source_last_updated\"f\n\x1e\x43reateVersionAndPublishRequest\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x1e\n\x16publish_to_marketplace\x18\x03 \x01(\x08\"\x91\x02\n\x1f\x43reateVersionAndPublishResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x17\n\x0fversion_created\x18\x03 \x01(\x08\x12\x1b\n\x13marketplace_updated\x18\x04 \x01(\x08\x12\x1b\n\x0eversion_number\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\x17\n\nversion_id\x18\x06 \x01(\tH\x01\x88\x01\x01\x12#\n\x16marketplace_listing_id\x18\x07 \x01(\tH\x02\x88\x01\x01\x42\x11\n\x0f_version_numberB\r\n\x0b_version_idB\x19\n\x17_marketplace_listing_id*;\n\x11WorkflowOwnerType\x12\x08\n\x04USER\x10\x00\x12\x0e\n\nENTERPRISE\x10\x01\x12\x0c\n\x08PLATFORM\x10\x02*-\n\x12WorkflowVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*5\n\x0eWorkflowStatus\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\x0c\n\x08INACTIVE\x10\x01\x12\t\n\x05\x44RAFT\x10\x02*\x94\x02\n\x10WorkflowCategory\x12\x0e\n\nAUTOMATION\x10\x00\x12\x11\n\rDATA_PIPELINE\x10\x01\x12\x0f\n\x0bINTEGRATION\x10\x02\x12\x10\n\x0cWEB_SCRAPING\x10\x03\x12\x07\n\x03\x41PI\x10\x04\x12\t\n\x05\x45MAIL\x10\x05\x12\x15\n\x11LLM_ORCHESTRATION\x10\x06\x12\x0c\n\x08\x44\x41TABASE\x10\x07\x12\x13\n\x0f\x46ILE_MANAGEMENT\x10\x08\x12\x0e\n\nSCHEDULING\x10\t\x12\x0e\n\nMONITORING\x10\n\x12\x07\n\x03\x43RM\x10\x0b\x12\x11\n\rNOTIFICATIONS\x10\x0c\x12\x17\n\x13\x44OCUMENT_PROCESSING\x10\r\x12\n\n\x06\x44\x45VOPS\x10\x0e\x12\x0b\n\x07GENERAL\x10\x0f*V\n\x17MarketplaceItemSortEnum\x12\n\n\x06NEWEST\x10\x00\x12\n\n\x06OLDEST\x10\x01\x12\x10\n\x0cMOST_POPULAR\x10\x02\x12\x11\n\rHIGHEST_RATED\x10\x03\x32\xba\x11\n\x0fWorkflowService\x12S\n\x0e\x63reateWorkflow\x12\x1f.workflow.CreateWorkflowRequest\x1a .workflow.CreateWorkflowResponse\x12G\n\x0bgetWorkflow\x12\x1c.workflow.GetWorkflowRequest\x1a\x1a.workflow.WorkflowResponse\x12S\n\x0eupdateWorkflow\x12\x1f.workflow.UpdateWorkflowRequest\x1a .workflow.UpdateWorkflowResponse\x12S\n\x0e\x64\x65leteWorkflow\x12\x1f.workflow.DeleteWorkflowRequest\x1a .workflow.DeleteWorkflowResponse\x12P\n\rlistWorkflows\x12\x1e.workflow.ListWorkflowsRequest\x1a\x1f.workflow.ListWorkflowsResponse\x12`\n\x15listWorkflowsByUserId\x12&.workflow.ListWorkflowsByUserIdRequest\x1a\x1f.workflow.ListWorkflowsResponse\x12w\n\x1a\x63reateWorkflowFromTemplate\x12+.workflow.CreateWorkflowFromTemplateRequest\x1a,.workflow.CreateWorkflowFromTemplateResponse\x12J\n\x0bgetTemplate\x12\x1c.workflow.GetTemplateRequest\x1a\x1d.workflow.GetTemplateResponse\x12\\\n\x11getWorkflowsByIds\x12\".workflow.GetWorkflowsByIdsRequest\x1a#.workflow.GetWorkflowsByIdsResponse\x12q\n\x18toggleWorkflowVisibility\x12).workflow.ToggleWorkflowVisibilityRequest\x1a*.workflow.ToggleWorkflowVisibilityResponse\x12k\n\x16updateWorkflowSettings\x12\'.workflow.UpdateWorkflowSettingsRequest\x1a(.workflow.UpdateWorkflowSettingsResponse\x12_\n\x12\x64iscoverComponents\x12#.workflow.DiscoverComponentsRequest\x1a$.workflow.DiscoverComponentsResponse\x12Y\n\x10validateWorkflow\x12!.workflow.ValidateWorkflowRequest\x1a\".workflow.ValidateWorkflowResponse\x12n\n\x17getMarketplaceWorkflows\x12(.workflow.GetMarketplaceWorkflowsRequest\x1a).workflow.GetMarketplaceWorkflowsResponse\x12}\n\x1cgetMarketplaceWorkflowDetail\x12-.workflow.GetMarketplaceWorkflowDetailRequest\x1a..workflow.GetMarketplaceWorkflowDetailResponse\x12M\n\x0crateWorkflow\x12\x1d.workflow.RateWorkflowRequest\x1a\x1e.workflow.RateWorkflowResponse\x12J\n\x0buseWorkflow\x12\x1c.workflow.UseWorkflowRequest\x1a\x1d.workflow.UseWorkflowResponse\x12\x65\n\x14listWorkflowVersions\x12%.workflow.ListWorkflowVersionsRequest\x1a&.workflow.ListWorkflowVersionsResponse\x12_\n\x12getWorkflowVersion\x12#.workflow.GetWorkflowVersionRequest\x1a$.workflow.GetWorkflowVersionResponse\x12h\n\x15switchWorkflowVersion\x12&.workflow.SwitchWorkflowVersionRequest\x1a\'.workflow.SwitchWorkflowVersionResponse\x12h\n\x15pullUpdatesFromSource\x12&.workflow.PullUpdatesFromSourceRequest\x1a\'.workflow.PullUpdatesFromSourceResponse\x12V\n\x0f\x63heckForUpdates\x12 .workflow.CheckForUpdatesRequest\x1a!.workflow.CheckForUpdatesResponse\x12n\n\x17\x63reateVersionAndPublish\x12(.workflow.CreateVersionAndPublishRequest\x1a).workflow.CreateVersionAndPublishResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'workflow_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_REQUESTNODE_DATAENTRY']._loaded_options = None
  _globals['_REQUESTNODE_DATAENTRY']._serialized_options = b'8\001'
  _globals['_WORKFLOWOWNERTYPE']._serialized_start=9831
  _globals['_WORKFLOWOWNERTYPE']._serialized_end=9890
  _globals['_WORKFLOWVISIBILITY']._serialized_start=9892
  _globals['_WORKFLOWVISIBILITY']._serialized_end=9937
  _globals['_WORKFLOWSTATUS']._serialized_start=9939
  _globals['_WORKFLOWSTATUS']._serialized_end=9992
  _globals['_WORKFLOWCATEGORY']._serialized_start=9995
  _globals['_WORKFLOWCATEGORY']._serialized_end=10271
  _globals['_MARKETPLACEITEMSORTENUM']._serialized_start=10273
  _globals['_MARKETPLACEITEMSORTENUM']._serialized_end=10359
  _globals['_OWNER']._serialized_start=62
  _globals['_OWNER']._serialized_end=81
  _globals['_CREATEWORKFLOWREQUEST']._serialized_start=84
  _globals['_CREATEWORKFLOWREQUEST']._serialized_end=437
  _globals['_UPDATEWORKFLOWREQUEST']._serialized_start=440
  _globals['_UPDATEWORKFLOWREQUEST']._serialized_end=944
  _globals['_GETWORKFLOWREQUEST']._serialized_start=946
  _globals['_GETWORKFLOWREQUEST']._serialized_end=1012
  _globals['_DELETEWORKFLOWREQUEST']._serialized_start=1014
  _globals['_DELETEWORKFLOWREQUEST']._serialized_end=1081
  _globals['_LISTWORKFLOWSREQUEST']._serialized_start=1084
  _globals['_LISTWORKFLOWSREQUEST']._serialized_end=1423
  _globals['_LISTWORKFLOWSBYUSERIDREQUEST']._serialized_start=1426
  _globals['_LISTWORKFLOWSBYUSERIDREQUEST']._serialized_end=1699
  _globals['_WORKFLOW']._serialized_start=1702
  _globals['_WORKFLOW']._serialized_end=2292
  _globals['_CREATEWORKFLOWRESPONSE']._serialized_start=2294
  _globals['_CREATEWORKFLOWRESPONSE']._serialized_end=2390
  _globals['_UPDATEWORKFLOWRESPONSE']._serialized_start=2392
  _globals['_UPDATEWORKFLOWRESPONSE']._serialized_end=2450
  _globals['_DELETEWORKFLOWRESPONSE']._serialized_start=2452
  _globals['_DELETEWORKFLOWRESPONSE']._serialized_end=2510
  _globals['_WORKFLOWRESPONSE']._serialized_start=2512
  _globals['_WORKFLOWRESPONSE']._serialized_end=2620
  _globals['_LISTWORKFLOWSRESPONSE']._serialized_start=2623
  _globals['_LISTWORKFLOWSRESPONSE']._serialized_end=2752
  _globals['_WORKFLOWTEMPLATE']._serialized_start=2755
  _globals['_WORKFLOWTEMPLATE']._serialized_end=3209
  _globals['_GETTEMPLATEREQUEST']._serialized_start=3211
  _globals['_GETTEMPLATEREQUEST']._serialized_end=3277
  _globals['_GETTEMPLATERESPONSE']._serialized_start=3279
  _globals['_GETTEMPLATERESPONSE']._serialized_end=3380
  _globals['_CREATEWORKFLOWFROMTEMPLATEREQUEST']._serialized_start=3383
  _globals['_CREATEWORKFLOWFROMTEMPLATEREQUEST']._serialized_end=3520
  _globals['_CREATEWORKFLOWFROMTEMPLATERESPONSE']._serialized_start=3522
  _globals['_CREATEWORKFLOWFROMTEMPLATERESPONSE']._serialized_end=3592
  _globals['_GETWORKFLOWSBYIDSREQUEST']._serialized_start=3594
  _globals['_GETWORKFLOWSBYIDSREQUEST']._serialized_end=3633
  _globals['_GETWORKFLOWSBYIDSRESPONSE']._serialized_start=3635
  _globals['_GETWORKFLOWSBYIDSRESPONSE']._serialized_end=3750
  _globals['_TOGGLEWORKFLOWVISIBILITYREQUEST']._serialized_start=3752
  _globals['_TOGGLEWORKFLOWVISIBILITYREQUEST']._serialized_end=3838
  _globals['_TOGGLEWORKFLOWVISIBILITYRESPONSE']._serialized_start=3840
  _globals['_TOGGLEWORKFLOWVISIBILITYRESPONSE']._serialized_end=3946
  _globals['_UPDATEWORKFLOWSETTINGSREQUEST']._serialized_start=3949
  _globals['_UPDATEWORKFLOWSETTINGSREQUEST']._serialized_end=4163
  _globals['_UPDATEWORKFLOWSETTINGSRESPONSE']._serialized_start=4165
  _globals['_UPDATEWORKFLOWSETTINGSRESPONSE']._serialized_end=4231
  _globals['_LISTTEMPLATESBYUSERIDREQUEST']._serialized_start=4234
  _globals['_LISTTEMPLATESBYUSERIDREQUEST']._serialized_end=4379
  _globals['_MARKETPLACEWORKFLOW']._serialized_start=4382
  _globals['_MARKETPLACEWORKFLOW']._serialized_end=4823
  _globals['_GETMARKETPLACEWORKFLOWSREQUEST']._serialized_start=4826
  _globals['_GETMARKETPLACEWORKFLOWSREQUEST']._serialized_end=5075
  _globals['_GETMARKETPLACEWORKFLOWSRESPONSE']._serialized_start=5078
  _globals['_GETMARKETPLACEWORKFLOWSRESPONSE']._serialized_end=5376
  _globals['_GETMARKETPLACEWORKFLOWDETAILREQUEST']._serialized_start=5378
  _globals['_GETMARKETPLACEWORKFLOWDETAILREQUEST']._serialized_end=5427
  _globals['_GETMARKETPLACEWORKFLOWDETAILRESPONSE']._serialized_start=5429
  _globals['_GETMARKETPLACEWORKFLOWDETAILRESPONSE']._serialized_end=5550
  _globals['_DISCOVERCOMPONENTSREQUEST']._serialized_start=5552
  _globals['_DISCOVERCOMPONENTSREQUEST']._serialized_end=5602
  _globals['_VISIBILITYRULE']._serialized_start=5604
  _globals['_VISIBILITYRULE']._serialized_end=5679
  _globals['_REQUIREMENTRULE']._serialized_start=5681
  _globals['_REQUIREMENTRULE']._serialized_end=5757
  _globals['_COMPONENTINPUT']._serialized_start=5760
  _globals['_COMPONENTINPUT']._serialized_end=6232
  _globals['_COMPONENTOUTPUT']._serialized_start=6234
  _globals['_COMPONENTOUTPUT']._serialized_end=6347
  _globals['_MCPINFO']._serialized_start=6349
  _globals['_MCPINFO']._serialized_end=6452
  _globals['_COMPONENT']._serialized_start=6455
  _globals['_COMPONENT']._serialized_end=6777
  _globals['_DISCOVERCOMPONENTSRESPONSE']._serialized_start=6779
  _globals['_DISCOVERCOMPONENTSRESPONSE']._serialized_end=6848
  _globals['_REQUESTNODE']._serialized_start=6851
  _globals['_REQUESTNODE']._serialized_end=7004
  _globals['_REQUESTNODE_DATAENTRY']._serialized_start=6961
  _globals['_REQUESTNODE_DATAENTRY']._serialized_end=7004
  _globals['_REQUESTEDGE']._serialized_start=7006
  _globals['_REQUESTEDGE']._serialized_end=7107
  _globals['_VALIDATEWORKFLOWREQUEST']._serialized_start=7110
  _globals['_VALIDATEWORKFLOWREQUEST']._serialized_end=7320
  _globals['_MISSINGFIELD']._serialized_start=7322
  _globals['_MISSINGFIELD']._serialized_end=7440
  _globals['_VALIDATEWORKFLOWRESPONSE']._serialized_start=7443
  _globals['_VALIDATEWORKFLOWRESPONSE']._serialized_end=7599
  _globals['_RATEWORKFLOWREQUEST']._serialized_start=7601
  _globals['_RATEWORKFLOWREQUEST']._serialized_end=7676
  _globals['_RATEWORKFLOWRESPONSE']._serialized_start=7678
  _globals['_RATEWORKFLOWRESPONSE']._serialized_end=7758
  _globals['_USEWORKFLOWREQUEST']._serialized_start=7760
  _globals['_USEWORKFLOWREQUEST']._serialized_end=7818
  _globals['_USEWORKFLOWRESPONSE']._serialized_start=7820
  _globals['_USEWORKFLOWRESPONSE']._serialized_end=7894
  _globals['_WORKFLOWVERSION']._serialized_start=7897
  _globals['_WORKFLOWVERSION']._serialized_end=8227
  _globals['_LISTWORKFLOWVERSIONSREQUEST']._serialized_start=8230
  _globals['_LISTWORKFLOWVERSIONSREQUEST']._serialized_end=8363
  _globals['_LISTWORKFLOWVERSIONSRESPONSE']._serialized_start=8366
  _globals['_LISTWORKFLOWVERSIONSRESPONSE']._serialized_end=8553
  _globals['_GETWORKFLOWVERSIONREQUEST']._serialized_start=8555
  _globals['_GETWORKFLOWVERSIONREQUEST']._serialized_end=8640
  _globals['_GETWORKFLOWVERSIONRESPONSE']._serialized_start=8642
  _globals['_GETWORKFLOWVERSIONRESPONSE']._serialized_end=8748
  _globals['_SWITCHWORKFLOWVERSIONREQUEST']._serialized_start=8750
  _globals['_SWITCHWORKFLOWVERSIONREQUEST']._serialized_end=8838
  _globals['_SWITCHWORKFLOWVERSIONRESPONSE']._serialized_start=8840
  _globals['_SWITCHWORKFLOWVERSIONRESPONSE']._serialized_end=8961
  _globals['_PULLUPDATESFROMSOURCEREQUEST']._serialized_start=8963
  _globals['_PULLUPDATESFROMSOURCEREQUEST']._serialized_end=9031
  _globals['_PULLUPDATESFROMSOURCERESPONSE']._serialized_start=9033
  _globals['_PULLUPDATESFROMSOURCERESPONSE']._serialized_end=9144
  _globals['_CHECKFORUPDATESREQUEST']._serialized_start=9146
  _globals['_CHECKFORUPDATESREQUEST']._serialized_end=9208
  _globals['_CHECKFORUPDATESRESPONSE']._serialized_start=9211
  _globals['_CHECKFORUPDATESRESPONSE']._serialized_end=9449
  _globals['_CREATEVERSIONANDPUBLISHREQUEST']._serialized_start=9451
  _globals['_CREATEVERSIONANDPUBLISHREQUEST']._serialized_end=9553
  _globals['_CREATEVERSIONANDPUBLISHRESPONSE']._serialized_start=9556
  _globals['_CREATEVERSIONANDPUBLISHRESPONSE']._serialized_end=9829
  _globals['_WORKFLOWSERVICE']._serialized_start=10362
  _globals['_WORKFLOWSERVICE']._serialized_end=12596
# @@protoc_insertion_point(module_scope)
