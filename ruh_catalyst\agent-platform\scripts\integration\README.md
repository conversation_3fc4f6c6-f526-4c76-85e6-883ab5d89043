# Kafka Integration Test Scripts

This directory contains comprehensive test scripts for testing the Agent Platform's Kafka integration functionality.

## Overview

The Agent Platform supports five main Kafka topics for different types of agent interactions:

1. **Agent Creation** - Create new agent sessions
2. **Agent Chat** - Interactive chat with existing agent sessions
3. **Agent Query** - Direct queries to agents without session management
4. **Agent Message** - Direct messages to agents with custom configurations
5. **Session Deletion** - Delete agent sessions when chat is complete

## Test Scripts

### 1. `run_kafka_test.py`
Original test script for agent creation and chat functionality.

**Usage:**
```bash
python scripts/integration/run_kafka_test.py
```

**Features:**
- Creates an agent session
- Interactive chat interface
- Tests agent creation and chat topics

### 2. `run_agent_query_test.py`
Test script specifically for agent query functionality.

**Usage:**
```bash
python scripts/integration/run_agent_query_test.py
```

**Features:**
- Single query test mode
- Interactive query session mode
- Tests direct agent queries without session overhead

**Options:**
1. Single query test - Send one query and get response
2. Interactive query session - Continuous query interface

### 3. `run_agent_message_test.py`
Test script for agent message functionality with custom configurations.

**Usage:**
```bash
python scripts/integration/run_agent_message_test.py
```

**Features:**
- Single message test with default agent config
- Interactive message session
- Custom agent configuration test (Math Tutor example)

**Options:**
1. Single message test - Basic test with default agent
2. Interactive message session - Continuous messaging
3. Custom agent configuration test - Specialized agent example

### 5. `run_session_deletion_test.py`
Test script for session deletion functionality.

**Usage:**
```bash
python scripts/integration/run_session_deletion_test.py
```

**Features:**
- Create and delete session test
- Force deletion test for non-existent sessions
- Expected failure test (deletion without force)
- Interactive session deletion interface

**Options:**
1. Comprehensive test suite - Automated testing of all deletion scenarios
2. Interactive deletion interface - Manual session deletion testing

### 6. `run_comprehensive_kafka_test.py`
Complete test suite that tests all Kafka functionality.

**Usage:**
```bash
python scripts/integration/run_comprehensive_kafka_test.py
```

**Features:**
- Tests all five Kafka topics
- Automated test execution
- Comprehensive results summary
- Pass/fail reporting for each component

## Environment Variables

Make sure these environment variables are set:

```bash
# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Topic Names
KAFKA_AGENT_CREATION_TOPIC=agent_creation_requests
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_QUERY_TOPIC=agent_query_requests
KAFKA_AGENT_MESSAGE_TOPIC=agent_message_requests
KAFKA_AGENT_SESSION_DELETION_TOPIC=agent_session_deletion_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
```

## Prerequisites

1. **Kafka Server Running**
   ```bash
   # Start Kafka (example with Docker)
   docker run -d --name kafka \
     -p 9092:9092 \
     -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
     -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
     confluentinc/cp-kafka:latest
   ```

2. **Agent Platform Running**
   ```bash
   # Start the agent platform in engine mode
   python -m app.main --mode engine
   ```

3. **Python Dependencies**
   ```bash
   pip install aiokafka
   ```

## Test Scenarios

### Agent Creation + Chat Flow
```bash
python scripts/integration/run_kafka_test.py
```
1. Creates an agent session using a predefined agent ID
2. Returns a session ID for subsequent interactions
3. Allows interactive chat with the created agent

### Direct Agent Query
```bash
python scripts/integration/run_agent_query_test.py
```
1. Sends direct queries to agents without creating persistent sessions
2. Useful for one-off questions or stateless interactions
3. Automatically creates and cleans up temporary sessions

### Custom Agent Messages
```bash
python scripts/integration/run_agent_message_test.py
```
1. Sends messages to agents with custom configurations
2. No need for predefined agent IDs
3. Allows testing different agent personalities and capabilities

### Full Integration Test
```bash
python scripts/integration/run_comprehensive_kafka_test.py
```
1. Tests all four Kafka topics sequentially
2. Provides pass/fail status for each component
3. Useful for CI/CD validation

## Message Schemas

### Agent Creation Request
```json
{
  "agent_id": "uuid-string",
  "user_id": "string",
  "communication_type": "single|group",
  "run_id": "uuid-string",
  "organization_id": "string|null",
  "use_knowledge": "boolean",
  "agent_group_id": "string|null",
  "variables": "object|null"
}
```

### Agent Chat Request
```json
{
  "run_id": "uuid-string",
  "session_id": "uuid-string",
  "chat_context": [
    {"role": "user", "content": "string"}
  ]
}
```

### Agent Query Request
```json
{
  "agent_id": "uuid-string",
  "query": "string",
  "run_id": "uuid-string",
  "user_id": "string",
  "organization_id": "string|null",
  "variables": "object"
}
```

### Agent Message Request
```json
{
  "agent_config": {
    "id": "string",
    "name": "string",
    "description": "string",
    "system_message": "string",
    "model_config": {
      "model": "string",
      "temperature": "number",
      "max_tokens": "number"
    },
    "tools": "array",
    "capabilities": "array"
  },
  "run_id": "uuid-string",
  "query": "string",
  "user_id": "string",
  "variables": "object|null",
  "organization_id": "string|null",
  "use_knowledge": "boolean"
}
```

### Agent Session Deletion Request
```json
{
  "session_id": "uuid-string",
  "run_id": "uuid-string",
  "user_id": "string",
  "reason": "string",
  "force": "boolean"
}
```

### Agent Session Deletion Response
```json
{
  "run_id": "uuid-string",
  "session_id": "uuid-string",
  "success": "boolean",
  "message": "string",
  "deleted_at": "string|null"
}
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure Kafka is running on the specified port
   - Check `KAFKA_BOOTSTRAP_SERVERS` environment variable

2. **No Response Received**
   - Verify the Agent Platform is running in engine mode
   - Check that all required topics exist in Kafka
   - Ensure the agent ID exists in the system

3. **Timeout Errors**
   - Increase timeout values in test scripts
   - Check Agent Platform logs for processing errors
   - Verify model API keys are configured correctly

4. **Agent Not Found**
   - Use the correct agent ID: `d406f37f-5c8f-43ce-8835-a69a9a8a764a`
   - Check agent configuration in the database
   - Verify agent fetch service is working

### Debugging

Enable debug logging by modifying the logging level:

```python
logging.basicConfig(
    level=logging.DEBUG,  # Change from INFO to DEBUG
    format="%(asctime)s - %(levelname)s - %(message)s"
)
```

### Monitoring

Monitor Kafka topics:
```bash
# List topics
kafka-topics --bootstrap-server localhost:9092 --list

# Monitor messages
kafka-console-consumer --bootstrap-server localhost:9092 \
  --topic agent_chat_responses --from-beginning
```

## Integration with CI/CD

Use the comprehensive test script for automated testing:

```bash
# In your CI/CD pipeline
python scripts/integration/run_comprehensive_kafka_test.py
if [ $? -eq 0 ]; then
  echo "All Kafka tests passed"
else
  echo "Kafka tests failed"
  exit 1
fi