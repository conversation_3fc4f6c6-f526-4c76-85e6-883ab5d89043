"""
OAuth Database Models

This module defines the database models for OAuth credential storage
and management in the authentication service.
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Index, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class OAuthCredential(Base):
    """
    OAuth credential storage model.

    Stores references to OAuth tokens in Google Secret Manager
    along with metadata for credential management.
    """

    __tablename__ = "oauth_credentials"

    # Primary key
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # OAuth credential identifiers
    user_id = Column(String, nullable=False, index=True)
    tool_name = Column(String, nullable=False, index=True)
    provider = Column(String, nullable=False, default="google", index=True)

    # Composite key for unique lookups (includes provider)
    composite_key = Column(String, nullable=False, unique=True, index=True)

    # Secret Manager reference
    secret_reference = Column(String, nullable=False)

    # OAuth-specific metadata
    scopes = Column(Text, nullable=True)  # JSON string of granted scopes

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    last_used_at = Column(DateTime, default=func.now(), nullable=False)

    # Create indexes for faster lookups
    __table_args__ = (
        Index("idx_oauth_composite_key", "composite_key"),
        Index("idx_oauth_user_provider", "user_id", "provider"),
        Index("idx_oauth_provider_tool", "provider", "tool_name"),
        Index("idx_oauth_user_tool", "user_id", "tool_name"),
        Index("idx_oauth_last_used", "last_used_at"),
    )

    def __repr__(self) -> str:
        return f"<OAuthCredential {self.composite_key}>"

    @classmethod
    def generate_composite_key(cls, user_id: str, tool_name: str, provider: str = "google") -> str:
        """
        Generate composite key for OAuth credential lookup.

        Args:
            user_id: User identifier
            tool_name: Tool name
            provider: OAuth provider name (unused, kept for backward compatibility)

        Returns:
            Composite key string
        """
        return f"{user_id}_{tool_name}"

    def update_last_used(self) -> None:
        """Update the last_used_at timestamp."""
        self.last_used_at = datetime.now(timezone.utc)

    def is_expired(self, max_age_days: int = 90) -> bool:
        """
        Check if credential is considered expired based on last usage.

        Args:
            max_age_days: Maximum age in days before considering expired

        Returns:
            True if credential is expired
        """
        if not self.last_used_at:
            return True

        age_days = (datetime.now(timezone.utc) - self.last_used_at).days
        return age_days > max_age_days
