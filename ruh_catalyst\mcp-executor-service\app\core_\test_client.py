import asyncio
from contextlib import Abstract<PERSON>ync<PERSON>ontextManager, AsyncExitStack
from typing import Any, Dict, List, Optional, Tuple, Union, Literal
from datetime import timedelta
import logging

# Import from the installed mcp package
from anyio.streams.memory import MemoryObjectReceiveStream, MemoryObjectSendStream
import mcp.types
from mcp.types import CallToolResult, JSONRPCMessage, Tool as MCPTool
from mcp.client.sse import sse_client
from mcp.client.streamable_http import streamablehttp_client
from mcp.client.stdio import stdio_client, StdioServerParameters
from mcp.client.session import ClientSession
import httpx


# Base class for MCP servers
class MCPServer:
    async def connect(self):
        """Connect to the server."""
        raise NotImplementedError

    @property
    def name(self) -> str:
        """A readable name for the server."""
        raise NotImplementedError

    async def list_tools(self) -> List[MCPTool]:
        """List the tools available on the server."""
        raise NotImplementedError

    async def call_tool(
        self, tool_name: str, arguments: Optional[Dict[str, Any]] = None
    ) -> CallToolResult:
        """Invoke a tool on the server."""
        raise NotImplementedError

    async def cleanup(self):
        """Cleanup the server."""
        raise NotImplementedError


# Base class for MCP servers that use a ClientSession
class _MCPServerWithClientSession(MCPServer):
    """Base class for MCP servers that use a ClientSession to communicate with the server."""

    def __init__(self, cache_tools_list: bool):
        """
        Args:
            cache_tools_list: Whether to cache the tools list. If True, the tools list will be
            cached and only fetched from the server once. If False, the tools list will be
            fetched from the server on each call to list_tools(). You should set this to True
            if you know the server will not change its tools list, because it can drastically
            improve latency.
        """
        self.session: Optional[ClientSession] = None
        self.exit_stack: AsyncExitStack = AsyncExitStack()
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.cache_tools_list = cache_tools_list

        # The cache is always dirty at startup, so that we fetch tools at least once
        self._cache_dirty = True
        self._tools_list: Optional[List[MCPTool]] = None
        self.logger = logging.getLogger(__name__)

    def create_streams(
        self,
    ) -> AbstractAsyncContextManager[
        Tuple[
            MemoryObjectReceiveStream[JSONRPCMessage | Exception],
            MemoryObjectSendStream[JSONRPCMessage],
        ]
    ]:
        """Create the streams for the server."""
        raise NotImplementedError

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        await self.cleanup()

    def invalidate_tools_cache(self):
        """Invalidate the tools cache."""
        self._cache_dirty = True

    async def connect(self):
        """Connect to the server."""
        try:
            transport = await self.exit_stack.enter_async_context(self.create_streams())
            read, write = transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read, write)
            )
            await session.initialize()
            self.session = session
            self.logger.info(f"Connected to MCP server: {self.name}")
        except Exception as e:
            self.logger.error(f"Error initializing MCP server: {e}")
            await self.cleanup()
            raise

    async def list_tools(self) -> List[MCPTool]:
        """List the tools available on the server."""
        if not self.session:
            raise RuntimeError(
                "Server not initialized. Make sure you call connect() first."
            )

        # Return from cache if caching is enabled, we have tools, and the cache is not dirty
        if self.cache_tools_list and not self._cache_dirty and self._tools_list:
            return self._tools_list

        # Reset the cache dirty to False
        self._cache_dirty = False

        try:
            # Fetch the tools from the server
            result = await self.session.list_tools()
            self._tools_list = result.tools
            return self._tools_list
        except Exception as e:
            self.logger.error(f"Error listing tools: {e}")
            raise

    async def call_tool(
        self, tool_name: str, arguments: Optional[Dict[str, Any]] = None
    ) -> CallToolResult:
        """Invoke a tool on the server."""
        if not self.session:
            raise RuntimeError(
                "Server not initialized. Make sure you call connect() first."
            )

        arguments = arguments or {}
        try:
            return await self.session.call_tool(tool_name, arguments)
        except Exception as e:
            self.logger.error(f"Error calling tool {tool_name}: {e}")
            raise

    async def cleanup(self):
        """Cleanup the server."""
        async with self._cleanup_lock:
            try:
                await self.exit_stack.aclose()
                self.session = None
                self.logger.info(f"Cleaned up MCP server: {self.name}")
            except Exception as e:
                self.logger.error(f"Error cleaning up server: {e}")


# Define parameter types for clarity
MCPServerSseParams = Dict[str, Any]
MCPServerStdioParams = Dict[str, Any]
UniversalMCPParams = Dict[str, Any]

# Transport type literals
TransportType = Literal["sse", "streamable_http", "stdio"]


# SSE server implementation
class MCPServerSse(_MCPServerWithClientSession):
    """MCP server implementation that uses the HTTP with SSE transport."""

    def __init__(
        self,
        params: MCPServerSseParams,
        cache_tools_list: bool = False,
        name: Optional[str] = None,
    ):
        """Create a new MCP server based on the HTTP with SSE transport.

        Args:
            params: The params that configure the server including the URL, headers,
                   timeout, and SSE read timeout.
            cache_tools_list: Whether to cache the tools list.
            name: A readable name for the server.
        """
        super().__init__(cache_tools_list)
        self.params = params
        self._name = name or f"SSE Server at {self.params.get('url', 'unknown')}"

    def create_streams(
        self,
    ) -> AbstractAsyncContextManager[
        Tuple[
            MemoryObjectReceiveStream[JSONRPCMessage | Exception],
            MemoryObjectSendStream[JSONRPCMessage],
        ]
    ]:
        """Create the streams for the server."""
        return sse_client(
            url=self.params["url"],
            headers=self.params.get("headers"),
            timeout=self.params.get("timeout", 5),
            sse_read_timeout=self.params.get("sse_read_timeout", 60 * 5),
        )

    @property
    def name(self) -> str:
        """A readable name for the server."""
        return self._name


# Stdio server implementation
class MCPServerStdio(MCPServer):
    """An example (minimal) Stdio server implementation."""

    def __init__(
        self,
        params: MCPServerStdioParams,
        cache_tools_list: bool = False,
        name: Optional[str] = None,
    ):
        self.params = params
        self.cache_tools_list = cache_tools_list
        self._tools_cache: Optional[List[MCPTool]] = None
        self._name = name or f"Stdio Server: {self.params.get('command', 'unknown')}"
        self.connected = False
        self.logger = logging.getLogger(__name__)

    @property
    def name(self) -> str:
        return self._name

    async def connect(self):
        await asyncio.sleep(0.5)
        self.connected = True
        self.logger.info(f"Connected to MCP Stdio server: {self.name}")

    async def list_tools(self) -> List[MCPTool]:
        if self.cache_tools_list and self._tools_cache is not None:
            return self._tools_cache
        # For demonstration, return an empty list or similar static tools.
        tools: List[MCPTool] = []
        if self.cache_tools_list:
            self._tools_cache = tools
        return tools

    async def call_tool(
        self, tool_name: str, arguments: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        return {"content": [f"Called {tool_name} with args {arguments} via Stdio"]}

    async def cleanup(self):
        self.connected = False
        self.logger.info(f"Cleaned up MCP Stdio server: {self.name}")


# Universal MCP Server implementation
class UniversalMCPServer(_MCPServerWithClientSession):
    """Universal MCP server that can handle all transport types (SSE, Streamable HTTP, STDIO)."""

    def __init__(
        self,
        params: UniversalMCPParams,
        cache_tools_list: bool = False,
        name: Optional[str] = None,
        transport_type: Optional[TransportType] = None,
    ):
        """Create a universal MCP server that auto-detects transport type.

        Args:
            params: Parameters for the server (flexible structure supporting all transport types)
            cache_tools_list: Whether to cache the tools list
            name: A readable name for the server
            transport_type: Manual transport type override (auto-detected if None)
        """
        super().__init__(cache_tools_list)
        self.params = params
        self.manual_transport_type = transport_type
        self.detected_transport_type = self._detect_transport_type()
        self.active_transport_type = transport_type or self.detected_transport_type
        self._name = name or self._generate_server_name()

        # Validate parameters for the selected transport type
        self._validate_parameters()

    def _detect_transport_type(self) -> TransportType:
        """Detect transport type based on parameters."""
        # Priority 1: Check for STDIO indicators
        if "command" in self.params or "args" in self.params:
            return "stdio"

        # Priority 2: Check for URL-based transports
        if "url" in self.params:
            url = self.params["url"]
            # Check for SSE indicators
            if (
                url.endswith("/sse")
                or "sse_read_timeout" in self.params
                and "terminate_on_close" not in self.params
            ):
                return "sse"
            # Default to streamable HTTP for URLs
            return "streamable_http"

        # Fallback: if no clear indicators, default to SSE (most common)
        return "sse"

    def _generate_server_name(self) -> str:
        """Generate a descriptive server name based on transport type and parameters."""
        transport = self.active_transport_type.upper()

        if self.active_transport_type == "stdio":
            command = self.params.get("command", "unknown")
            if isinstance(command, list):
                command = " ".join(command)
            return f"Universal {transport} Server: {command}"
        elif self.active_transport_type in ["sse", "streamable_http"]:
            url = self.params.get("url", "unknown")
            return f"Universal {transport} Server at {url}"
        else:
            return f"Universal {transport} Server"

    def _validate_parameters(self) -> None:
        """Validate parameters for the active transport type."""
        if self.active_transport_type == "stdio":
            if "command" not in self.params:
                raise ValueError("STDIO transport requires 'command' parameter")
        elif self.active_transport_type in ["sse", "streamable_http"]:
            if "url" not in self.params:
                raise ValueError(
                    f"{self.active_transport_type.upper()} transport requires 'url' parameter"
                )

    @property
    def name(self) -> str:
        """A readable name for the server."""
        return self._name

    def create_streams(
        self,
    ) -> AbstractAsyncContextManager[
        Tuple[
            MemoryObjectReceiveStream[JSONRPCMessage | Exception],
            MemoryObjectSendStream[JSONRPCMessage],
        ]
    ]:
        """Create the streams for the server based on the active transport type."""
        if self.active_transport_type == "sse":
            return self._create_sse_streams()
        elif self.active_transport_type == "streamable_http":
            return self._create_streamable_http_streams()
        elif self.active_transport_type == "stdio":
            return self._create_stdio_streams()
        else:
            raise ValueError(
                f"Unsupported transport type: {self.active_transport_type}"
            )

    def _create_sse_streams(
        self,
    ) -> AbstractAsyncContextManager[
        Tuple[
            MemoryObjectReceiveStream[JSONRPCMessage | Exception],
            MemoryObjectSendStream[JSONRPCMessage],
        ]
    ]:
        """Create SSE streams."""
        return sse_client(
            url=self.params["url"],
            headers=self.params.get("headers"),
            timeout=self.params.get("timeout", 5),
            sse_read_timeout=self.params.get("sse_read_timeout", 60 * 5),
        )

    def _create_streamable_http_streams(
        self,
    ) -> AbstractAsyncContextManager[
        Tuple[
            MemoryObjectReceiveStream[JSONRPCMessage | Exception],
            MemoryObjectSendStream[JSONRPCMessage],
        ]
    ]:
        """Create Streamable HTTP streams."""

        # Create a wrapper that converts 3-tuple to 2-tuple for base class compatibility
        class StreamableHttpWrapper:
            def __init__(self, params):
                self.params = params
                self.context = None

            async def __aenter__(self):
                # For Google Calendar MCP: Don't send auth headers during connection
                # Only send them during tool calls
                connection_params = self.params.copy()

                # Remove ALL headers for initial connection to avoid 400 Bad Request
                # Google Calendar MCP doesn't want any headers during connection
                if "headers" in connection_params:
                    del connection_params["headers"]

                # Create the streamable HTTP client context without auth headers
                self.context = streamablehttp_client(
                    url=connection_params["url"],
                    headers=connection_params.get("headers"),
                    timeout=timedelta(seconds=connection_params.get("timeout", 30)),
                    sse_read_timeout=timedelta(
                        seconds=connection_params.get("sse_read_timeout", 300)
                    ),
                    terminate_on_close=connection_params.get(
                        "terminate_on_close", True
                    ),
                    auth=connection_params.get("auth"),
                )

                # Enter the context and extract only read and write streams
                result = await self.context.__aenter__()

                # streamablehttp_client returns (read, write, session_callback)
                # We only return (read, write) for base class compatibility
                if isinstance(result, tuple) and len(result) >= 2:
                    return (result[0], result[1])  # Return only read and write
                else:
                    return result  # Fallback if format is different

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                if self.context:
                    return await self.context.__aexit__(exc_type, exc_val, exc_tb)

        return StreamableHttpWrapper(self.params)

    async def call_tool(
        self, tool_name: str, arguments: Optional[Dict[str, Any]] = None
    ) -> CallToolResult:
        """Override call_tool to add authentication headers for Google Calendar MCP."""
        if not self.session:
            raise RuntimeError(
                "Server not initialized. Make sure you call connect() first."
            )

        arguments = arguments or {}

        # For streamable HTTP with headers: temporarily modify the client's headers
        if self.active_transport_type == "streamable_http" and "headers" in self.params:
            # Get auth headers from params
            auth_headers = {}
            for key, value in self.params["headers"].items():
                # Include Google Calendar specific headers and standard auth headers
                if key.lower() in [
                    "authorization",
                    "auth",
                    "x-google-access-token",
                    "x-google-refresh-token",
                ] or key.startswith("x-"):
                    auth_headers[key] = value

            if auth_headers:
                # Access the streamable HTTP client's headers directly
                # The session should have a reference to the HTTP client
                original_client_headers = None

                # Try to access the HTTP client through the session's streams
                if hasattr(self.session, "_read_stream") and hasattr(
                    self.session._read_stream, "_client"
                ):
                    client = self.session._read_stream._client
                    if hasattr(client, "headers"):
                        original_client_headers = client.headers.copy()
                        client.headers.update(auth_headers)

                # Alternative: Try accessing through the write stream
                elif hasattr(self.session, "_write_stream") and hasattr(
                    self.session._write_stream, "_client"
                ):
                    client = self.session._write_stream._client
                    if hasattr(client, "headers"):
                        original_client_headers = client.headers.copy()
                        client.headers.update(auth_headers)

                # Fallback: Try setting on the session itself
                else:
                    # Set headers on session for the request
                    if not hasattr(self.session, "_tool_call_headers"):
                        self.session._tool_call_headers = {}
                    self.session._tool_call_headers.update(auth_headers)

                try:
                    # Call the tool with auth headers
                    result = await super().call_tool(tool_name, arguments)
                    return result
                finally:
                    # Restore original headers
                    if original_client_headers is not None and hasattr(
                        client, "headers"
                    ):
                        client.headers = original_client_headers
                    elif hasattr(self.session, "_tool_call_headers"):
                        delattr(self.session, "_tool_call_headers")
            else:
                # No auth headers, use default implementation
                return await super().call_tool(tool_name, arguments)
        else:
            # For other transport types, use the default implementation
            return await super().call_tool(tool_name, arguments)

    def _create_stdio_streams(
        self,
    ) -> AbstractAsyncContextManager[
        Tuple[
            MemoryObjectReceiveStream[JSONRPCMessage | Exception],
            MemoryObjectSendStream[JSONRPCMessage],
        ]
    ]:
        """Create STDIO streams."""
        # Prepare command
        command = self.params["command"]
        if isinstance(command, str):
            command = [command]

        # Add args if provided
        if "args" in self.params:
            command.extend(self.params["args"])

        # Create StdioServerParameters
        server_params = StdioServerParameters(
            command=command,
            env=self.params.get("env"),
            cwd=self.params.get("cwd"),
        )

        return stdio_client(server_params)


# Factory function for creating universal MCP servers
def create_universal_mcp_server(
    params: UniversalMCPParams,
    cache_tools_list: bool = False,
    name: Optional[str] = None,
    transport_type: Optional[TransportType] = None,
) -> UniversalMCPServer:
    """Factory function to create a universal MCP server.

    Args:
        params: Parameters for the server (flexible structure supporting all transport types)
        cache_tools_list: Whether to cache the tools list
        name: A readable name for the server
        transport_type: Manual transport type override (auto-detected if None)

    Returns:
        UniversalMCPServer instance

    Examples:
        # Auto-detected Streamable HTTP
        server = create_universal_mcp_server({
            "url": "http://localhost:8080/mcp",
            "headers": {"Authorization": "Bearer token"}
        })

        # Auto-detected STDIO
        server = create_universal_mcp_server({
            "command": ["python", "-m", "my_mcp_server"],
            "args": ["--config", "config.json"]
        })

        # Manual transport override
        server = create_universal_mcp_server({
            "url": "http://localhost:8080/mcp",
            "transport_type": "streamable_http"
        })
    """
    return UniversalMCPServer(params, cache_tools_list, name, transport_type)


# Utility functions for parameter validation and conversion
def validate_universal_params(
    params: UniversalMCPParams, transport_type: TransportType
) -> None:
    """Validate parameters for a specific transport type.

    Args:
        params: Parameters to validate
        transport_type: Transport type to validate against

    Raises:
        ValueError: If required parameters are missing or invalid
    """
    if transport_type == "stdio":
        if "command" not in params:
            raise ValueError("STDIO transport requires 'command' parameter")
        command = params["command"]
        if not isinstance(command, (str, list)):
            raise ValueError("'command' parameter must be a string or list")
    elif transport_type in ["sse", "streamable_http"]:
        if "url" not in params:
            raise ValueError(
                f"{transport_type.upper()} transport requires 'url' parameter"
            )
        if not isinstance(params["url"], str):
            raise ValueError("'url' parameter must be a string")


def normalize_universal_params(params: UniversalMCPParams) -> UniversalMCPParams:
    """Normalize parameters to ensure consistent format.

    Args:
        params: Parameters to normalize

    Returns:
        Normalized parameters
    """
    normalized = params.copy()

    # Normalize command to list format for STDIO
    if "command" in normalized and isinstance(normalized["command"], str):
        normalized["command"] = [normalized["command"]]

    # Ensure timeout values are numeric
    for timeout_key in ["timeout", "sse_read_timeout"]:
        if timeout_key in normalized and isinstance(normalized[timeout_key], str):
            try:
                normalized[timeout_key] = float(normalized[timeout_key])
            except ValueError:
                pass  # Keep original value if conversion fails

    return normalized
