apiVersion: v1
kind: ServiceAccount
metadata:
  name: mcp-execution-service-ai-sa
  namespace: ruh-catalyst
  labels:
    name: mcp-execution-service-ai-sa
    namespace: ruh-catalyst
    app: mcp-execution-service-ai
    deployment: mcp-execution-service-ai-dp
---
# Create Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: mcp-execution-service-secrets
  namespace: ruh-catalyst
type: Opaque
data:
  # Base64 encoded server auth key - replace with actual value
  server-auth-key: aENieEFscGpGeVhQZDFVaUp3WnFUV2dDMjBEck1iNllEeE43dHJ6N09YWg==
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-execution-service-ai-dp
  namespace: ruh-catalyst
  labels:
    name: mcp-execution-service-ai-dp
    namespace: ruh-catalyst
    app: mcp-execution-service-ai
    serviceaccount: mcp-execution-service-ai-sa
    deployment: mcp-execution-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mcp-execution-service-ai
      deployment: mcp-execution-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: mcp-execution-service-ai
        deployment: mcp-execution-service-ai-dp
    spec:
      serviceAccountName: mcp-execution-service-ai-sa
      containers:
      - name: mcp-execution-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        env:
        - name: API_BASE_URL
          value: "http://gateway-service-ai-svc.ruh-dev.svc.cluster.local"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka.ruh.ai:9094"
        - name: KAFKA_CONSUMER_TOPIC
          value: "mcp-execution-request"
        - name: KAFKA_CONSUMER_GROUP_ID
          value: "mcp_executor_service"
        - name: KAFKA_RESULTS_TOPIC
          value: "mcp_results"
        - name: LOG_LEVEL
          value: "INFO"
        - name: MAX_CONCURRENT_TASKS
          value: "10"
        - name: DEFAULT_MCP_RETRIES
          value: "3"
        - name: CONTAINER_API_TIMEOUT
          value: "30"
        - name: MCP_CONFIG_CACHE_TTL
          value: "300"
        - name: MCP_CONFIG_TIMEOUT
          value: "10"
        - name: CREDENTIAL_CACHE_TTL
          value: "300"
        - name: SERVER_AUTH_KEY
          valueFrom:
            secretKeyRef:
              name: mcp-execution-service-secrets
              key: server-auth-key
        # MCP SDK Configuration
        - name: ENV
          value: "dev"
        - name: USE_MCP_SDK
          value: "true"
        - name: MCP_SDK_TIMEOUT
          value: "60"
        - name: MCP_SDK_BUFFER_SIZE
          value: "1048576"
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50052
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:
      #   eks.amazonaws.com/capacityType: SPOT
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: mcp-execution-service-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: mcp-execution-service-ai
    deployment: mcp-execution-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50052
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:mcp-execution-service-mcp-execution-hpa
#   namespace:ruh-catalyst
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:mcp-execution-service-mcp-execution-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mcp-execution-service-ingress
  namespace: ruh-catalyst
spec:
  ingressClassName: nginx
  rules:
  - host: mcp-execution-service-dev.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mcp-execution-service-ai-svc
            port:
              number: 80
