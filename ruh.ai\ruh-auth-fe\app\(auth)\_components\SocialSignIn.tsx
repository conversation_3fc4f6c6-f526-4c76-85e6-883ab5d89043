"use client";

import { But<PERSON> } from "@/components/ui/button";
import { socialProviders } from "@/app/shared/constants";

export function SocialSignIn() {
  return (
    <div className="w-full">
      <div className="flex gap-4 justify-center">
        {socialProviders.map((provider) => (
          <Button
            key={provider.name}
            variant="iconBtn"
            size="icon"
            onClick={provider.onClick}
            aria-label={provider.name}
          >
            <provider.icon />
          </Button>
        ))}
      </div>
    </div>
  );
}
