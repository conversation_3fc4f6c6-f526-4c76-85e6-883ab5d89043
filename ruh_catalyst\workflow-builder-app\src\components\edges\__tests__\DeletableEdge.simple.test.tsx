import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import DeletableEdge from '../DeletableEdge';

// Mock reactflow
jest.mock('reactflow', () => ({
  getBezierPath: jest.fn(() => ['M 0 0 L 100 100', 50, 50]),
  EdgeLabelRenderer: ({ children }) => React.createElement('div', {}, children),
  useReactFlow: jest.fn(() => ({ setEdges: jest.fn() })),
}));

// Mock lucide-react
jest.mock('lucide-react', () => ({
  Trash2: () => React.createElement('div', { 'data-testid': 'trash-icon' }, '🗑️'),
}));

describe('DeletableEdge Tool Connection', () => {
  test('tool connections have orange dashed styling', () => {
    const props = {
      id: 'edge-1',
      sourceX: 0, sourceY: 0, targetX: 100, targetY: 100,
      sourcePosition: 'right', targetPosition: 'left',
      targetHandle: 'tool_1', selected: false
    };
    
    render(React.createElement(DeletableEdge, props));
    const edge = screen.getByTestId('edge-tool-connection');
    expect(edge).toHaveStyle('stroke: rgb(245, 158, 11)');
    expect(edge).toHaveStyle('stroke-dasharray: 5,5');
  });
  
  test('regular connections have blue solid styling', () => {
    const props = {
      id: 'edge-2',
      sourceX: 0, sourceY: 0, targetX: 100, targetY: 100,
      sourcePosition: 'right', targetPosition: 'left',
      targetHandle: 'input_data', selected: false
    };
    
    render(React.createElement(DeletableEdge, props));
    const edge = screen.getByTestId('edge-regular-connection');
    expect(edge).toHaveStyle('stroke: rgb(59, 130, 246)');
    expect(edge).toHaveStyle('stroke-dasharray: none');
  });
});
