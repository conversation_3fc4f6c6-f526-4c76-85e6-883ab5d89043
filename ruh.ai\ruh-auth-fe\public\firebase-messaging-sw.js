// Firebase Cloud Messaging Service Worker

// Log when the service worker is installed
self.addEventListener("install", (event) => {
  self.skipWaiting(); // Ensure the service worker activates immediately
});

// Log when the service worker is activated
self.addEventListener("activate", (event) => {
  // Claim clients immediately
  event.waitUntil(clients.claim());
});

// Import and configure the Firebase SDK
// Using the latest version of Firebase
importScripts(
  "https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js"
);

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAbZVxFPPdykopVUGeMmViTqTMp_QJ8X5U",
  authDomain: "ruh-ai.firebaseapp.com",
  projectId: "ruh-ai",
  storageBucket: "ruh-ai.firebasestorage.app",
  messagingSenderId: "931841925104",
  appId: "1:931841925104:web:20552e8066ecec705c0735",
  measurementId: "G-YKXRZ87QFE",
};

// Initialize Firebase
try {
  firebase.initializeApp(firebaseConfig);
} catch (error) {
  // Silent error handling
}

// Get messaging instance
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  // Extract notification data
  const notificationTitle = payload.notification?.title || "New Notification";
  const notificationOptions = {
    body: payload.notification?.body || "",
    icon: "/icon.png",
    badge: "/icon.png",
    data: payload.data, // Include any custom data
    // Add vibration pattern for mobile devices
    vibrate: [100, 50, 100],
    // Add actions if needed
    actions: [
      {
        action: "open",
        title: "Open App",
      },
    ],
  };

  // Show the notification
  return self.registration.showNotification(
    notificationTitle,
    notificationOptions
  );
});

// Handle notification click
self.addEventListener("notificationclick", (event) => {
  // Close the notification
  event.notification.close();

  // This looks to see if the current is already open and focuses if it is
  event.waitUntil(
    clients
      .matchAll({
        type: "window",
      })
      .then((clientList) => {
        // Try to focus an existing window
        for (const client of clientList) {
          if (client.url === "/" && "focus" in client) {
            return client.focus();
          }
        }

        // If no window is open, open a new one
        if (clients.openWindow) {
          return clients.openWindow("/");
        }
      })
  );
});
