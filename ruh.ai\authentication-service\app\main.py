"""
Authentication Service Main Application

This module sets up the gRPC service for the authentication microservice.
"""

import os
import grpc
from concurrent import futures
import logging

from app.core.config import settings
from app.db.session import create_tables
from app.grpc_ import authentication_pb2_grpc
from app.services.auth_service import AuthenticationServicer

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def serve():
    """Start the gRPC authentication service."""
    try:
        # Create database tables
        logger.info("Starting Authentication Service...")
        try:
            create_tables()
            logger.info("Database tables created/verified")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise

        # Create gRPC server
        server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

        # Add authentication service
        auth_servicer = AuthenticationServicer()
        authentication_pb2_grpc.add_AuthenticationServiceServicer_to_server(auth_servicer, server)

        # Get port from environment or use default
        port = os.getenv("PORT", "50054")
        server.add_insecure_port(f"[::]:{port}")

        # Start server
        server.start()
        logger.info(f"Authentication service started on port {port}")

        # Keep thread alive
        server.wait_for_termination()

    except Exception as e:
        logger.error(f"Failed to start gRPC server: {e}")
        raise


if __name__ == "__main__":
    serve()
