#!/usr/bin/env python3
"""
Integration test for SSH key manager with MCP client.
Tests the actual SSH command construction using the global SSH manager.
"""

import os
import asyncio
import logging

from app.services.ssh_manager import initialize_global_ssh_key, cleanup_global_ssh_key
from app.core_.client import MC<PERSON>lient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Sample SSH key content (base64 encoded dummy key for testing)
SAMPLE_SSH_KEY_B64 = """LS0tLS1CRUdJTiBPUEVOU1NIIFBSSVZBVEUgS0VZLS0tLS0KYjNCbGJuTnphQzFyWlhrdGRqRUFBQUFBQkc1dmJtVUFBQUFFYm05dVpRQUFBQUFBQUFBQkFBQUJGd0FBQUFkemMyZ3RjbgpOaEFBQUFBd0VBQVFBQUFRRUF0K2dGejVZWUNxK2ZqQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCkFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUEKQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQQpBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBCi0tLS0tRU5EIE9QRU5TU0ggUFJJVkFURSBLRVktLS0tLQo="""


def test_ssh_command_construction():
    """Test SSH command construction with global SSH manager."""
    logger.info("🧪 Testing SSH command construction with MCP client...")
    
    # Initialize global SSH key
    logger.info("Initializing global SSH key...")
    initialize_global_ssh_key(SAMPLE_SSH_KEY_B64)
    
    # Create MCP client with SSH Docker connection
    client = MCPClient(
        connection_type="ssh_docker",
        ssh_host="example.com",
        ssh_user="ubuntu",
        ssh_port=22,
        ssh_key_content=SAMPLE_SSH_KEY_B64,
        docker_image="test-container",
        container_command="echo 'test'"
    )
    
    # Test SSH command construction
    logger.info("Building SSH command...")
    ssh_command = client._build_ssh_command()
    
    logger.info(f"Generated SSH command: {' '.join(ssh_command)}")
    
    # Verify the command contains the SSH key
    ssh_key_found = False
    for i, arg in enumerate(ssh_command):
        if arg == "-i" and i + 1 < len(ssh_command):
            key_path = ssh_command[i + 1]
            logger.info(f"SSH key path found in command: {key_path}")
            if os.path.exists(key_path):
                logger.info("✅ SSH key file exists and is accessible")
                ssh_key_found = True
            else:
                logger.error("❌ SSH key file not found")
            break
    
    if not ssh_key_found:
        logger.error("❌ SSH key not found in command")
        return False
    
    # Verify other SSH options are present
    required_options = [
        "StrictHostKeyChecking=no",
        "UserKnownHostsFile=/dev/null",
        "ConnectTimeout=30",
        "IdentitiesOnly=yes"
    ]
    
    for option in required_options:
        if option in ' '.join(ssh_command):
            logger.info(f"✅ Found required SSH option: {option}")
        else:
            logger.error(f"❌ Missing required SSH option: {option}")
            return False
    
    # Verify Docker command is included
    if "docker" in ' '.join(ssh_command):
        logger.info("✅ Docker command found in SSH command")
    else:
        logger.error("❌ Docker command not found in SSH command")
        return False
    
    logger.info("✅ SSH command construction test completed successfully")
    return True


def test_multiple_clients():
    """Test multiple MCP clients using the same global SSH key."""
    logger.info("🧪 Testing multiple MCP clients with shared SSH key...")
    
    # Initialize global SSH key
    initialize_global_ssh_key(SAMPLE_SSH_KEY_B64)
    
    # Create multiple clients
    clients = []
    for i in range(3):
        client = MCPClient(
            connection_type="ssh_docker",
            ssh_host=f"host{i}.example.com",
            ssh_user="ubuntu",
            ssh_port=22,
            ssh_key_content=SAMPLE_SSH_KEY_B64,
            docker_image=f"test-container-{i}",
            container_command=f"echo 'test-{i}'"
        )
        clients.append(client)
    
    # Build SSH commands for all clients
    ssh_key_paths = []
    for i, client in enumerate(clients):
        ssh_command = client._build_ssh_command()
        logger.info(f"Client {i} SSH command: {' '.join(ssh_command)}")
        
        # Extract SSH key path
        for j, arg in enumerate(ssh_command):
            if arg == "-i" and j + 1 < len(ssh_command):
                key_path = ssh_command[j + 1]
                ssh_key_paths.append(key_path)
                break
    
    # Verify all clients use the same SSH key file
    if len(set(ssh_key_paths)) == 1:
        logger.info("✅ All clients use the same SSH key file")
        logger.info(f"Shared SSH key path: {ssh_key_paths[0]}")
        return True
    else:
        logger.error("❌ Clients are using different SSH key files")
        logger.error(f"SSH key paths: {ssh_key_paths}")
        return False


def main():
    """Run all integration tests."""
    logger.info("🚀 Starting SSH Integration Tests...")
    
    try:
        # Test 1: SSH command construction
        success1 = test_ssh_command_construction()
        print()
        
        # Test 2: Multiple clients sharing SSH key
        success2 = test_multiple_clients()
        print()
        
        if success1 and success2:
            logger.info("🎉 All integration tests completed successfully!")
            return 0
        else:
            logger.error("💥 Some integration tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"💥 Integration test failed: {e}", exc_info=True)
        return 1
    finally:
        # Clean up
        logger.info("Cleaning up global SSH key...")
        cleanup_global_ssh_key()


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
