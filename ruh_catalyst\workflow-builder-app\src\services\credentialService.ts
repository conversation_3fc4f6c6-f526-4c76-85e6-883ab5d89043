/**
 * Credential Service
 * 
 * This service handles all credential-related API operations,
 * including CRUD operations and secure value retrieval for workflow execution.
 */

import { AxiosInstance } from 'axios';
import { createAxiosInstance } from '../utils/axios';
import { API_ENDPOINTS } from '../lib/apiConfig';
import {
  CredentialCreate,
  CredentialUpdate,
  Credential,
  CredentialListResponse,
  CredentialServiceInterface,
  CredentialErrorType,
  BackendCredentialListResponse,
  BackendCredentialDetailsResponse,
  BackendCredentialResponse,
  BackendCredentialDeleteResponse,
} from '../types/credentials';
import {
  transformCredentialForBackend,
  transformCredentialUpdateForBackend,
  transformCredentialFromBackend,
  transformCredentialListFromBackend,
  validateCredentialCreate,
  validateCredentialUpdate,
} from '../utils/credentialTransforms';
import {
  handleCredentialError,
  logCredentialError,
  withRetry,
} from '../utils/credentialErrorHandler';

/**
 * CredentialService class implementing the CredentialServiceInterface
 * Provides authenticated API operations for credential management
 */
export class CredentialService implements CredentialServiceInterface {
  private api: AxiosInstance;

  constructor() {
    this.api = createAxiosInstance({
      enableTokenRefresh: true,
      enableClientSideToken: true,
    });
  }

  /**
   * Fetch all credentials for the current user
   * Returns transformed credentials without values for security
   */
  async fetchCredentials(): Promise<CredentialListResponse> {
    try {
      const response = await this.api.get<BackendCredentialListResponse>(API_ENDPOINTS.CREDENTIALS.LIST);

      const transformedResponse = transformCredentialListFromBackend(response.data);

      console.log(`Fetched ${transformedResponse.credentials.length} credentials`);
      return transformedResponse;
    } catch (error) {
      const credentialError = handleCredentialError(error);
      logCredentialError('fetchCredentials', credentialError);
      throw credentialError;
    }
  }

  /**
   * Create a new credential
   * Validates input data and transforms to backend format
   */
  async createCredential(data: CredentialCreate): Promise<Credential> {
    try {
      // Validate input data
      if (!validateCredentialCreate(data)) {
        const validationError = {
          type: CredentialErrorType.VALIDATION_ERROR,
          message: 'Invalid credential data provided',
          field: 'name',
          code: '400'
        };
        throw validationError;
      }

      // Transform to backend format
      const backendData = transformCredentialForBackend(data);

      // Create credential
      const response = await this.api.post<BackendCredentialResponse>(
        API_ENDPOINTS.CREDENTIALS.CREATE,
        backendData
      );

      if (!response.data.success || !response.data.id) {
        throw new Error(response.data.message || 'Failed to create credential');
      }

      // Fetch the created credential to get full details
      const createdCredential = await this.getCredential(response.data.id);

      console.log(`Created credential: ${createdCredential.name}`);
      return createdCredential;
    } catch (error) {
      // If it's already a credential error (from validation), don't transform it
      if (error && typeof error === 'object' && 'type' in error) {
        logCredentialError('createCredential', error, { name: data.name });
        throw error;
      }

      const credentialError = handleCredentialError(error);
      logCredentialError('createCredential', credentialError, { name: data.name });
      throw credentialError;
    }
  }

  /**
   * Get a specific credential by ID
   * Returns transformed credential without value for security
   */
  async getCredential(id: string): Promise<Credential> {
    try {
      if (!id || id.trim().length === 0) {
        throw new Error('Credential ID is required');
      }

      const response = await this.api.get<BackendCredentialDetailsResponse>(API_ENDPOINTS.CREDENTIALS.GET(id));

      if (!response.data.success || !response.data.credential) {
        throw new Error(response.data.message || 'Credential not found');
      }

      const transformedCredential = transformCredentialFromBackend(response.data.credential);

      console.log(`Retrieved credential: ${transformedCredential.name}`);
      return transformedCredential;
    } catch (error) {
      const credentialError = handleCredentialError(error);
      logCredentialError('getCredential', credentialError, { credentialId: id });
      throw credentialError;
    }
  }

  /**
   * Update an existing credential
   * Validates input data and transforms to backend format
   */
  async updateCredential(id: string, data: CredentialUpdate): Promise<Credential> {
    try {
      if (!id || id.trim().length === 0) {
        throw new Error('Credential ID is required');
      }

      // Validate input data
      if (!validateCredentialUpdate(data)) {
        throw new Error('Invalid credential update data provided');
      }

      // Transform to backend format
      const backendData = transformCredentialUpdateForBackend(data);

      // Update credential
      const response = await this.api.put<BackendCredentialResponse>(
        API_ENDPOINTS.CREDENTIALS.UPDATE(id),
        backendData
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to update credential');
      }

      // Fetch the updated credential to get full details
      const updatedCredential = await this.getCredential(id);
      
      console.log(`Updated credential: ${updatedCredential.name}`);
      return updatedCredential;
    } catch (error) {
      const credentialError = handleCredentialError(error);
      logCredentialError('updateCredential', credentialError, { credentialId: id });
      throw credentialError;
    }
  }

  /**
   * Delete a credential by ID
   * Permanently removes the credential from the system
   */
  async deleteCredential(id: string): Promise<void> {
    try {
      if (!id || id.trim().length === 0) {
        throw new Error('Credential ID is required');
      }

      const response = await this.api.delete<BackendCredentialDeleteResponse>(
        API_ENDPOINTS.CREDENTIALS.DELETE(id)
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to delete credential');
      }

      console.log(`Deleted credential: ${id}`);
    } catch (error) {
      const credentialError = handleCredentialError(error);
      logCredentialError('deleteCredential', credentialError, { credentialId: id });
      throw credentialError;
    }
  }

  /**
   * Get credential value for workflow execution
   * This is the only method that returns the actual credential value
   * Should only be used during workflow execution
   */
  async getCredentialValueForExecution(id: string): Promise<string> {
    try {
      if (!id || id.trim().length === 0) {
        throw new Error('Credential ID is required');
      }

      const response = await this.api.get<BackendCredentialDetailsResponse>(API_ENDPOINTS.CREDENTIALS.GET(id));

      if (!response.data.success || !response.data.credential) {
        const notFoundError = {
          type: CredentialErrorType.NOT_FOUND_ERROR,
          message: 'Credential not found.',
          code: '404'
        };
        throw notFoundError;
      }

      const credentialValue = response.data.credential.value;
      if (!credentialValue) {
        const notFoundError = {
          type: CredentialErrorType.NOT_FOUND_ERROR,
          message: 'Credential not found.',
          code: '404'
        };
        throw notFoundError;
      }

      console.log(`Retrieved credential value for execution: ${id}`);
      return credentialValue;
    } catch (error) {
      // If it's already a credential error (from validation), don't transform it
      if (error && typeof error === 'object' && 'type' in error) {
        logCredentialError('getCredentialValueForExecution', error, { credentialId: id });
        throw error;
      }

      const credentialError = handleCredentialError(error);
      logCredentialError('getCredentialValueForExecution', credentialError, { credentialId: id });
      throw credentialError;
    }
  }

  /**
   * Check if the credential service is available
   * Used for health checks and feature flag validation
   */
  async isServiceAvailable(): Promise<boolean> {
    try {
      await this.api.get(API_ENDPOINTS.CREDENTIALS.LIST);
      return true;
    } catch (error) {
      console.warn('Credential service is not available:', error);
      return false;
    }
  }

  /**
   * Get credential statistics for the current user
   * Returns count and usage information
   */
  async getCredentialStats(): Promise<{ total: number; lastUsed?: string }> {
    try {
      const response = await this.fetchCredentials();
      const credentials = response.credentials;
      
      const stats = {
        total: credentials.length,
        lastUsed: credentials.length > 0 
          ? credentials
              .map(c => c.lastUsedAt)
              .sort()
              .reverse()[0]
          : undefined
      };

      return stats;
    } catch (error) {
      const credentialError = handleCredentialError(error);
      logCredentialError('getCredentialStats', credentialError);
      throw credentialError;
    }
  }

  /**
   * Search credentials by name or description
   * Client-side filtering for now, can be moved to backend later
   */
  async searchCredentials(query: string): Promise<CredentialListResponse> {
    try {
      const response = await this.fetchCredentials();
      
      if (!query || query.trim().length === 0) {
        return response;
      }

      const searchTerm = query.toLowerCase().trim();
      const filteredCredentials = response.credentials.filter(credential => 
        credential.name.toLowerCase().includes(searchTerm) ||
        (credential.description && credential.description.toLowerCase().includes(searchTerm))
      );

      return { credentials: filteredCredentials };
    } catch (error) {
      const credentialError = handleCredentialError(error);
      logCredentialError('searchCredentials', credentialError, { query });
      throw credentialError;
    }
  }
}

// Export a singleton instance for use across the application
export const credentialService = new CredentialService();
