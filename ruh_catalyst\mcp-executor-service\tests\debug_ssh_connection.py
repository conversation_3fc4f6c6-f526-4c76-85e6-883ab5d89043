#!/usr/bin/env python3
"""
Debug script to test SSH connection and container execution step by step.
"""

import asyncio
import subprocess
import logging
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import get_global_ssh_manager, initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_ssh_connection():
    """Test SSH connection step by step."""
    logger.info("=== SSH Connection Debug Test ===")

    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
        ssh_manager = get_global_ssh_manager()
        ssh_key_path = ssh_manager.get_ssh_key_path()
        logger.info(f"SSH key path: {ssh_key_path}")
    else:
        logger.error("No SSH key content found in settings")
        return False

    # Test basic SSH connection
    logger.info("Testing basic SSH connection...")
    ssh_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "echo 'SSH connection successful'",
    ]

    try:
        result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            logger.info(f"✅ SSH connection successful: {result.stdout.strip()}")
        else:
            logger.error(f"❌ SSH connection failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ SSH connection error: {e}")
        return False

    return True


async def test_container_status(container_id: str):
    """Test container status and commands."""
    logger.info(f"=== Container Status Test: {container_id} ===")

    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()

    # Test if container exists and is running
    logger.info("Checking if container exists...")
    check_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker ps --filter name={container_id} --format 'table {{{{.Names}}}}\\t{{{{.Status}}}}\\t{{{{.Image}}}}'",
    ]

    try:
        result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            logger.info(f"Container status:\n{result.stdout}")
        else:
            logger.error(f"Failed to check container status: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Error checking container status: {e}")
        return False

    # Test container inspect
    logger.info("Inspecting container configuration...")
    inspect_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker inspect {container_id} --format='{{{{json .Config}}}}'",
    ]

    try:
        result = subprocess.run(inspect_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            import json

            config = json.loads(result.stdout.strip())
            logger.info(f"Container config:")
            logger.info(f"  Image: {config.get('Image', 'N/A')}")
            logger.info(f"  Cmd: {config.get('Cmd', 'N/A')}")
            logger.info(f"  Entrypoint: {config.get('Entrypoint', 'N/A')}")
            logger.info(f"  WorkingDir: {config.get('WorkingDir', 'N/A')}")
            logger.info(
                f"  Env: {config.get('Env', [])[:5]}..."
            )  # Show first 5 env vars
        else:
            logger.error(f"Failed to inspect container: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Error inspecting container: {e}")
        return False

    return True


async def test_container_exec(container_id: str, command: str = "python server.py"):
    """Test executing commands in the container."""
    logger.info(f"=== Container Exec Test: {container_id} ===")

    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()

    # Test simple command first
    logger.info("Testing simple echo command...")
    echo_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec {container_id} echo 'Container exec working'",
    ]

    try:
        result = subprocess.run(echo_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            logger.info(f"✅ Container exec successful: {result.stdout.strip()}")
        else:
            logger.error(f"❌ Container exec failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Container exec error: {e}")
        return False

    # Test listing files in container
    logger.info("Testing file listing in container...")
    ls_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec {container_id} ls -la",
    ]

    try:
        result = subprocess.run(ls_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            logger.info(f"Container file listing:\n{result.stdout}")
        else:
            logger.error(f"Failed to list files: {result.stderr}")
    except Exception as e:
        logger.error(f"Error listing files: {e}")

    # Test the actual MCP command
    logger.info(f"Testing MCP command: {command}")
    mcp_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"timeout 5 docker exec -i {container_id} {command}",
    ]

    try:
        # Use a shorter timeout for this test
        result = subprocess.run(mcp_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"MCP command exit code: {result.returncode}")
        if result.stdout:
            logger.info(f"MCP command stdout:\n{result.stdout}")
        if result.stderr:
            logger.info(f"MCP command stderr:\n{result.stderr}")
    except subprocess.TimeoutExpired:
        logger.info("MCP command timed out (expected for interactive server)")
    except Exception as e:
        logger.error(f"Error testing MCP command: {e}")

    return True


async def list_running_containers():
    """List all running containers to find available ones."""
    logger.info("=== Listing Running Containers ===")

    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()

    list_cmd = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKnownHostsFile=/dev/null",
        "-o",
        "ConnectTimeout=30",
        "-o",
        "IdentitiesOnly=yes",
        "-p",
        str(settings.ssh_port),
        "-i",
        ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "docker ps --format 'table {{.Names}}\\t{{.Status}}\\t{{.Image}}'",
    ]

    try:
        result = subprocess.run(list_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            logger.info(f"Running containers:\n{result.stdout}")
            # Extract container names from the output
            lines = result.stdout.strip().split("\n")[1:]  # Skip header
            container_names = []
            for line in lines:
                if line.strip():
                    # Split by whitespace and take the first part (container name)
                    parts = line.split()
                    if len(parts) > 0:
                        container_names.append(parts[0].strip())
            return container_names
        else:
            logger.error(f"Failed to list containers: {result.stderr}")
            return []
    except Exception as e:
        logger.error(f"Error listing containers: {e}")
        return []


async def main():
    """Main debug function."""
    logger.info("Starting SSH and Container Debug Tests")

    # Test SSH connection first
    if not await test_ssh_connection():
        logger.error("SSH connection test failed, stopping")
        return

    # List running containers first
    running_containers = await list_running_containers()
    if not running_containers:
        logger.warning(
            "No running containers found. The container may have been stopped."
        )
        logger.info(
            "This explains why the MCP connection was failing - the target container doesn't exist."
        )
        return

    # Use the first available container for testing
    container_id = running_containers[0]
    logger.info(f"Using container for testing: {container_id}")

    # Test container status
    if not await test_container_status(container_id):
        logger.error("Container status test failed")
        return

    # Test container execution with the correct command
    # For Node.js containers, use "node dist/index.js"
    if not await test_container_exec(container_id, "node dist/index.js"):
        logger.error("Container exec test failed")
        return

    logger.info("All debug tests completed")


if __name__ == "__main__":
    asyncio.run(main())
