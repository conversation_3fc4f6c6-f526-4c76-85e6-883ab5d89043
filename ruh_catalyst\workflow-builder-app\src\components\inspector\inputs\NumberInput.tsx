import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Input } from "@/components/ui/input";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { cn } from "@/lib/utils";

interface NumberInputProps {
  inputDef: InputDefinition;
  value: number | string;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering number inputs (int and float)
 */
export function NumberInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: NumberInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  const isFloat = inputDef.input_type === "float";
  
  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="relative">
        <Input
          id={inputId}
          type="number"
          step={isFloat ? "any" : "1"}
          value={value ?? ""}
          onChange={(e) => onChange(inputDef.name, e.target.value)}
          placeholder={inputDef.display_name}
          className={cn(
            "bg-background/50 mt-1 h-8 text-xs",
            isDisabled && "opacity-50"
          )}
          disabled={isDisabled}
        />
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
