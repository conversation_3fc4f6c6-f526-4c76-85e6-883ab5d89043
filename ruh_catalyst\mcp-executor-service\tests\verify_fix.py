#!/usr/bin/env python3
"""
Simple verification script for SSH host key verification fix.
"""

import sys
import os

def verify_imports():
    """Verify that all imports work correctly."""
    try:
        from app.services.ssh_manager import (
            GlobalSSHKeyManager, 
            get_global_ssh_manager,
            setup_global_ssh_host_verification_async,
            initialize_global_ssh_key
        )
        print("✅ SSH manager imports successful")
        
        from app.core_.client import MCPClient
        print("✅ MCP client import successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def verify_ssh_manager_functionality():
    """Verify SSH manager basic functionality."""
    try:
        from app.services.ssh_manager import get_global_ssh_manager
        
        # Test manager creation
        manager = get_global_ssh_manager()
        print("✅ Global SSH manager created")
        
        # Test setting connection details
        manager.set_ssh_connection_details("test.example.com", "testuser")
        print("✅ SSH connection details set")
        
        # Test that the details are stored
        if manager._ssh_host == "test.example.com" and manager._ssh_user == "testuser":
            print("✅ SSH connection details stored correctly")
        else:
            print("❌ SSH connection details not stored correctly")
            return False
        
        return True
    except Exception as e:
        print(f"❌ SSH manager functionality test failed: {e}")
        return False

def verify_method_signatures():
    """Verify that all new methods have correct signatures."""
    try:
        from app.services.ssh_manager import GlobalSSHKeyManager
        import inspect
        
        manager = GlobalSSHKeyManager()
        
        # Check setup_host_key_verification method
        if hasattr(manager, 'setup_host_key_verification'):
            sig = inspect.signature(manager.setup_host_key_verification)
            params = list(sig.parameters.keys())
            if 'ssh_host' in params and 'ssh_user' in params:
                print("✅ setup_host_key_verification method signature correct")
            else:
                print("❌ setup_host_key_verification method signature incorrect")
                return False
        else:
            print("❌ setup_host_key_verification method not found")
            return False
        
        # Check set_ssh_connection_details method
        if hasattr(manager, 'set_ssh_connection_details'):
            sig = inspect.signature(manager.set_ssh_connection_details)
            params = list(sig.parameters.keys())
            if 'ssh_host' in params and 'ssh_user' in params:
                print("✅ set_ssh_connection_details method signature correct")
            else:
                print("❌ set_ssh_connection_details method signature incorrect")
                return False
        else:
            print("❌ set_ssh_connection_details method not found")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Method signature verification failed: {e}")
        return False

def verify_client_integration():
    """Verify that the client has the new SSH setup method."""
    try:
        from app.core_.client import MCPClient
        import inspect
        
        # Check if _setup_ssh_host_verification method exists
        if hasattr(MCPClient, '_setup_ssh_host_verification'):
            print("✅ _setup_ssh_host_verification method found in MCPClient")
        else:
            print("❌ _setup_ssh_host_verification method not found in MCPClient")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Client integration verification failed: {e}")
        return False

def main():
    """Run all verification tests."""
    print("🔧 Verifying SSH Host Key Verification Fix")
    print("=" * 50)
    
    tests = [
        ("Import Verification", verify_imports),
        ("SSH Manager Functionality", verify_ssh_manager_functionality),
        ("Method Signatures", verify_method_signatures),
        ("Client Integration", verify_client_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n📊 Verification Results")
    print("=" * 30)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All verification tests passed!")
        print("💡 The SSH host key verification fix is properly implemented.")
        print("🚀 The fix should resolve the 'Host key verification failed' error.")
    else:
        print("❌ Some verification tests failed.")
        print("🔧 Please check the implementation and fix any issues.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
