from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


# Enums matching the proto definitions exactly
class EventTypeEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    USAGE = "usage"
    RATING = "rating"
    CREATION = "creation"
    MODIFICATION = "modification"
    DELETION = "deletion"
    LOGIN = "login"
    LOGOUT = "logout"
    ERROR = "error"
    OTHER = "other"


class ServiceTypeEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    MCP = "mcp"
    WORKFLOW = "workflow"
    AGENT = "agent"
    USER = "user"
    APPLICATION = "application"
    WEBHOOK = "webhook"
    OTHER = "other"


class ApplicationStatusEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class WebhookStatusEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    ACTIVE = "active"
    INACTIVE = "inactive"
    FAILED = "failed"


class WebhookEventTypeEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    AGENT_EXECUTION = "agent_execution"
    AGENT_INVOKED = "agent_invoked"
    WORKFLOW_COMPLETION = "workflow_completion"
    WORKFLOW_EXECUTED = "workflow_executed"
    MCP_USAGE = "mcp_usage"
    MCP_USED = "mcp_used"
    USER_ACTIVATION = "user_activation"
    USER_SIGNUP = "user_signup"
    USER_ACTIVATED = "user_activated"
    API_USAGE = "api_usage"
    ERROR = "error"


class ActivationEventTypeEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    USER_SIGNUP = "user_signup"
    FIRST_AGENT_CREATED = "first_agent_created"
    FIRST_WORKFLOW_CREATED = "first_workflow_created"
    FIRST_MCP_REGISTERED = "first_mcp_registered"
    FIRST_API_CALL = "first_api_call"
    MARKETPLACE_USAGE = "marketplace_usage"


class RequestTypeEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    API_REQUEST = "api_request"
    WORKFLOW_EXEC = "workflow_exec"
    AUTH_EVENT = "auth_event"
    AGENT_INVOKE = "agent_invoke"
    MCP_REQUEST = "mcp_request"


class RequestStatusEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    TIMEOUT = "timeout"


class CreditCategoryEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    AGENTS = "agents"
    WORKFLOWS = "workflows"
    CUSTOM_MCPS = "custom_mcps"
    APP_CREDITS = "app_credits"
    OTHER = "other"


# Base models for requests and responses
class BaseResponse(BaseModel):
    success: bool
    message: str


# Event tracking models
class TrackEventRequest(BaseModel):
    event_type: EventTypeEnum
    service_type: ServiceTypeEnum
    entity_id: str
    user_id: str
    metadata: Optional[str] = None


class TrackEventResponse(BaseResponse):
    event_id: Optional[str] = None


# Service metrics models
class TimeSeriesDataPoint(BaseModel):
    date: str
    count: int


class ServiceMetricsRequest(BaseModel):
    service_type: ServiceTypeEnum
    entity_id: str
    time_period_days: Optional[int] = 0


class ServiceMetrics(BaseModel):
    entity_id: str
    service_type: str
    usage_count: int
    average_rating: float
    rating_count: int
    usage_time_series: List[TimeSeriesDataPoint] = Field(default_factory=list)


class ServiceMetricsResponse(BaseResponse):
    metrics: Optional[ServiceMetrics] = None


# User activity models
class UserActivityRequest(BaseModel):
    user_id: str
    time_period_days: Optional[int] = 0


class UserActivity(BaseModel):
    user_id: str
    mcp_usage_count: int
    workflow_usage_count: int
    agent_usage_count: int
    mcp_creation_count: int
    workflow_creation_count: int
    agent_creation_count: int
    last_activity_date: str


class UserActivityResponse(BaseResponse):
    activity: Optional[UserActivity] = None


# Rating analytics models
class RatingAnalyticsRequest(BaseModel):
    service_type: ServiceTypeEnum
    entity_id: Optional[str] = None
    time_period_days: Optional[int] = 0


class RatingAnalytics(BaseModel):
    service_type: str
    entity_id: str
    average_rating: float
    rating_count: int
    rating_distribution: Dict[str, int] = Field(default_factory=dict)


class RatingAnalyticsResponse(BaseResponse):
    analytics: Optional[RatingAnalytics] = None


# Overview analytics models
class OverviewAnalyticsRequest(BaseModel):
    user_id: str
    time_period_days: Optional[int] = 0
    application_id: Optional[str] = None


class CreditUsage(BaseModel):
    total_credits_used: float
    credits_remaining: float
    credits_limit: float
    daily_average: float
    projected_monthly: float


class RequestMetrics(BaseModel):
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    request_trend: List[TimeSeriesDataPoint] = Field(default_factory=list)


class RecentActivity(BaseModel):
    activity_id: str
    activity_type: str
    entity_id: str
    entity_name: str
    status: str
    timestamp: str
    metadata: str


class OverviewAnalytics(BaseModel):
    credit_usage: CreditUsage
    active_agents_count: int
    active_workflows_count: int
    active_mcps_count: int
    applications_count: int
    request_metrics: RequestMetrics
    recent_activities: List[RecentActivity] = Field(default_factory=list)
    usage_trend: List[TimeSeriesDataPoint] = Field(default_factory=list)
    credit_trend: List[TimeSeriesDataPoint] = Field(default_factory=list)


class OverviewAnalyticsResponse(BaseResponse):
    analytics: Optional[OverviewAnalytics] = None


# Activation tracking models
class TrackActivationRequest(BaseModel):
    user_id: str
    event_type: ActivationEventTypeEnum
    entity_id: Optional[str] = None
    metadata: Optional[str] = None


class TrackActivationResponse(BaseResponse):
    activation_id: Optional[str] = None


class ActivationMetricsRequest(BaseModel):
    user_id: Optional[str] = None
    time_period_days: Optional[int] = 0


class ActivationMetrics(BaseModel):
    user_id: str
    total_signups: int
    activated_users: int
    activation_rate: float
    activation_funnel: Dict[str, int] = Field(default_factory=dict)
    activation_trend: List[TimeSeriesDataPoint] = Field(default_factory=list)


class ActivationMetricsResponse(BaseResponse):
    metrics: Optional[ActivationMetrics] = None


# Webhook models
class CreateWebhookRequest(BaseModel):
    user_id: str
    url: str
    event_types: List[WebhookEventTypeEnum]
    application_id: Optional[str] = None
    secret: Optional[str] = None
    description: Optional[str] = None
    is_active: bool = True


class UpdateWebhookRequest(BaseModel):
    webhook_id: str
    user_id: str
    url: str
    event_types: List[WebhookEventTypeEnum]
    secret: Optional[str] = None
    description: Optional[str] = None
    is_active: bool = True


class Webhook(BaseModel):
    id: str
    user_id: str
    application_id: str
    url: str
    event_types: List[WebhookEventTypeEnum]
    description: str
    is_active: bool
    status: WebhookStatusEnum
    created_at: str
    updated_at: str
    delivery_count: int = 0
    failure_count: int = 0
    last_delivery_at: str = ""


class WebhookResponse(BaseResponse):
    webhook: Optional[Webhook] = None


class GetWebhooksRequest(BaseModel):
    user_id: str
    application_id: Optional[str] = None
    status: Optional[WebhookStatusEnum] = None
    limit: int = 50
    offset: int = 0


class GetWebhooksResponse(BaseResponse):
    webhooks: List[Webhook] = Field(default_factory=list)
    total_count: int = 0


class DeleteWebhookRequest(BaseModel):
    webhook_id: str
    user_id: str


class WebhookLog(BaseModel):
    id: str
    webhook_id: str
    event_type: WebhookEventTypeEnum
    payload: str
    response_status: int
    response_body: str
    error_message: str
    delivered_at: str = ""
    success: bool = False
    retry_count: int = 0


class GetWebhookLogsRequest(BaseModel):
    webhook_id: str
    user_id: str
    time_period_days: Optional[int] = 0
    limit: int = 50
    offset: int = 0


class GetWebhookLogsResponse(BaseResponse):
    logs: List[WebhookLog] = Field(default_factory=list)
    total_count: int = 0


# Dashboard models
class DashboardOverviewRequest(BaseModel):
    user_id: str
    time_period_days: int = 7


class DashboardTimeSeriesPoint(BaseModel):
    timestamp: str
    value: float


class AgentPerformanceDataPoint(BaseModel):
    timestamp: str
    requests: int
    completion_rate: float


class RecentEvent(BaseModel):
    type: str
    endpoint: str
    status: str
    timestamp: str
    duration: str
    user: str


class DashboardOverview(BaseModel):
    user_id: str
    active_agents: int
    credit_usage: float
    agent_requests: int
    workflow_requests: int
    custom_mcps: int
    credit_breakdown: Dict[str, float] = Field(default_factory=dict)
    app_credit_usage: List[DashboardTimeSeriesPoint] = Field(default_factory=list)
    agent_performance: List[AgentPerformanceDataPoint] = Field(default_factory=list)
    recent_events: List[RecentEvent] = Field(default_factory=list)


class DashboardOverviewResponse(BaseResponse):
    overview: Optional[DashboardOverview] = None


# Enhanced dashboard models
class DashboardMetricsRequest(BaseModel):
    user_id: Optional[str] = None
    days: int = 7


class DashboardMetrics(BaseModel):
    active_agents: int
    credit_usage: float
    agent_requests: int
    workflow_requests: int
    custom_mcps: int
    credit_usage_change: float
    agent_requests_change_pct: float
    workflow_requests_change_pct: float
    custom_mcps_change: int
    total_cost: float


class DashboardMetricsResponse(BaseResponse):
    metrics: Optional[DashboardMetrics] = None


class CreditUsageBreakdownRequest(BaseModel):
    user_id: Optional[str] = None
    days: int = 7


class CreditUsageBreakdownItem(BaseModel):
    category: str
    credits_used: float
    cost: float
    request_count: int


class CreditUsageBreakdownResponse(BaseResponse):
    breakdown: List[CreditUsageBreakdownItem] = Field(default_factory=list)


class AppCreditUsageRequest(BaseModel):
    user_id: Optional[str] = None
    application_id: Optional[str] = None
    days: int = 7


class AppCreditTimeSeriesPoint(BaseModel):
    timestamp: str
    credits_used: float
    cost: float
    cumulative_credits: float
    cumulative_cost: float


class AppCreditUsageData(BaseModel):
    total_credits: float
    total_cost: float
    timeseries: List[AppCreditTimeSeriesPoint] = Field(default_factory=list)


class AppCreditUsageResponse(BaseResponse):
    data: Optional[AppCreditUsageData] = None


class LatestApiRequestsRequest(BaseModel):
    user_id: Optional[str] = None
    limit: int = 50


class ApiRequestEventItem(BaseModel):
    id: str
    type: str
    endpoint: str
    method: str
    status: str
    timestamp: str
    duration_ms: int
    user_email: str
    error_message: str
    credits_used: float
    cost: float


class LatestApiRequestsResponse(BaseResponse):
    events: List[ApiRequestEventItem] = Field(default_factory=list)


class AgentPerformanceRequest(BaseModel):
    user_id: Optional[str] = None
    days: int = 7


class AgentPerformanceItem(BaseModel):
    agent_id: str
    agent_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    success_rate: float
    avg_response_time_ms: float
    total_credits_used: float
    total_cost: float
    is_active: bool


class AgentPerformanceResponse(BaseResponse):
    performance: List[AgentPerformanceItem] = Field(default_factory=list)


class WorkflowUtilizationRequest(BaseModel):
    user_id: Optional[str] = None
    days: int = 7


class WorkflowUtilizationItem(BaseModel):
    workflow_id: str
    workflow_name: str
    total_executions: int
    successful_executions: int
    failed_executions: int
    success_rate: float
    avg_execution_time_ms: float
    completion_rate_pct: float
    total_credits_used: float
    total_cost: float


class WorkflowUtilizationResponse(BaseResponse):
    utilization: List[WorkflowUtilizationItem] = Field(default_factory=list)


class SystemActivityRequest(BaseModel):
    user_id: Optional[str] = None
    limit: int = 10


class SystemActivityItem(BaseModel):
    id: str
    activity_type: str
    title: str
    description: str
    severity: str
    status: str
    timestamp: str
    user_id: str
    customer_id: str
    metadata: str


class SystemActivityResponse(BaseResponse):
    activities: List[SystemActivityItem] = Field(default_factory=list)


# Recording models
class RecordApiRequestRequest(BaseModel):
    request_type: RequestTypeEnum
    endpoint: str
    status: RequestStatusEnum
    duration_ms: int
    user_id: str
    user_email: str
    method: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    request_data: Optional[str] = None
    response_data: Optional[str] = None
    error_message: Optional[str] = None
    agent_id: Optional[str] = None
    workflow_id: Optional[str] = None
    application_id: Optional[str] = None
    credits_used: float = 0.0
    cost: float = 0.0


class RecordApiRequestResponse(BaseResponse):
    event_id: Optional[str] = None


class RecordSystemActivityRequest(BaseModel):
    activity_type: str
    title: str
    description: str
    severity: str
    status: str
    user_id: Optional[str] = None
    customer_id: Optional[str] = None
    metadata: Optional[str] = None


class RecordSystemActivityResponse(BaseResponse):
    activity_id: Optional[str] = None
