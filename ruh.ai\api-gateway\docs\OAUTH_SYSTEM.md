# Generalized OAuth System

The API Gateway now supports a generalized OAuth system that can handle multiple OAuth providers with dynamic scope resolution. This system is designed to be extensible and reusable for any OAuth provider.

## Features

- **Multiple OAuth Providers**: Support for Google, Microsoft, GitHub, and custom providers
- **Dynamic Scope Resolution**: Automatic scope mapping based on tool requirements
- **Custom Scope Support**: Override default scopes with custom ones
- **Secure State Management**: Redis-based state storage with expiration
- **Provider-Agnostic API**: Unified API endpoints for all providers
- **Extensible Configuration**: Easy addition of new providers and tools

## Supported Providers

### Built-in Providers

1. **Google OAuth**
   - Calendar API
   - Sheets API
   - Drive API
   - Gmail API (configurable)

2. **Microsoft OAuth**
   - Outlook Calendar
   - OneDrive
   - Teams (configurable)

3. **GitHub OAuth**
   - Repository access
   - User information

### Custom Providers

You can add custom OAuth providers by configuring them in the `CUSTOM_OAUTH_PROVIDERS` environment variable.

## Configuration

### Environment Variables

```bash
# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Microsoft OAuth
MICROSOFT_CLIENT_ID="your-microsoft-client-id"
MICROSOFT_CLIENT_SECRET="your-microsoft-client-secret"
MICROSOFT_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# GitHub OAuth
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
GITHUB_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Custom OAuth Providers (JSON format)
CUSTOM_OAUTH_PROVIDERS='[{"provider": "custom", "client_id": "...", "client_secret": "...", "auth_url": "...", "token_url": "..."}]'
```

### Tool Scope Mappings

The system automatically maps tools to their required scopes:

```python
# Google Calendar
google_calendar: [
    "https://www.googleapis.com/auth/calendar",
    "openid", "profile", "email"
]

# Microsoft Calendar
microsoft_calendar: [
    "https://graph.microsoft.com/calendars.readwrite",
    "openid", "profile", "email"
]

# GitHub Repositories
github_repos: [
    "repo", "user:email"
]
```

## API Endpoints

### 1. List Available Providers

```http
GET /api/v1/oauth/providers
```

Returns all configured OAuth providers and their supported tools.

### 2. Get Tool Scope Requirements

```http
GET /api/v1/oauth/tools/{tool_name}/scopes
```

Returns the required scopes for a specific tool across all providers.

### 3. Initiate OAuth Flow

```http
POST /api/v1/oauth/authorize?provider=google&mcp_id=123&tool_name=google_calendar&scopes=custom,scopes
```

Parameters:

- `provider`: OAuth provider (google, microsoft, github)
- `mcp_id`: MCP module ID
- `tool_name`: Tool name
- `scopes`: Optional custom scopes (comma-separated)

### 4. OAuth Callback

```http
GET /api/v1/oauth/callback?code=...&state=...
```

Handles the OAuth callback from any provider.

### 5. Retrieve Credentials

```http
GET /api/v1/oauth/credentials?mcp_id=123&tool_name=google_calendar&provider=google
```

Retrieves stored OAuth credentials for a user.

### 6. Server Credential Access

```http
GET /api/v1/oauth/server/credentials?user_id=123&mcp_id=456&tool_name=google_calendar&provider=google
```

Server-to-server credential retrieval with authentication.

## Usage Examples

### Frontend Integration

```javascript
// Get available providers
const providers = await fetch('/api/v1/oauth/providers').then(r => r.json());

// Initiate OAuth flow
window.location.href = `/api/v1/oauth/authorize?provider=google&mcp_id=123&tool_name=google_calendar`;

// Get credentials
const credentials = await fetch('/api/v1/oauth/credentials?mcp_id=123&tool_name=google_calendar&provider=google')
    .then(r => r.json());
```

### Backend Integration

```python
# Get credentials for a tool
async def get_tool_credentials(user_id: str, tool_name: str, provider: str = None):
    # Use the generalized OAuth service
    result = oauth_service.get_oauth_credentials(
        db=db,
        user_id=user_id,
        mcp_id="your_mcp_id",
        tool_name=tool_name,
        provider=OAuthProvider(provider) if provider else None
    )
    return result
```

## Adding New Providers

### 1. Built-in Provider

Add to `oauth_providers.py`:

```python
# Add to _initialize_default_providers
self._providers[OAuthProvider.NEWPROVIDER] = OAuthProviderConfig(
    provider=OAuthProvider.NEWPROVIDER,
    client_id="",
    client_secret="",
    redirect_uri="",
    auth_url="https://provider.com/oauth/authorize",
    token_url="https://provider.com/oauth/token",
    user_info_url="https://provider.com/user",
)
```

### 2. Custom Provider via Environment

```bash
CUSTOM_OAUTH_PROVIDERS='[{
    "provider": "custom",
    "client_id": "your-client-id",
    "client_secret": "your-client-secret",
    "redirect_uri": "http://localhost:8000/api/v1/oauth/callback",
    "auth_url": "https://custom-provider.com/oauth/authorize",
    "token_url": "https://custom-provider.com/oauth/token",
    "user_info_url": "https://custom-provider.com/user"
}]'
```

## Adding New Tools

Add tool scope mappings in `oauth_providers.py`:

```python
self._tool_scopes["new_tool"] = ToolScopeMapping(
    tool_name="new_tool",
    provider_scopes={
        OAuthProvider.GOOGLE: ["https://www.googleapis.com/auth/new-scope"],
        OAuthProvider.MICROSOFT: ["https://graph.microsoft.com/new-scope"],
    },
    description="New tool description"
)
```

## Database Schema

The OAuth credentials table includes:

- `provider`: OAuth provider identifier
- `scopes`: JSON array of granted scopes
- `composite_key`: Format `{user_id}_{mcp_id}_{tool_name}_{provider}`

## Security Features

- **State Validation**: Secure state tokens with Redis expiration
- **Scope Validation**: Automatic scope validation per tool
- **Credential Encryption**: Secure storage in Google Secret Manager
- **Access Control**: Role-based access to OAuth endpoints

## Migration

Run the migration script to update existing databases:

```sql
-- See migrations/add_oauth_provider_support.sql
```

## Backward Compatibility

The system maintains backward compatibility with existing Google OAuth implementations while adding support for multiple providers.
