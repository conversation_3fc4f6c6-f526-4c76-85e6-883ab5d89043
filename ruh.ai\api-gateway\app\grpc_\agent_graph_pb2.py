# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: agent_graph.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'agent_graph.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x61gent_graph.proto\x12\x0b\x61gent_graph\">\n#GetAllAgentsFromOrganisationRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"7\n\x1eGetAgentsFromDepartmentRequest\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\"@\n\x1b\x43heckUserAgentAccessRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x02 \x01(\t\":\n\x13\x43heckAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x12\n\nhas_access\x18\x02 \x01(\x08\"\x13\n\x05Owner\x12\n\n\x02id\x18\x01 \x01(\t\"\x9a\x02\n\x12\x43reateAgentRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x12\n\ndepartment\x18\x03 \x01(\t\x12!\n\x05owner\x18\x04 \x01(\x0b\x32\x12.agent_graph.Owner\x12\x10\n\x08user_ids\x18\x05 \x03(\t\x12\x0f\n\x02id\x18\x06 \x01(\tH\x00\x88\x01\x01\x12+\n\nvisibility\x18\x07 \x01(\x0e\x32\x17.agent_graph.Visibility\x12#\n\x06status\x18\x08 \x01(\x0e\x32\x13.agent_graph.Status\x12.\n\x0c\x63reator_role\x18\t \x01(\x0e\x32\x18.agent_graph.CreatorRoleB\x05\n\x03_id\"7\n\x13\x43reateAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x1d\n\x0fGetAgentRequest\x12\n\n\x02id\x18\x01 \x01(\t\"T\n\rAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12!\n\x05\x61gent\x18\x03 \x01(\x0b\x32\x12.agent_graph.Agent\"N\n\x19ListAgentsByUserIdRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\"\xac\x02\n\x05\x41gent\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x10\n\x08owner_id\x18\x05 \x01(\t\x12\x12\n\nowner_name\x18\x06 \x01(\t\x12\x10\n\x08user_ids\x18\x07 \x03(\t\x12\x12\n\ncreated_at\x18\x08 \x01(\t\x12\x12\n\nupdated_at\x18\t \x01(\t\x12+\n\nvisibility\x18\n \x01(\x0e\x32\x17.agent_graph.Visibility\x12#\n\x06status\x18\x0b \x01(\x0e\x32\x13.agent_graph.Status\x12.\n\x0c\x63reator_role\x18\x0c \x01(\x0e\x32\x18.agent_graph.CreatorRole\"{\n\x12ListAgentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\"\n\x06\x61gents\x18\x02 \x03(\x0b\x32\x12.agent_graph.Agent\x12\r\n\x05total\x18\x03 \x01(\x05\x12\x0c\n\x04page\x18\x04 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x05 \x01(\x05*%\n\nVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*-\n\x06Status\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\x0c\n\x08INACTIVE\x10\x01\x12\t\n\x05\x42\x45NCH\x10\x02*2\n\x0b\x43reatorRole\x12\n\n\x06MEMBER\x10\x00\x12\x0b\n\x07\x43REATOR\x10\x01\x12\n\n\x06VIEWER\x10\x02\x32\xd6\x04\n\x11\x41gentGraphService\x12R\n\x0b\x63reateAgent\x12\x1f.agent_graph.CreateAgentRequest\x1a .agent_graph.CreateAgentResponse\"\x00\x12\x46\n\x08getAgent\x12\x1c.agent_graph.GetAgentRequest\x1a\x1a.agent_graph.AgentResponse\"\x00\x12_\n\x12listAgentsByUserId\x12&.agent_graph.ListAgentsByUserIdRequest\x1a\x1f.agent_graph.ListAgentsResponse\"\x00\x12s\n\x1cgetAllAgentsFromOrganisation\x12\x30.agent_graph.GetAllAgentsFromOrganisationRequest\x1a\x1f.agent_graph.ListAgentsResponse\"\x00\x12i\n\x17getAgentsFromDepartment\x12+.agent_graph.GetAgentsFromDepartmentRequest\x1a\x1f.agent_graph.ListAgentsResponse\"\x00\x12\x64\n\x14\x63heckUserAgentAccess\x12(.agent_graph.CheckUserAgentAccessRequest\x1a .agent_graph.CheckAccessResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'agent_graph_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_VISIBILITY']._serialized_start=1269
  _globals['_VISIBILITY']._serialized_end=1306
  _globals['_STATUS']._serialized_start=1308
  _globals['_STATUS']._serialized_end=1353
  _globals['_CREATORROLE']._serialized_start=1355
  _globals['_CREATORROLE']._serialized_end=1405
  _globals['_GETALLAGENTSFROMORGANISATIONREQUEST']._serialized_start=34
  _globals['_GETALLAGENTSFROMORGANISATIONREQUEST']._serialized_end=96
  _globals['_GETAGENTSFROMDEPARTMENTREQUEST']._serialized_start=98
  _globals['_GETAGENTSFROMDEPARTMENTREQUEST']._serialized_end=153
  _globals['_CHECKUSERAGENTACCESSREQUEST']._serialized_start=155
  _globals['_CHECKUSERAGENTACCESSREQUEST']._serialized_end=219
  _globals['_CHECKACCESSRESPONSE']._serialized_start=221
  _globals['_CHECKACCESSRESPONSE']._serialized_end=279
  _globals['_OWNER']._serialized_start=281
  _globals['_OWNER']._serialized_end=300
  _globals['_CREATEAGENTREQUEST']._serialized_start=303
  _globals['_CREATEAGENTREQUEST']._serialized_end=585
  _globals['_CREATEAGENTRESPONSE']._serialized_start=587
  _globals['_CREATEAGENTRESPONSE']._serialized_end=642
  _globals['_GETAGENTREQUEST']._serialized_start=644
  _globals['_GETAGENTREQUEST']._serialized_end=673
  _globals['_AGENTRESPONSE']._serialized_start=675
  _globals['_AGENTRESPONSE']._serialized_end=759
  _globals['_LISTAGENTSBYUSERIDREQUEST']._serialized_start=761
  _globals['_LISTAGENTSBYUSERIDREQUEST']._serialized_end=839
  _globals['_AGENT']._serialized_start=842
  _globals['_AGENT']._serialized_end=1142
  _globals['_LISTAGENTSRESPONSE']._serialized_start=1144
  _globals['_LISTAGENTSRESPONSE']._serialized_end=1267
  _globals['_AGENTGRAPHSERVICE']._serialized_start=1408
  _globals['_AGENTGRAPHSERVICE']._serialized_end=2006
# @@protoc_insertion_point(module_scope)
