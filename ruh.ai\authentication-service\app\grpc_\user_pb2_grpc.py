# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import user_pb2 as user__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in user_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class UserServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.register = channel.unary_unary(
                '/user.UserService/register',
                request_serializer=user__pb2.RegisterRequest.SerializeToString,
                response_deserializer=user__pb2.RegisterResponse.FromString,
                _registered_method=True)
        self.updateEmailVerifiedDetails = channel.unary_unary(
                '/user.UserService/updateEmailVerifiedDetails',
                request_serializer=user__pb2.UpdateEmailVerificationDetails.SerializeToString,
                response_deserializer=user__pb2.UpdateEmailVerifiedDetailsResponse.FromString,
                _registered_method=True)
        self.login = channel.unary_unary(
                '/user.UserService/login',
                request_serializer=user__pb2.LoginRequest.SerializeToString,
                response_deserializer=user__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.googleOAuthLogin = channel.unary_unary(
                '/user.UserService/googleOAuthLogin',
                request_serializer=user__pb2.GoogleOAuthRequest.SerializeToString,
                response_deserializer=user__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.refreshToken = channel.unary_unary(
                '/user.UserService/refreshToken',
                request_serializer=user__pb2.RefreshTokenRequest.SerializeToString,
                response_deserializer=user__pb2.RefreshTokenResponse.FromString,
                _registered_method=True)
        self.accessToken = channel.unary_unary(
                '/user.UserService/accessToken',
                request_serializer=user__pb2.AccessTokenRequest.SerializeToString,
                response_deserializer=user__pb2.AccessTokenResponse.FromString,
                _registered_method=True)
        self.generateResetPasswordOTP = channel.unary_unary(
                '/user.UserService/generateResetPasswordOTP',
                request_serializer=user__pb2.ResetPasswordOTPRequest.SerializeToString,
                response_deserializer=user__pb2.ResetPasswordOTPResponse.FromString,
                _registered_method=True)
        self.updatePassword = channel.unary_unary(
                '/user.UserService/updatePassword',
                request_serializer=user__pb2.UpdatePasswordRequest.SerializeToString,
                response_deserializer=user__pb2.UpdatePasswordResponse.FromString,
                _registered_method=True)
        self.resetPassword = channel.unary_unary(
                '/user.UserService/resetPassword',
                request_serializer=user__pb2.ResetPasswordRequest.SerializeToString,
                response_deserializer=user__pb2.ResetPasswordResponse.FromString,
                _registered_method=True)
        self.updateUserProfileDetails = channel.unary_unary(
                '/user.UserService/updateUserProfileDetails',
                request_serializer=user__pb2.UpdateUserProfileDetailsRequest.SerializeToString,
                response_deserializer=user__pb2.UserResponse.FromString,
                _registered_method=True)
        self.getUser = channel.unary_unary(
                '/user.UserService/getUser',
                request_serializer=user__pb2.GetUserRequest.SerializeToString,
                response_deserializer=user__pb2.UserResponse.FromString,
                _registered_method=True)
        self.updateUser = channel.unary_unary(
                '/user.UserService/updateUser',
                request_serializer=user__pb2.UpdateUserRequest.SerializeToString,
                response_deserializer=user__pb2.UserResponse.FromString,
                _registered_method=True)
        self.deleteUser = channel.unary_unary(
                '/user.UserService/deleteUser',
                request_serializer=user__pb2.DeleteUserRequest.SerializeToString,
                response_deserializer=user__pb2.DeleteUserResponse.FromString,
                _registered_method=True)
        self.listUsers = channel.unary_unary(
                '/user.UserService/listUsers',
                request_serializer=user__pb2.ListUsersRequest.SerializeToString,
                response_deserializer=user__pb2.ListUsersResponse.FromString,
                _registered_method=True)
        self.searchUsers = channel.unary_unary(
                '/user.UserService/searchUsers',
                request_serializer=user__pb2.SearchUsersRequest.SerializeToString,
                response_deserializer=user__pb2.ListUsersResponse.FromString,
                _registered_method=True)
        self.updateStripeCustomerId = channel.unary_unary(
                '/user.UserService/updateStripeCustomerId',
                request_serializer=user__pb2.UpdateStripeCustomerIdRequest.SerializeToString,
                response_deserializer=user__pb2.UpdateStripeCustomerIdResponse.FromString,
                _registered_method=True)
        self.fetchStripeCustomerId = channel.unary_unary(
                '/user.UserService/fetchStripeCustomerId',
                request_serializer=user__pb2.FetchStripeCustomerIdRequest.SerializeToString,
                response_deserializer=user__pb2.FetchStripeCustomerIdResponse.FromString,
                _registered_method=True)
        self.addToWaitlist = channel.unary_unary(
                '/user.UserService/addToWaitlist',
                request_serializer=user__pb2.AddToWaitlistRequest.SerializeToString,
                response_deserializer=user__pb2.AddToWaitlistResponse.FromString,
                _registered_method=True)
        self.getWaitlist = channel.unary_unary(
                '/user.UserService/getWaitlist',
                request_serializer=user__pb2.GetWaitlistRequest.SerializeToString,
                response_deserializer=user__pb2.GetWaitlistResponse.FromString,
                _registered_method=True)
        self.approveWaitlistUser = channel.unary_unary(
                '/user.UserService/approveWaitlistUser',
                request_serializer=user__pb2.ApproveWaitlistUserRequest.SerializeToString,
                response_deserializer=user__pb2.ApproveWaitlistUserResponse.FromString,
                _registered_method=True)
        self.approveMultipleWaitlistUsers = channel.unary_unary(
                '/user.UserService/approveMultipleWaitlistUsers',
                request_serializer=user__pb2.ApproveMultipleWaitlistUsersRequest.SerializeToString,
                response_deserializer=user__pb2.ApproveMultipleWaitlistUsersResponse.FromString,
                _registered_method=True)
        self.validateUser = channel.unary_unary(
                '/user.UserService/validateUser',
                request_serializer=user__pb2.ValidateUserRequest.SerializeToString,
                response_deserializer=user__pb2.ValidateUserResponse.FromString,
                _registered_method=True)
        self.getUsersByIds = channel.unary_unary(
                '/user.UserService/getUsersByIds',
                request_serializer=user__pb2.GetUsersByIdsRequest.SerializeToString,
                response_deserializer=user__pb2.GetUsersByIdsResponse.FromString,
                _registered_method=True)
        self.GenerateAPIKey = channel.unary_unary(
                '/user.UserService/GenerateAPIKey',
                request_serializer=user__pb2.GenerateAPIKeyRequest.SerializeToString,
                response_deserializer=user__pb2.GenerateAPIKeyResponse.FromString,
                _registered_method=True)
        self.ListAPIKeys = channel.unary_unary(
                '/user.UserService/ListAPIKeys',
                request_serializer=user__pb2.ListAPIKeysRequest.SerializeToString,
                response_deserializer=user__pb2.ListAPIKeysResponse.FromString,
                _registered_method=True)
        self.DeleteAPIKey = channel.unary_unary(
                '/user.UserService/DeleteAPIKey',
                request_serializer=user__pb2.DeleteAPIKeyRequest.SerializeToString,
                response_deserializer=user__pb2.DeleteAPIKeyResponse.FromString,
                _registered_method=True)
        self.GetAPIKeyById = channel.unary_unary(
                '/user.UserService/GetAPIKeyById',
                request_serializer=user__pb2.GetAPIKeyByIdRequest.SerializeToString,
                response_deserializer=user__pb2.GetAPIKeyByIdResponse.FromString,
                _registered_method=True)
        self.getAllUsers = channel.unary_unary(
                '/user.UserService/getAllUsers',
                request_serializer=user__pb2.GetAllUsersRequest.SerializeToString,
                response_deserializer=user__pb2.GetAllUsersResponse.FromString,
                _registered_method=True)
        self.createOrganization = channel.unary_unary(
                '/user.UserService/createOrganization',
                request_serializer=user__pb2.CreateOrganizationRequest.SerializeToString,
                response_deserializer=user__pb2.OrganizationResponse.FromString,
                _registered_method=True)
        self.getOrganization = channel.unary_unary(
                '/user.UserService/getOrganization',
                request_serializer=user__pb2.GetOrganizationRequest.SerializeToString,
                response_deserializer=user__pb2.OrganizationResponse.FromString,
                _registered_method=True)
        self.updateOrganization = channel.unary_unary(
                '/user.UserService/updateOrganization',
                request_serializer=user__pb2.UpdateOrganizationRequest.SerializeToString,
                response_deserializer=user__pb2.OrganizationResponse.FromString,
                _registered_method=True)
        self.deleteOrganization = channel.unary_unary(
                '/user.UserService/deleteOrganization',
                request_serializer=user__pb2.DeleteOrganizationRequest.SerializeToString,
                response_deserializer=user__pb2.DeleteOrganizationResponse.FromString,
                _registered_method=True)
        self.addUserToOrganization = channel.unary_unary(
                '/user.UserService/addUserToOrganization',
                request_serializer=user__pb2.AddUserToOrganizationRequest.SerializeToString,
                response_deserializer=user__pb2.OrganizationResponse.FromString,
                _registered_method=True)
        self.removeUserFromOrganization = channel.unary_unary(
                '/user.UserService/removeUserFromOrganization',
                request_serializer=user__pb2.RemoveUserFromOrganizationRequest.SerializeToString,
                response_deserializer=user__pb2.OrganizationResponse.FromString,
                _registered_method=True)
        self.listUserOrganizations = channel.unary_unary(
                '/user.UserService/listUserOrganizations',
                request_serializer=user__pb2.ListUserOrganizationsRequest.SerializeToString,
                response_deserializer=user__pb2.ListOrganizationsResponse.FromString,
                _registered_method=True)
        self.CreateCredential = channel.unary_unary(
                '/user.UserService/CreateCredential',
                request_serializer=user__pb2.CreateCredentialRequest.SerializeToString,
                response_deserializer=user__pb2.CreateCredentialResponse.FromString,
                _registered_method=True)
        self.GetCredential = channel.unary_unary(
                '/user.UserService/GetCredential',
                request_serializer=user__pb2.GetCredentialRequest.SerializeToString,
                response_deserializer=user__pb2.GetCredentialResponse.FromString,
                _registered_method=True)
        self.ListCredentials = channel.unary_unary(
                '/user.UserService/ListCredentials',
                request_serializer=user__pb2.ListCredentialsRequest.SerializeToString,
                response_deserializer=user__pb2.ListCredentialsResponse.FromString,
                _registered_method=True)
        self.DeleteCredential = channel.unary_unary(
                '/user.UserService/DeleteCredential',
                request_serializer=user__pb2.DeleteCredentialRequest.SerializeToString,
                response_deserializer=user__pb2.DeleteCredentialResponse.FromString,
                _registered_method=True)
        self.UpdateCredential = channel.unary_unary(
                '/user.UserService/UpdateCredential',
                request_serializer=user__pb2.UpdateCredentialRequest.SerializeToString,
                response_deserializer=user__pb2.UpdateCredentialResponse.FromString,
                _registered_method=True)
        self.updateUserGitToken = channel.unary_unary(
                '/user.UserService/updateUserGitToken',
                request_serializer=user__pb2.UpdateUserGitToken.SerializeToString,
                response_deserializer=user__pb2.UserResponse.FromString,
                _registered_method=True)
        self.getUserGitHubToken = channel.unary_unary(
                '/user.UserService/getUserGitHubToken',
                request_serializer=user__pb2.GetUserGitHubTokenRequest.SerializeToString,
                response_deserializer=user__pb2.GetUserGitHubTokenResponse.FromString,
                _registered_method=True)


class UserServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def register(self, request, context):
        """Authentication
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateEmailVerifiedDetails(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def login(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def googleOAuthLogin(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def refreshToken(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def accessToken(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def generateResetPasswordOTP(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updatePassword(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def resetPassword(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateUserProfileDetails(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getUser(self, request, context):
        """User Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listUsers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def searchUsers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateStripeCustomerId(self, request, context):
        """New RPCs for Stripe Customer ID
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def fetchStripeCustomerId(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def addToWaitlist(self, request, context):
        """--- New Waitlist RPC ---
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getWaitlist(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def approveWaitlistUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def approveMultipleWaitlistUsers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def validateUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getUsersByIds(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateAPIKey(self, request, context):
        """API Key management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAPIKeys(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAPIKey(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAPIKeyById(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAllUsers(self, request, context):
        """Admin
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createOrganization(self, request, context):
        """--- Organization RPCs ---
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getOrganization(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateOrganization(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteOrganization(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def addUserToOrganization(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def removeUserFromOrganization(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listUserOrganizations(self, request, context):
        """Lists orgs where the user is owner or member
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCredential(self, request, context):
        """Credential Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCredential(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListCredentials(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteCredential(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateCredential(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateUserGitToken(self, request, context):
        """Git Token Update
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getUserGitHubToken(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_UserServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'register': grpc.unary_unary_rpc_method_handler(
                    servicer.register,
                    request_deserializer=user__pb2.RegisterRequest.FromString,
                    response_serializer=user__pb2.RegisterResponse.SerializeToString,
            ),
            'updateEmailVerifiedDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.updateEmailVerifiedDetails,
                    request_deserializer=user__pb2.UpdateEmailVerificationDetails.FromString,
                    response_serializer=user__pb2.UpdateEmailVerifiedDetailsResponse.SerializeToString,
            ),
            'login': grpc.unary_unary_rpc_method_handler(
                    servicer.login,
                    request_deserializer=user__pb2.LoginRequest.FromString,
                    response_serializer=user__pb2.LoginResponse.SerializeToString,
            ),
            'googleOAuthLogin': grpc.unary_unary_rpc_method_handler(
                    servicer.googleOAuthLogin,
                    request_deserializer=user__pb2.GoogleOAuthRequest.FromString,
                    response_serializer=user__pb2.LoginResponse.SerializeToString,
            ),
            'refreshToken': grpc.unary_unary_rpc_method_handler(
                    servicer.refreshToken,
                    request_deserializer=user__pb2.RefreshTokenRequest.FromString,
                    response_serializer=user__pb2.RefreshTokenResponse.SerializeToString,
            ),
            'accessToken': grpc.unary_unary_rpc_method_handler(
                    servicer.accessToken,
                    request_deserializer=user__pb2.AccessTokenRequest.FromString,
                    response_serializer=user__pb2.AccessTokenResponse.SerializeToString,
            ),
            'generateResetPasswordOTP': grpc.unary_unary_rpc_method_handler(
                    servicer.generateResetPasswordOTP,
                    request_deserializer=user__pb2.ResetPasswordOTPRequest.FromString,
                    response_serializer=user__pb2.ResetPasswordOTPResponse.SerializeToString,
            ),
            'updatePassword': grpc.unary_unary_rpc_method_handler(
                    servicer.updatePassword,
                    request_deserializer=user__pb2.UpdatePasswordRequest.FromString,
                    response_serializer=user__pb2.UpdatePasswordResponse.SerializeToString,
            ),
            'resetPassword': grpc.unary_unary_rpc_method_handler(
                    servicer.resetPassword,
                    request_deserializer=user__pb2.ResetPasswordRequest.FromString,
                    response_serializer=user__pb2.ResetPasswordResponse.SerializeToString,
            ),
            'updateUserProfileDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.updateUserProfileDetails,
                    request_deserializer=user__pb2.UpdateUserProfileDetailsRequest.FromString,
                    response_serializer=user__pb2.UserResponse.SerializeToString,
            ),
            'getUser': grpc.unary_unary_rpc_method_handler(
                    servicer.getUser,
                    request_deserializer=user__pb2.GetUserRequest.FromString,
                    response_serializer=user__pb2.UserResponse.SerializeToString,
            ),
            'updateUser': grpc.unary_unary_rpc_method_handler(
                    servicer.updateUser,
                    request_deserializer=user__pb2.UpdateUserRequest.FromString,
                    response_serializer=user__pb2.UserResponse.SerializeToString,
            ),
            'deleteUser': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteUser,
                    request_deserializer=user__pb2.DeleteUserRequest.FromString,
                    response_serializer=user__pb2.DeleteUserResponse.SerializeToString,
            ),
            'listUsers': grpc.unary_unary_rpc_method_handler(
                    servicer.listUsers,
                    request_deserializer=user__pb2.ListUsersRequest.FromString,
                    response_serializer=user__pb2.ListUsersResponse.SerializeToString,
            ),
            'searchUsers': grpc.unary_unary_rpc_method_handler(
                    servicer.searchUsers,
                    request_deserializer=user__pb2.SearchUsersRequest.FromString,
                    response_serializer=user__pb2.ListUsersResponse.SerializeToString,
            ),
            'updateStripeCustomerId': grpc.unary_unary_rpc_method_handler(
                    servicer.updateStripeCustomerId,
                    request_deserializer=user__pb2.UpdateStripeCustomerIdRequest.FromString,
                    response_serializer=user__pb2.UpdateStripeCustomerIdResponse.SerializeToString,
            ),
            'fetchStripeCustomerId': grpc.unary_unary_rpc_method_handler(
                    servicer.fetchStripeCustomerId,
                    request_deserializer=user__pb2.FetchStripeCustomerIdRequest.FromString,
                    response_serializer=user__pb2.FetchStripeCustomerIdResponse.SerializeToString,
            ),
            'addToWaitlist': grpc.unary_unary_rpc_method_handler(
                    servicer.addToWaitlist,
                    request_deserializer=user__pb2.AddToWaitlistRequest.FromString,
                    response_serializer=user__pb2.AddToWaitlistResponse.SerializeToString,
            ),
            'getWaitlist': grpc.unary_unary_rpc_method_handler(
                    servicer.getWaitlist,
                    request_deserializer=user__pb2.GetWaitlistRequest.FromString,
                    response_serializer=user__pb2.GetWaitlistResponse.SerializeToString,
            ),
            'approveWaitlistUser': grpc.unary_unary_rpc_method_handler(
                    servicer.approveWaitlistUser,
                    request_deserializer=user__pb2.ApproveWaitlistUserRequest.FromString,
                    response_serializer=user__pb2.ApproveWaitlistUserResponse.SerializeToString,
            ),
            'approveMultipleWaitlistUsers': grpc.unary_unary_rpc_method_handler(
                    servicer.approveMultipleWaitlistUsers,
                    request_deserializer=user__pb2.ApproveMultipleWaitlistUsersRequest.FromString,
                    response_serializer=user__pb2.ApproveMultipleWaitlistUsersResponse.SerializeToString,
            ),
            'validateUser': grpc.unary_unary_rpc_method_handler(
                    servicer.validateUser,
                    request_deserializer=user__pb2.ValidateUserRequest.FromString,
                    response_serializer=user__pb2.ValidateUserResponse.SerializeToString,
            ),
            'getUsersByIds': grpc.unary_unary_rpc_method_handler(
                    servicer.getUsersByIds,
                    request_deserializer=user__pb2.GetUsersByIdsRequest.FromString,
                    response_serializer=user__pb2.GetUsersByIdsResponse.SerializeToString,
            ),
            'GenerateAPIKey': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateAPIKey,
                    request_deserializer=user__pb2.GenerateAPIKeyRequest.FromString,
                    response_serializer=user__pb2.GenerateAPIKeyResponse.SerializeToString,
            ),
            'ListAPIKeys': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAPIKeys,
                    request_deserializer=user__pb2.ListAPIKeysRequest.FromString,
                    response_serializer=user__pb2.ListAPIKeysResponse.SerializeToString,
            ),
            'DeleteAPIKey': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAPIKey,
                    request_deserializer=user__pb2.DeleteAPIKeyRequest.FromString,
                    response_serializer=user__pb2.DeleteAPIKeyResponse.SerializeToString,
            ),
            'GetAPIKeyById': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAPIKeyById,
                    request_deserializer=user__pb2.GetAPIKeyByIdRequest.FromString,
                    response_serializer=user__pb2.GetAPIKeyByIdResponse.SerializeToString,
            ),
            'getAllUsers': grpc.unary_unary_rpc_method_handler(
                    servicer.getAllUsers,
                    request_deserializer=user__pb2.GetAllUsersRequest.FromString,
                    response_serializer=user__pb2.GetAllUsersResponse.SerializeToString,
            ),
            'createOrganization': grpc.unary_unary_rpc_method_handler(
                    servicer.createOrganization,
                    request_deserializer=user__pb2.CreateOrganizationRequest.FromString,
                    response_serializer=user__pb2.OrganizationResponse.SerializeToString,
            ),
            'getOrganization': grpc.unary_unary_rpc_method_handler(
                    servicer.getOrganization,
                    request_deserializer=user__pb2.GetOrganizationRequest.FromString,
                    response_serializer=user__pb2.OrganizationResponse.SerializeToString,
            ),
            'updateOrganization': grpc.unary_unary_rpc_method_handler(
                    servicer.updateOrganization,
                    request_deserializer=user__pb2.UpdateOrganizationRequest.FromString,
                    response_serializer=user__pb2.OrganizationResponse.SerializeToString,
            ),
            'deleteOrganization': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteOrganization,
                    request_deserializer=user__pb2.DeleteOrganizationRequest.FromString,
                    response_serializer=user__pb2.DeleteOrganizationResponse.SerializeToString,
            ),
            'addUserToOrganization': grpc.unary_unary_rpc_method_handler(
                    servicer.addUserToOrganization,
                    request_deserializer=user__pb2.AddUserToOrganizationRequest.FromString,
                    response_serializer=user__pb2.OrganizationResponse.SerializeToString,
            ),
            'removeUserFromOrganization': grpc.unary_unary_rpc_method_handler(
                    servicer.removeUserFromOrganization,
                    request_deserializer=user__pb2.RemoveUserFromOrganizationRequest.FromString,
                    response_serializer=user__pb2.OrganizationResponse.SerializeToString,
            ),
            'listUserOrganizations': grpc.unary_unary_rpc_method_handler(
                    servicer.listUserOrganizations,
                    request_deserializer=user__pb2.ListUserOrganizationsRequest.FromString,
                    response_serializer=user__pb2.ListOrganizationsResponse.SerializeToString,
            ),
            'CreateCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCredential,
                    request_deserializer=user__pb2.CreateCredentialRequest.FromString,
                    response_serializer=user__pb2.CreateCredentialResponse.SerializeToString,
            ),
            'GetCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCredential,
                    request_deserializer=user__pb2.GetCredentialRequest.FromString,
                    response_serializer=user__pb2.GetCredentialResponse.SerializeToString,
            ),
            'ListCredentials': grpc.unary_unary_rpc_method_handler(
                    servicer.ListCredentials,
                    request_deserializer=user__pb2.ListCredentialsRequest.FromString,
                    response_serializer=user__pb2.ListCredentialsResponse.SerializeToString,
            ),
            'DeleteCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteCredential,
                    request_deserializer=user__pb2.DeleteCredentialRequest.FromString,
                    response_serializer=user__pb2.DeleteCredentialResponse.SerializeToString,
            ),
            'UpdateCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateCredential,
                    request_deserializer=user__pb2.UpdateCredentialRequest.FromString,
                    response_serializer=user__pb2.UpdateCredentialResponse.SerializeToString,
            ),
            'updateUserGitToken': grpc.unary_unary_rpc_method_handler(
                    servicer.updateUserGitToken,
                    request_deserializer=user__pb2.UpdateUserGitToken.FromString,
                    response_serializer=user__pb2.UserResponse.SerializeToString,
            ),
            'getUserGitHubToken': grpc.unary_unary_rpc_method_handler(
                    servicer.getUserGitHubToken,
                    request_deserializer=user__pb2.GetUserGitHubTokenRequest.FromString,
                    response_serializer=user__pb2.GetUserGitHubTokenResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'user.UserService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('user.UserService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class UserService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def register(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/register',
            user__pb2.RegisterRequest.SerializeToString,
            user__pb2.RegisterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateEmailVerifiedDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updateEmailVerifiedDetails',
            user__pb2.UpdateEmailVerificationDetails.SerializeToString,
            user__pb2.UpdateEmailVerifiedDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def login(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/login',
            user__pb2.LoginRequest.SerializeToString,
            user__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def googleOAuthLogin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/googleOAuthLogin',
            user__pb2.GoogleOAuthRequest.SerializeToString,
            user__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def refreshToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/refreshToken',
            user__pb2.RefreshTokenRequest.SerializeToString,
            user__pb2.RefreshTokenResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def accessToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/accessToken',
            user__pb2.AccessTokenRequest.SerializeToString,
            user__pb2.AccessTokenResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def generateResetPasswordOTP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/generateResetPasswordOTP',
            user__pb2.ResetPasswordOTPRequest.SerializeToString,
            user__pb2.ResetPasswordOTPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updatePassword(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updatePassword',
            user__pb2.UpdatePasswordRequest.SerializeToString,
            user__pb2.UpdatePasswordResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def resetPassword(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/resetPassword',
            user__pb2.ResetPasswordRequest.SerializeToString,
            user__pb2.ResetPasswordResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateUserProfileDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updateUserProfileDetails',
            user__pb2.UpdateUserProfileDetailsRequest.SerializeToString,
            user__pb2.UserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/getUser',
            user__pb2.GetUserRequest.SerializeToString,
            user__pb2.UserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updateUser',
            user__pb2.UpdateUserRequest.SerializeToString,
            user__pb2.UserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/deleteUser',
            user__pb2.DeleteUserRequest.SerializeToString,
            user__pb2.DeleteUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listUsers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/listUsers',
            user__pb2.ListUsersRequest.SerializeToString,
            user__pb2.ListUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def searchUsers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/searchUsers',
            user__pb2.SearchUsersRequest.SerializeToString,
            user__pb2.ListUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateStripeCustomerId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updateStripeCustomerId',
            user__pb2.UpdateStripeCustomerIdRequest.SerializeToString,
            user__pb2.UpdateStripeCustomerIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def fetchStripeCustomerId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/fetchStripeCustomerId',
            user__pb2.FetchStripeCustomerIdRequest.SerializeToString,
            user__pb2.FetchStripeCustomerIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def addToWaitlist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/addToWaitlist',
            user__pb2.AddToWaitlistRequest.SerializeToString,
            user__pb2.AddToWaitlistResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getWaitlist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/getWaitlist',
            user__pb2.GetWaitlistRequest.SerializeToString,
            user__pb2.GetWaitlistResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def approveWaitlistUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/approveWaitlistUser',
            user__pb2.ApproveWaitlistUserRequest.SerializeToString,
            user__pb2.ApproveWaitlistUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def approveMultipleWaitlistUsers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/approveMultipleWaitlistUsers',
            user__pb2.ApproveMultipleWaitlistUsersRequest.SerializeToString,
            user__pb2.ApproveMultipleWaitlistUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def validateUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/validateUser',
            user__pb2.ValidateUserRequest.SerializeToString,
            user__pb2.ValidateUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getUsersByIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/getUsersByIds',
            user__pb2.GetUsersByIdsRequest.SerializeToString,
            user__pb2.GetUsersByIdsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateAPIKey(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/GenerateAPIKey',
            user__pb2.GenerateAPIKeyRequest.SerializeToString,
            user__pb2.GenerateAPIKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAPIKeys(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/ListAPIKeys',
            user__pb2.ListAPIKeysRequest.SerializeToString,
            user__pb2.ListAPIKeysResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteAPIKey(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/DeleteAPIKey',
            user__pb2.DeleteAPIKeyRequest.SerializeToString,
            user__pb2.DeleteAPIKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAPIKeyById(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/GetAPIKeyById',
            user__pb2.GetAPIKeyByIdRequest.SerializeToString,
            user__pb2.GetAPIKeyByIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAllUsers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/getAllUsers',
            user__pb2.GetAllUsersRequest.SerializeToString,
            user__pb2.GetAllUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createOrganization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/createOrganization',
            user__pb2.CreateOrganizationRequest.SerializeToString,
            user__pb2.OrganizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getOrganization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/getOrganization',
            user__pb2.GetOrganizationRequest.SerializeToString,
            user__pb2.OrganizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateOrganization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updateOrganization',
            user__pb2.UpdateOrganizationRequest.SerializeToString,
            user__pb2.OrganizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteOrganization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/deleteOrganization',
            user__pb2.DeleteOrganizationRequest.SerializeToString,
            user__pb2.DeleteOrganizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def addUserToOrganization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/addUserToOrganization',
            user__pb2.AddUserToOrganizationRequest.SerializeToString,
            user__pb2.OrganizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def removeUserFromOrganization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/removeUserFromOrganization',
            user__pb2.RemoveUserFromOrganizationRequest.SerializeToString,
            user__pb2.OrganizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listUserOrganizations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/listUserOrganizations',
            user__pb2.ListUserOrganizationsRequest.SerializeToString,
            user__pb2.ListOrganizationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/CreateCredential',
            user__pb2.CreateCredentialRequest.SerializeToString,
            user__pb2.CreateCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/GetCredential',
            user__pb2.GetCredentialRequest.SerializeToString,
            user__pb2.GetCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListCredentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/ListCredentials',
            user__pb2.ListCredentialsRequest.SerializeToString,
            user__pb2.ListCredentialsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/DeleteCredential',
            user__pb2.DeleteCredentialRequest.SerializeToString,
            user__pb2.DeleteCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/UpdateCredential',
            user__pb2.UpdateCredentialRequest.SerializeToString,
            user__pb2.UpdateCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateUserGitToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/updateUserGitToken',
            user__pb2.UpdateUserGitToken.SerializeToString,
            user__pb2.UserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getUserGitHubToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/user.UserService/getUserGitHubToken',
            user__pb2.GetUserGitHubTokenRequest.SerializeToString,
            user__pb2.GetUserGitHubTokenResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
