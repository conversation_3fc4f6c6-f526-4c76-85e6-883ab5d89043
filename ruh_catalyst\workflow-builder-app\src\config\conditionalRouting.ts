/**
 * Conditional routing configuration and feature flags for the frontend.
 * 
 * This module provides configuration for conditional routing modes:
 * - Embedded mode: Traditional conditional routing embedded in transitions
 * - Component mode: New component-based conditional routing architecture
 * 
 * The mode is controlled by environment variables and provides feature flags
 * for the frontend to adapt its UI and behavior accordingly.
 */

export type ConditionalRoutingMode = 'component';

/**
 * Get the current conditional routing mode.
 *
 * @returns The conditional routing mode (always 'component' now)
 */
export function getConditionalRoutingMode(): ConditionalRoutingMode {
  // Legacy embedded mode has been removed - always use component mode
  return 'component';
}

/**
 * Check if component-based conditional routing should be used.
 *
 * @returns Always true (component mode is the only supported mode)
 */
export function shouldUseComponentMode(): boolean {
  return true;
}

/**
 * Conditional routing feature flags and configuration.
 *
 * Component-based conditional routing is now the only supported mode.
 */
export const CONDITIONAL_ROUTING_FEATURES = {
  /**
   * Current conditional routing mode (always 'component')
   */
  MODE: getConditionalRoutingMode(),

  /**
   * Whether component-based conditional routing is enabled (always true)
   */
  COMPONENT_MODE_ENABLED: true,

  /**
   * Mode toggle UI is no longer needed since embedded mode is removed
   */
  SHOW_MODE_TOGGLE: false,
} as const;

/**
 * Configuration for conditional component UI features.
 *
 * Enhanced UI features are disabled - using legacy inspector for conditional nodes.
 */
export const CONDITIONAL_COMPONENT_UI_CONFIG = {
  /**
   * Whether to show enhanced conditional component inspector (disabled - using legacy UI)
   */
  ENHANCED_INSPECTOR: false,

  /**
   * Whether to show component mode indicators in the UI (disabled)
   */
  SHOW_MODE_INDICATORS: false,

  /**
   * Whether to show handle management information for conditional components (disabled)
   */
  SHOW_HANDLE_INFO: false,

  /**
   * Whether to show routing decision visualization (disabled)
   */
  SHOW_ROUTING_VISUALIZATION: false,
} as const;

/**
 * Get a human-readable description of the current conditional routing mode.
 * 
 * @returns Description string for the current mode
 */
export function getConditionalRoutingModeDescription(): string {
  const mode = getConditionalRoutingMode();
  
  switch (mode) {
    case 'component':
      return 'Component-based conditional routing with enhanced capabilities';
    case 'embedded':
      return 'Traditional embedded conditional routing';
    default:
      return 'Unknown conditional routing mode';
  }
}

/**
 * Check if a specific conditional routing feature is enabled.
 * 
 * @param feature - The feature to check
 * @returns True if the feature is enabled
 */
export function isConditionalFeatureEnabled(feature: keyof typeof CONDITIONAL_ROUTING_FEATURES): boolean {
  return Boolean(CONDITIONAL_ROUTING_FEATURES[feature]);
}

/**
 * Get all conditional routing configuration as a single object.
 * Useful for debugging and development.
 * 
 * @returns Complete conditional routing configuration
 */
export function getConditionalRoutingConfig() {
  return {
    mode: getConditionalRoutingMode(),
    description: getConditionalRoutingModeDescription(),
    features: CONDITIONAL_ROUTING_FEATURES,
    uiConfig: CONDITIONAL_COMPONENT_UI_CONFIG,
    environment: {
      NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE: process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE,
      NEXT_PUBLIC_SHOW_CONDITIONAL_MODE_TOGGLE: process.env.NEXT_PUBLIC_SHOW_CONDITIONAL_MODE_TOGGLE,
    },
  };
}
