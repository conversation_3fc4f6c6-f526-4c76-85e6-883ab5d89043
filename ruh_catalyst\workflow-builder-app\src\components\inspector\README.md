# Inspector Panel Components

This directory contains the components for the Inspector Panel, which is used to inspect and configure nodes in the workflow builder.

## Component Structure

The Inspector Panel has been refactored into smaller, more manageable components:

1. **InspectorPanel.tsx** - The main component that renders the drawer and orchestrates the other components.
2. **InspectorContext.tsx** - Provides a context for sharing state between inspector components.
3. **InspectorHeader.tsx** - Renders the header section with the node title and description.
4. **InspectorTabs.tsx** - Renders the tabs for switching between settings, info, and advanced panels.
5. **InspectorFooter.tsx** - Renders the footer with delete, validate, and apply buttons.
6. **EmptyState.tsx** - Renders a message when no node is selected.
7. **NotificationManager.tsx** - Manages notifications for the inspector panel.

## Usage

The Inspector Panel is used in the workflow builder to inspect and configure nodes. It is rendered when a node is selected and provides a UI for editing the node's properties.

```tsx
import { InspectorPanel } from '@/components/inspector';

// In your component
<InspectorPanel
  selectedNode={selectedNode}
  onNodeDataChange={handleNodeDataChange}
  onClose={handleCloseInspector}
  onDeleteNode={handleDeleteNode}
  edges={edges}
  nodes={nodes}
/>
```

## State Management

The Inspector Panel uses a combination of React Context and Zustand for state management:

- **InspectorContext** - Provides shared state and methods for the inspector components.
- **useInspectorStore** - A Zustand store for managing UI state like active tab and validation errors.

## Testing

The Inspector Panel components are tested using Jest and React Testing Library. The tests verify that the components render correctly and that the user interactions work as expected.

```bash
# Run tests
npm test
```
