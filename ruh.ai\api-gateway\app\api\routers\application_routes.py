"""
Application Routes

This module provides FastAPI routes for application management including:
- CRUD operations for applications
- Application metrics and analytics
- Image attachment functionality
- Bulk operations
- Search and filtering
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.application_service import application_service
from app.schemas.application import (
    # Proto-aligned models
    # Legacy models for backward compatibility
    PaginatedApplicationResponse,
    BulkDeleteRequest,
    BulkDeleteResponse,
    BulkUpdateStatusRequest,
    BulkUpdateStatusResponse,
    ApplicationSearchResponse,
    # Missing legacy models
    ApplicationCreate,
    ApplicationUpdate,
    ApplicationResponse,
    ApplicationWithMetricsResponse,
    # Enums
    ApplicationStatusEnum,
    # Base response
    BaseResponse,
)
from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error
import logging

logger = logging.getLogger(__name__)

application_router = APIRouter(prefix="/applications", tags=["applications"])


# ===== CORE CRUD OPERATIONS =====


@application_router.post("", response_model=ApplicationResponse)
async def create_application(
    application_data: ApplicationCreate,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Create a new application.

    Creates an application with the specified workflows and agents.
    """
    try:
        # Ensure user can only create applications for themselves (unless admin)
        if current_user.get("role") == "user":
            application_data.user_id = current_user.get("user_id")

        result = await application_service.create_application(
            user_id=application_data.user_id,
            name=application_data.name,
            description=application_data.description,
            workflow_ids=application_data.workflow_ids,
            agent_ids=application_data.agent_ids,
        )

        return ApplicationResponse(
            success=result["success"],
            message=result["message"],
            application=result.get("application"),
        )

    except Exception as e:
        logger.error(f"Error creating application: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@application_router.get("", response_model=PaginatedApplicationResponse)
async def get_applications(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of applications per page"),
    status: Optional[ApplicationStatusEnum] = Query(None, description="Filter by status"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get applications for the current user.

    Retrieves a paginated list of applications with optional filtering.
    """
    try:
        # Users can only see their own applications
        user_id = current_user.get("user_id") if current_user.get("role") == "user" else None

        # Calculate offset from page and page_size
        offset = (page - 1) * page_size

        result = await application_service.get_applications(
            user_id=user_id or "",  # Empty string for admin to see all
            status=status.value if status else None,
            limit=page_size,
            offset=offset,
        )

        # Calculate pagination metadata
        total_count = result.get("total_count", 0)
        total_pages = (total_count + page_size - 1) // page_size
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        from app.schemas.application import PaginationMetadata

        metadata = PaginationMetadata(
            total=total_count,
            total_pages=total_pages,
            current_page=current_page,
            page_size=page_size,
            has_next_page=has_next_page,
            has_previous_page=has_previous_page,
        )

        return PaginatedApplicationResponse(data=result.get("applications", []), metadata=metadata)

    except Exception as e:
        logger.error(f"Error getting applications: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@application_router.get("/{application_id}", response_model=ApplicationWithMetricsResponse)
async def get_application(
    application_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Get a specific application with metrics.

    Retrieves application details along with usage metrics.
    """
    try:
        # For users, we'll check ownership in the service layer
        user_id = current_user.get("user_id") if current_user.get("role") == "user" else ""

        result = await application_service.get_application(
            application_id=application_id, user_id=user_id
        )

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])

        return ApplicationWithMetricsResponse(
            success=result["success"],
            message=result["message"],
            application=result.get("application"),
            metrics=result.get("metrics"),
        )

    except Exception as e:
        logger.error(f"Error getting application: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@application_router.put("/{application_id}", response_model=ApplicationResponse)
async def update_application(
    application_id: str,
    application_data: ApplicationUpdate,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Update an application.

    Updates application details, workflows, agents, or status.
    """
    try:
        user_id = current_user.get("user_id") if current_user.get("role") == "user" else ""

        result = await application_service.update_application(
            application_id=application_id,
            user_id=user_id,
            name=application_data.name,
            description=application_data.description,
            workflow_ids=application_data.workflow_ids,
            agent_ids=application_data.agent_ids,
            status=application_data.status.value if application_data.status else None,
        )

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])

        return ApplicationResponse(
            success=result["success"],
            message=result["message"],
            application=result.get("application"),
        )

    except Exception as e:
        logger.error(f"Error updating application: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@application_router.delete("/{application_id}", response_model=BaseResponse)
async def delete_application(
    application_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Delete an application.

    Permanently removes an application and its associated data.
    """
    try:
        user_id = current_user.get("user_id") if current_user.get("role") == "user" else ""

        result = await application_service.delete_application(
            application_id=application_id, user_id=user_id
        )

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])

        return BaseResponse(success=result["success"], message=result["message"])

    except Exception as e:
        logger.error(f"Error deleting application: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== SEARCH AND FILTERING =====


@application_router.get("/search", response_model=ApplicationSearchResponse)
async def search_applications(
    query: str = Query(..., min_length=1, description="Search query"),
    status: Optional[ApplicationStatusEnum] = Query(None, description="Filter by status"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Search applications.

    Searches applications by name and description with optional filtering.
    """
    try:
        # For now, we'll implement a simple search by getting all applications
        # and filtering on the client side. In a real implementation, this would
        # be done in the service layer with proper database search.

        user_id = current_user.get("user_id") if current_user.get("role") == "user" else ""

        result = await application_service.get_applications(
            user_id=user_id, status=status.value if status else None, limit=limit, offset=offset
        )

        # Simple client-side filtering (should be done in service layer)
        applications = result.get("applications", [])
        filtered_apps = [
            app
            for app in applications
            if query.lower() in app.name.lower() or query.lower() in app.description.lower()
        ]

        return ApplicationSearchResponse(
            success=True,
            message="Search completed successfully",
            applications=filtered_apps,
            total_count=len(filtered_apps),
            search_query=query,
        )

    except Exception as e:
        logger.error(f"Error searching applications: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# ===== BULK OPERATIONS =====


@application_router.delete("/bulk", response_model=BulkDeleteResponse)
async def bulk_delete_applications(
    request: BulkDeleteRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Bulk delete applications.

    Deletes multiple applications in a single operation.
    """
    try:
        user_id = current_user.get("user_id") if current_user.get("role") == "user" else ""

        deleted_count = 0
        failed_deletions = []

        for app_id in request.application_ids:
            try:
                result = await application_service.delete_application(
                    application_id=app_id, user_id=user_id
                )
                if result["success"]:
                    deleted_count += 1
                else:
                    failed_deletions.append(app_id)
            except Exception:
                failed_deletions.append(app_id)

        return BulkDeleteResponse(
            success=True,
            message=f"Deleted {deleted_count} applications, {len(failed_deletions)} failed",
            deleted_count=deleted_count,
            failed_deletions=failed_deletions,
        )

    except Exception as e:
        logger.error(f"Error in bulk delete: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@application_router.put("/bulk/status", response_model=BulkUpdateStatusResponse)
async def bulk_update_status(
    request: BulkUpdateStatusRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Bulk update application status.

    Updates the status of multiple applications in a single operation.
    """
    try:
        user_id = current_user.get("user_id") if current_user.get("role") == "user" else ""

        updated_count = 0
        failed_updates = []

        for app_id in request.application_ids:
            try:
                result = await application_service.update_application(
                    application_id=app_id,
                    user_id=user_id,
                    name=None,
                    description=None,
                    workflow_ids=None,
                    agent_ids=None,
                    status=request.status.value,
                )
                if result["success"]:
                    updated_count += 1
                else:
                    failed_updates.append(app_id)
            except Exception:
                failed_updates.append(app_id)

        return BulkUpdateStatusResponse(
            success=True,
            message=f"Updated {updated_count} applications, {len(failed_updates)} failed",
            updated_count=updated_count,
            failed_updates=failed_updates,
        )

    except Exception as e:
        logger.error(f"Error in bulk status update: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
