import React from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { ValidationWrapper } from "../ValidationWrapper";
import CredentialSelector from "../../credentials/CredentialSelector";
import type { InputDefinition } from "@/types";

interface CredentialInputValue {
  use_credential_id: boolean;
  credential_id?: string;
  value?: string;
  credential_type?: string;
}

interface CredentialInputProps {
  inputDef: InputDefinition;
  value: CredentialInputValue | null | undefined;
  onChange: (name: string, value: CredentialInputValue) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId: string;
}

/**
 * Component for rendering credential inputs with secure storage option
 */
export function CredentialInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: CredentialInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  const currentValue = value || { use_credential_id: false };

  const handleToggleCredentialMode = (useCredentialId: boolean) => {
    const newValue: CredentialInputValue = {
      ...currentValue,
      use_credential_id: useCredentialId,
    };

    // Clear the opposite field when switching modes
    if (useCredentialId) {
      newValue.value = "";
    } else {
      newValue.credential_id = "";
    }

    onChange(inputDef.name, newValue);
  };

  const handleCredentialSelect = (credentialId: string) => {
    onChange(inputDef.name, {
      ...currentValue,
      credential_id: credentialId,
    });
  };

  const handleDirectValueChange = (directValue: string) => {
    onChange(inputDef.name, {
      ...currentValue,
      value: directValue,
    });
  };

  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="mt-1 space-y-3">
        {/* Toggle between direct input and credential selection */}
        <div className="flex items-center space-x-2">
          <Switch
            id={`${inputId}-use-credential`}
            checked={currentValue.use_credential_id}
            onCheckedChange={handleToggleCredentialMode}
            disabled={isDisabled}
            className={isDisabled ? "opacity-50" : ""}
          />
          <Label htmlFor={`${inputId}-use-credential`} className="text-xs">
            Use credential from secure storage
          </Label>
        </div>

        {/* Credential selection mode */}
        {currentValue.use_credential_id ? (
          <div className="space-y-2">
            <CredentialSelector
              value={currentValue.credential_id}
              onSelect={handleCredentialSelect}
              placeholder={`Select ${inputDef.display_name || 'credential'}...`}
              disabled={isDisabled}
              searchable
              className="w-full"
            />
            
            {/* Show selected credential info */}
            {currentValue.credential_id && (
              <div className="text-xs text-muted-foreground">
                <p>✓ Credential selected. The secure value will be used during workflow execution.</p>
              </div>
            )}
            
            {/* Help text for credential mode */}
            <div className="text-xs text-muted-foreground">
              <p>
                Credentials are stored securely and their values are only accessed during workflow execution.
                You can manage credentials in the{" "}
                <span className="font-medium">Credential Manager</span>.
              </p>
            </div>
          </div>
        ) : (
          /* Direct input mode */
          <div className="space-y-2">
            <div className="relative">
              <Input
                id={`${inputId}-value`}
                type="password"
                value={currentValue.value || ""}
                onChange={(e) => handleDirectValueChange(e.target.value)}
                placeholder={`Enter ${inputDef.display_name || 'credential'}...`}
                className={cn(
                  "bg-background/50 h-8 text-xs",
                  isDisabled && "opacity-50"
                )}
                disabled={isDisabled}
              />
              {isDisabled && isConnected && <ConnectedIndicator />}
            </div>
            
            {/* Help text for direct input mode */}
            <div className="text-xs text-muted-foreground">
              <p>
                ⚠️ Entering credentials directly is less secure. Consider using the secure storage option above.
              </p>
            </div>
          </div>
        )}

        {/* Additional info based on credential type */}
        {inputDef.info && (
          <div className="text-xs text-muted-foreground">
            <p>{inputDef.info}</p>
          </div>
        )}
      </div>
    </ValidationWrapper>
  );
}
