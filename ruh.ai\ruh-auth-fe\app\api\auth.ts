import api from "@/services/axios";
import { LoginType, SignupType, ResetPasswordType } from "@/lib/schemas/auth";
import { loginRoute, onboardingRoute } from "@/app/shared/routes";
import {
  LoginResponse,
  SignupResponse,
  LoginResult,
  TokenResponse,
} from "@/app/shared/interfaces";
import { firebaseService } from "@/services/firebase";
import { clearAuthCookies, setAuthCookies } from "@/services/authCookies";
import { userApi } from "./user";

/**
 * Checks if a user has completed the onboarding process
 * by verifying they have both department and jobRole fields.
 *
 * @returns {Promise<boolean>} True if onboarding is complete, false otherwise
 */
export async function hasCompletedOnboarding(): Promise<boolean> {
  try {
    const userDetails = await userApi.getCurrentUser();
    return Boolean(userDetails?.department && userDetails?.jobRole);
  } catch (error: any) {
    // Check if this request has already been through the axios interceptor
    const originalRequest = error.config as any;
    if (originalRequest && originalRequest._retry) {
      // This request has already been through the interceptor and still failed
      // This means token refresh failed, so we should redirect to login
      throw error;
    }

    // For other errors, return false to allow normal flow
    return false;
  }
}

export const authApi = {
  /**
   * Login user with email and password
   * @param data LoginType from zod schema
   */
  login: async (
    data: LoginType,
    redirect_url: string
  ): Promise<LoginResult> => {
    const { email, password } = data;
    try {
      // Generate FCM token for push notifications
      let fcmToken = null;
      try {
        fcmToken = await firebaseService.requestFCMToken();
      } catch (fcmError) {
        console.error("Failed to generate FCM token:", fcmError);
        // Continue with login even if FCM token generation fails
      }

      // Prepare login payload
      const payload: any = {
        email,
        password,
      };

      // Only include FCM token if it was successfully generated
      if (fcmToken) {
        payload.fcm_token = fcmToken;
      }

      const response = await api.post<LoginResponse>("/auth/login", payload);

      if (!response.data.access_token) {
        throw new Error("Login failed: Unexpected response from server.");
      }

      let redirectPath = onboardingRoute; // Default to onboarding
      let hasCompletedOnboarding = false;

      try {
        // First clear any existing cookies to ensure clean state
        await clearAuthCookies(false); // Clear local cookies only

        // Set initial cookies with local-only access
        await setAuthCookies(
          response.data.access_token,
          response.data.refresh_token,
          response.data.accessTokenAge || 604800, // 1 week
          response.data.refreshTokenAge || 2592000, // 1 month
          false // Set to local-only initially
        );

        // Fetch user details after successful login
        const userDetails = await userApi.getCurrentUser();

        // Check if department exists and is not empty/null
        if (userDetails && userDetails.department && userDetails.jobRole) {
          hasCompletedOnboarding = true;
          redirectPath = redirect_url;

          // Clear local cookies before setting domain-wide ones
          await clearAuthCookies(false);

          // If onboarding is complete, set domain-wide cookies
          await setAuthCookies(
            response.data.access_token,
            response.data.refresh_token,
            response.data.accessTokenAge || 604800, // 1 week
            response.data.refreshTokenAge || 2592000, // 1 month
            true // Set domain-wide cookies
          );
        }
      } catch (userError) {
        console.error("Failed to fetch user details:", userError);
        // Keep redirectPath as '/onboarding' if fetching user details fails
      }

      // Return both login data and the determined redirect path
      return {
        loginData: response.data,
        redirectPath,
      };
    } catch (error: any) {
      // Clear any cookies that might have been set
      await clearAuthCookies(false);

      if (error.response?.status === 404) {
        // throw new Error("User not found.");
        throw new Error(error.response?.data?.detail);
      }
      if (error.response?.status === 412) {
        throw new Error(
          "Account inactive. Please check your email for verification."
        );
      }
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Something went wrong"
      );
    }
  },

  /**
   * Register new user
   * @param data SignupType from zod schema
   */
  signup: async (data: SignupType): Promise<SignupResponse> => {
    try {
      const { email, fullName, password } = data;
      const payload = {
        full_name: fullName,
        email,
        password,
      };
      const response = await api.post<SignupResponse>(
        "/auth/register",
        payload
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        throw new Error(
          error.response?.data?.detail || "Email already registered."
        );
      }
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Signup failed"
      );
    }
  },

  /**
   * Forgot password
   * @param data ForgotPasswordType from zod schema
   */
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    try {
      const response = await api.post<{ message: string }>(
        "/auth/reset-password-otp",
        null, // No request body for this POST request
        { params: { email } } // Pass email as query parameter in the config object
      );
      return response.data; // Return the success message
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || // Use detail field if available
          error.response?.data?.message ||
          "Failed to send password reset email"
      );
    }
  },

  /**
   * Reset user's password using OTP token
   * @param token The OTP token from the reset link
   * @param data ResetPasswordType containing newPassword and confirmNewPassword
   */
  resetPassword: async (
    token: string,
    data: ResetPasswordType
  ): Promise<{ message: string }> => {
    const { newPassword, confirmNewPassword } = data;
    try {
      // Construct the payload for the API
      const payload = {
        token,
        new_password: newPassword,
        confirm_new_password: confirmNewPassword,
      };

      // Call the actual API endpoint
      const response = await api.post<{ message: string }>(
        "/auth/update-password",
        payload
      );

      return response.data; // Return the success message from the API
    } catch (error: any) {
      throw new Error(
        // Use error details provided by the API response preferentially
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Password reset failed"
      );
    }
  },

  /**
   * Verify email using OTP token
   * @param token The OTP token from the verification link
   */
  verifyEmailOtp: async (token: string): Promise<{ message: string }> => {
    try {
      const payload = { token };
      const response = await api.post<{ message: string }>(
        "/auth/verify-email-otp",
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Email verification failed"
      );
    }
  },

  /**
   * Verify current auth session by attempting to fetch user data.
   */
  isLoggedIn: async (): Promise<boolean> => {
    try {
      await userApi.getCurrentUser(); // Attempt to fetch user data
      return true; // If successful, user is considered logged in
    } catch (error) {
      // Any error (including 401/403 from getCurrentUser) means session is not valid
      return false;
    }
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    try {
      // Clear both local and domain-wide cookies to ensure complete logout
      await clearAuthCookies(true); // Clear domain-wide cookies
      await clearAuthCookies(false); // Clear local cookies

      // Redirect to login page
      window.location.href = loginRoute;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Logout failed");
    }
  },

  /**
   * Generate new access token using refresh token
   * @param refreshToken The refresh token to use for generating a new access token
   */
  generateAccessToken: async (refreshToken: string): Promise<TokenResponse> => {
    try {
      const response = await api.post<TokenResponse>(
        "/auth/access-token",
        {},
        {
          params: { refresh_token: refreshToken },
        }
      );

      // Update the access token in cookies if successful
      if (response.data.success && response.data.access_token) {
        // Calculate token age in seconds from tokenExpireAt
        const expireAt = new Date(response.data.tokenExpireAt).getTime();
        const now = new Date().getTime();
        const accessTokenAge = Math.floor((expireAt - now) / 1000);

        await setAuthCookies(
          response.data.access_token,
          refreshToken,
          accessTokenAge > 0 ? accessTokenAge : 604800, // Default to 1 week if calculation is negative
          null // Don't update refresh token age
        );
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to generate new access token"
      );
    }
  },

  /**https://meet.google.com/vws-bbyx-xon?authuser=0
   * Initiates the Google OAuth login flow by redirecting the browser.
   */
  googleLogin: async (): Promise<void> => {
    try {
      // Construct the full URL to the backend's Google login initiation endpoint.
      const baseUrl = process.env.NEXT_PUBLIC_API_URL;
      let googleLoginUrl = `${baseUrl}/auth/google-login`;

      window.location.href = googleLoginUrl;
    } catch (error) {
      console.error("Error during Google login:", error);
    }
  },
};
