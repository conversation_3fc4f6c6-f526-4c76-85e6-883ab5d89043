import { <PERSON><PERSON>, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { InputDefinition, InputRequirementRule } from "@/types";
import { ValidationError, ValidationErrorCode, ValidationResult, MissingField } from "./types";
import { createValidationError } from "./errors";
import { checkInputVisibility } from "@/utils/inputVisibility";
import { isNodeConnectedAsTool } from "@/utils/toolConnectionUtils";

/**
 * Unwraps dual-purpose input values that may be wrapped in {value: ...} objects
 * @param value The value to unwrap
 * @returns The unwrapped value
 */
function unwrapDualPurposeValue(value: any): any {
  console.log(`[unwrapDualPurposeValue] Input value:`, value, `Type: ${typeof value}`);

  // Check if the value is wrapped in a dual-purpose input structure
  if (typeof value === 'object' && value !== null && 'value' in value) {
    // Check if this looks like a dual-purpose input wrapper
    // It should have a 'value' property and optionally other metadata properties
    const keys = Object.keys(value);
    const hasValueProperty = keys.includes('value');
    const hasOnlyValueProperty = keys.length === 1 && hasValueProperty;
    const hasValueAndMetadata = hasValueProperty && keys.every(key =>
      key === 'value' || key === 'transition_id' || key === 'metadata' || key === 'type'
    );

    console.log(`[unwrapDualPurposeValue] Analysis:`, {
      hasValueProperty,
      hasOnlyValueProperty,
      hasValueAndMetadata,
      keys,
      valueType: typeof value.value
    });

    if (hasOnlyValueProperty || hasValueAndMetadata) {
      console.log(`[unwrapDualPurposeValue] Detected dual-purpose wrapper, unwrapping:`, value);
      console.log(`[unwrapDualPurposeValue] Wrapper keys:`, keys);
      console.log(`[unwrapDualPurposeValue] Extracted value:`, value.value);

      // If the extracted value is a JSON string for object types, parse it
      if (typeof value.value === 'string' && value.value.trim().startsWith('{')) {
        try {
          const parsed = JSON.parse(value.value);
          console.log(`[unwrapDualPurposeValue] Parsed JSON string to object:`, parsed);
          return parsed;
        } catch (e) {
          console.log(`[unwrapDualPurposeValue] Failed to parse JSON string, returning as-is:`, e);
          return value.value;
        }
      }

      return value.value;
    }
  }

  // If it's a JSON string that looks like an object, try to parse it
  if (typeof value === 'string' && value.trim().startsWith('{')) {
    try {
      const parsed = JSON.parse(value);
      console.log(`[unwrapDualPurposeValue] Parsed standalone JSON string to object:`, parsed);
      return parsed;
    } catch (e) {
      console.log(`[unwrapDualPurposeValue] Failed to parse standalone JSON string, returning as-is:`, e);
    }
  }

  console.log(`[unwrapDualPurposeValue] No unwrapping needed, returning as-is:`, value);
  return value;
}

/**
 * Validates a single field based on its definition
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param node The node containing the field (optional)
 * @returns An array of validation errors
 */
export function validateField(
  inputDef: InputDefinition,
  value: any,
  nodeId: string,
  node?: Node<WorkflowNodeData>
): ValidationError[] {
  console.log(`[${getTimestamp()}] [validateField] ========== VALIDATING FIELD ==========`);
  console.log(`[${getTimestamp()}] [validateField] Field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
  console.log(`[${getTimestamp()}] [validateField] Node ID: ${nodeId}`);
  console.log(`[${getTimestamp()}] [validateField] Node label: ${node?.data?.label || "Unknown"}`);
  console.log(`[${getTimestamp()}] [validateField] Input type: ${inputDef.input_type}`);
  console.log(`[${getTimestamp()}] [validateField] Value: ${JSON.stringify(value)}`);

  const errors: ValidationError[] = [];

  // Skip validation for handle inputs
  if (inputDef.is_handle) {
    console.log(`[${getTimestamp()}] [validateField] Skipping validation for handle input: ${inputDef.name}`);
    return errors;
  }

  // Determine if field is required
  const isRequired = node ? isFieldRequired(node, inputDef) : !!inputDef.required;
  console.log(`[${getTimestamp()}] [validateField] Is required: ${isRequired ? "YES" : "NO"} (node provided: ${node ? "YES" : "NO"})`);

  // Check if field is empty
  const isEmpty = isValueEmpty(value, inputDef.input_type);
  console.log(`[${getTimestamp()}] [validateField] Is empty: ${isEmpty ? "YES" : "NO"}`);

  // Check if required field is missing
  if (isRequired && isEmpty) {
    console.log(`[${getTimestamp()}] [validateField] VALIDATION ERROR: Required field "${inputDef.display_name || inputDef.name}" is missing`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_REQUIRED,
        `Field "${inputDef.display_name}" is required`,
        "error",
        nodeId,
        inputDef.name
      )
    );
    console.log(`[${getTimestamp()}] [validateField] Stopping validation for this field due to missing required value`);
    return errors; // Stop validation if required field is missing
  }

  // Skip validation for empty optional fields
  if (!isRequired && isEmpty) {
    console.log(`[${getTimestamp()}] [validateField] Skipping validation for empty optional field: ${inputDef.name}`);
    return errors;
  }

  // Validate based on input type
  switch (inputDef.input_type) {
    case "string":
      validateStringField(inputDef, value, nodeId, errors);
      break;
    case "int":
    case "float":
    case "number":
      validateNumberField(inputDef, value, nodeId, errors);
      break;
    case "dict":
    case "json":
    case "object":
      validateObjectField(inputDef, value, nodeId, errors);
      break;
  }

  console.log(`[${getTimestamp()}] [validateField] Field validation complete. Errors found: ${errors.length}`);
  return errors;
}

/**
 * Validates a string field
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param errors The array of errors to append to
 */
function validateStringField(
  inputDef: InputDefinition,
  value: any,
  nodeId: string,
  errors: ValidationError[]
): void {
  console.log(`[${getTimestamp()}] [validateStringField] Validating string field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
  console.log(`[${getTimestamp()}] [validateStringField] Value type: ${typeof value}, Value: ${JSON.stringify(value)}`);

  // Check type
  if (typeof value !== "string") {
    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION ERROR: Value is not a string: ${typeof value}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_STRING_LENGTH,
        `Field "${inputDef.display_name}" must be a string`,
        "error",
        nodeId,
        inputDef.name
      )
    );
    return;
  }

  // Check min length
  if (inputDef.min_length !== undefined && value.length < inputDef.min_length) {
    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String length ${value.length} is less than minimum ${inputDef.min_length}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_STRING_LENGTH,
        `Field "${inputDef.display_name}" must be at least ${inputDef.min_length} characters`,
        "warning",
        nodeId,
        inputDef.name
      )
    );
  }

  // Check max length
  if (inputDef.max_length !== undefined && value.length > inputDef.max_length) {
    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String length ${value.length} exceeds maximum ${inputDef.max_length}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_STRING_LENGTH,
        `Field "${inputDef.display_name}" must be at most ${inputDef.max_length} characters`,
        "warning",
        nodeId,
        inputDef.name
      )
    );
  }

  // Check pattern
  if (inputDef.pattern && !new RegExp(inputDef.pattern).test(value)) {
    console.log(`[${getTimestamp()}] [validateStringField] VALIDATION WARNING: String does not match pattern: ${inputDef.pattern}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_PATTERN_MISMATCH,
        inputDef.pattern_error || `Field "${inputDef.display_name}" does not match the required pattern`,
        "warning",
        nodeId,
        inputDef.name
      )
    );
  }

  console.log(`[${getTimestamp()}] [validateStringField] String validation complete. Errors found: ${errors.length}`);
}

/**
 * Validates a number field
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param errors The array of errors to append to
 */
function validateNumberField(
  inputDef: InputDefinition,
  value: any,
  nodeId: string,
  errors: ValidationError[]
): void {
  console.log(`[${getTimestamp()}] [validateNumberField] Validating number field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
  console.log(`[${getTimestamp()}] [validateNumberField] Original value type: ${typeof value}, Value: ${JSON.stringify(value)}`);

  // Convert to number if string
  const numValue = typeof value === "string" ? Number(value) : value;
  console.log(`[${getTimestamp()}] [validateNumberField] Converted value: ${numValue}, isNaN: ${isNaN(numValue)}`);

  // Check type
  if (typeof numValue !== "number" || isNaN(numValue)) {
    console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION ERROR: Value is not a valid number: ${value}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_NUMBER_RANGE,
        `Field "${inputDef.display_name}" must be a number`,
        "error",
        nodeId,
        inputDef.name
      )
    );
    return;
  }

  // Check min value
  if (inputDef.min_value !== undefined && numValue < Number(inputDef.min_value)) {
    console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION WARNING: Number ${numValue} is less than minimum ${inputDef.min_value}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_NUMBER_RANGE,
        `Field "${inputDef.display_name}" must be at least ${inputDef.min_value}`,
        "warning",
        nodeId,
        inputDef.name
      )
    );
  }

  // Check max value
  if (inputDef.max_value !== undefined && numValue > Number(inputDef.max_value)) {
    console.log(`[${getTimestamp()}] [validateNumberField] VALIDATION WARNING: Number ${numValue} exceeds maximum ${inputDef.max_value}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_NUMBER_RANGE,
        `Field "${inputDef.display_name}" must be at most ${inputDef.max_value}`,
        "warning",
        nodeId,
        inputDef.name
      )
    );
  }

  console.log(`[${getTimestamp()}] [validateNumberField] Number validation complete. Errors found: ${errors.length}`);
}

/**
 * Validates an object field
 *
 * @param inputDef The input definition
 * @param value The field value
 * @param nodeId The ID of the node containing the field
 * @param errors The array of errors to append to
 */
function validateObjectField(
  inputDef: InputDefinition,
  value: any,
  nodeId: string,
  errors: ValidationError[]
): void {
  console.log(`[${getTimestamp()}] [validateObjectField] Validating object field: ${inputDef.name} (${inputDef.display_name || inputDef.name})`);
  console.log(`[${getTimestamp()}] [validateObjectField] Value type: ${typeof value}, Value: ${JSON.stringify(value)}`);

  // Handle string representation of objects
  if (typeof value === "string") {
    console.log(`[${getTimestamp()}] [validateObjectField] Value is a string, attempting to parse as JSON`);
    try {
      value = JSON.parse(value);
      console.log(`[${getTimestamp()}] [validateObjectField] Successfully parsed string as JSON: ${JSON.stringify(value)}`);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Failed to parse string as JSON: ${errorMessage}`);
      errors.push(
        createValidationError(
          "INVALID_JSON" as any, // Type assertion as a temporary fix
          `Field "${inputDef.display_name}" must be valid JSON`,
          "error",
          nodeId,
          inputDef.name
        )
      );
      return;
    }
  }

  // Check type
  if (typeof value !== "object" || value === null) {
    console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Value is not an object: ${typeof value}, null: ${value === null}`);
    errors.push(
      createValidationError(
        ValidationErrorCode.FIELD_MISSING_REQUIRED_KEYS,
        `Field "${inputDef.display_name}" must be an object`,
        "error",
        nodeId,
        inputDef.name
      )
    );
    return;
  }

  // Check required keys
  if (inputDef.required_keys && inputDef.required_keys.length > 0) {
    console.log(`[${getTimestamp()}] [validateObjectField] Checking for required keys: ${inputDef.required_keys.join(', ')}`);
    const missingKeys = inputDef.required_keys.filter(key => !(key in value));

    if (missingKeys.length > 0) {
      console.log(`[${getTimestamp()}] [validateObjectField] VALIDATION ERROR: Missing required keys: ${missingKeys.join(', ')}`);
      errors.push(
        createValidationError(
          ValidationErrorCode.FIELD_MISSING_REQUIRED_KEYS,
          `Field "${inputDef.display_name}" is missing required keys: ${missingKeys.join(", ")}`,
          "error",
          nodeId,
          inputDef.name
        )
      );
    } else {
      console.log(`[${getTimestamp()}] [validateObjectField] All required keys are present`);
    }
  } else {
    console.log(`[${getTimestamp()}] [validateObjectField] No required keys specified for this object`);
  }

  console.log(`[${getTimestamp()}] [validateObjectField] Object validation complete. Errors found: ${errors.length}`);
}

/**
 * Validates all fields in a node
 *
 * @param node The node to validate
 * @returns A validation result
 */
export function validateFields(node: Node<WorkflowNodeData>): ValidationResult {
  console.log(`[${getTimestamp()}] [validateFields] ========== STARTING FIELD VALIDATION FOR NODE ==========`);
  console.log(`[${getTimestamp()}] [validateFields] Node: ${node.id} (${node.data.label || "Unnamed"})`);
  console.log(`[${getTimestamp()}] [validateFields] Type: ${node.data.type}, Original Type: ${node.data.originalType}`);

  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  // Skip validation if node has no definition or inputs
  if (!node.data?.definition?.inputs) {
    console.log(`[${getTimestamp()}] [validateFields] Skipping validation - node has no definition or inputs`);
    return { isValid: true, errors, warnings, infos };
  }

  const inputs = node.data.definition.inputs;
  const config = node.data.config || {};

  // Log detailed node information
  console.log(`[${getTimestamp()}] [validateFields] Node details:
    - ID: ${node.id}
    - Label: ${node.data.label || "Unnamed"}
    - Type: ${node.data.type}
    - Original Type: ${node.data.originalType}
    - Position: (${node.position.x}, ${node.position.y})
    - Total inputs: ${inputs.length}
    - Config keys: ${Object.keys(config).join(', ') || "none"}
    - Is MCP component: ${isMCPMarketplaceComponent(node) ? "YES" : "NO"}`);

  // Validate each input
  inputs.forEach(input => {
    console.log(`[${getTimestamp()}] [validateFields] Validating input: ${input.name} (${input.display_name || input.name})`);

    // Pass the node to validateField so it can determine if the field is required
    const fieldErrors = validateField(input, config[input.name], node.id, node);

    // Log validation results
    if (fieldErrors.length > 0) {
      console.log(`[${getTimestamp()}] [validateFields] Found ${fieldErrors.length} validation issues for field ${input.name}:`);
      fieldErrors.forEach((error, index) => {
        console.log(`[${getTimestamp()}] [validateFields]   ${index + 1}. ${error.severity.toUpperCase()}: ${error.message}`);
      });
    } else {
      console.log(`[${getTimestamp()}] [validateFields] Field ${input.name} passed validation`);
    }

    // Separate errors by severity
    fieldErrors.forEach(error => {
      if (error.severity === "error") {
        errors.push(error);
      } else if (error.severity === "warning") {
        warnings.push(error);
      } else if (error.severity === "info") {
        infos.push(error);
      }
    });
  });

  // Log validation summary
  console.log(`[${getTimestamp()}] [validateFields] ========== FIELD VALIDATION COMPLETE ==========`);
  console.log(`[${getTimestamp()}] [validateFields] Validation results for node ${node.id} (${node.data.label || "Unnamed"}):`);
  console.log(`[${getTimestamp()}] [validateFields] - Errors: ${errors.length}`);
  console.log(`[${getTimestamp()}] [validateFields] - Warnings: ${warnings.length}`);
  console.log(`[${getTimestamp()}] [validateFields] - Infos: ${infos.length}`);
  console.log(`[${getTimestamp()}] [validateFields] - Is valid: ${errors.length === 0 ? "YES" : "NO"}`);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    infos,
  };
}

/**
 * Helper function to get a timestamp for logging
 * @returns Formatted timestamp string
 */
function getTimestamp(): string {
  return new Date().toISOString().replace('T', ' ').substring(0, 19);
}

/**
 * Evaluates requirement rules to determine if a field should be required
 *
 * @param requirementRules The requirement rules to evaluate
 * @param requirementLogic The logic to use when combining rules (OR, AND)
 * @param config The current node configuration
 * @returns True if the requirement rules are satisfied
 */
function evaluateRequirementRules(
  requirementRules: InputRequirementRule[] | null | undefined,
  requirementLogic: string | undefined,
  config: Record<string, any>
): boolean {
  if (!requirementRules || requirementRules.length === 0) {
    return false;
  }

  console.log(`[${getTimestamp()}] [evaluateRequirementRules] Evaluating ${requirementRules.length} requirement rules with logic: ${requirementLogic || "OR"}`);

  const logic = requirementLogic || "OR";
  const results = requirementRules.map(rule => {
    const targetValue = config[rule.field_name];
    const operator = rule.operator || "equals";

    let ruleMatches = false;
    switch (operator) {
      case "equals":
        ruleMatches = targetValue === rule.field_value;
        break;
      case "not_equals":
        ruleMatches = targetValue !== rule.field_value;
        break;
      case "contains":
        ruleMatches = typeof targetValue === "string" && targetValue.includes(rule.field_value);
        break;
      case "exists":
        ruleMatches = targetValue !== undefined && targetValue !== null;
        break;
      case "not_exists":
        ruleMatches = targetValue === undefined || targetValue === null;
        break;
      default:
        ruleMatches = targetValue === rule.field_value;
    }

    console.log(`[${getTimestamp()}] [evaluateRequirementRules] Rule: ${rule.field_name} ${operator} ${rule.field_value} | Target: ${targetValue} | Result: ${ruleMatches}`);
    return ruleMatches;
  });

  const finalResult = logic === "AND" ? results.every(r => r) : results.some(r => r);
  console.log(`[${getTimestamp()}] [evaluateRequirementRules] Final result with ${logic} logic: ${finalResult}`);

  return finalResult;
}

/**
 * Determines if a node is an MCP Marketplace component
 *
 * @param node The node to check
 * @returns True if the node is an MCP Marketplace component
 */
function isMCPMarketplaceComponent(node: Node<WorkflowNodeData>): boolean {
  if (!node || !node.data) return false;

  // Check various indicators that this is an MCP component
  const isMCP = (
    node.data.type === "mcp" ||
    node.data.originalType === "MCPMarketplaceComponent" ||
    node.data.type === "MCPMarketplaceComponent" ||
    (node.data.definition && node.data.definition.type === "MCPMarketplaceComponent") ||
    (node.data.definition && node.data.definition.mcp_info) ||
    (node.data.definition && node.data.definition.path &&
     (node.data.definition.path.includes("mcp_marketplace") ||
      node.data.definition.path.includes("components.mcp")))
  );

  console.log(`[${getTimestamp()}] [isMCPMarketplaceComponent] Node ${node.id} (${node.data.label || "Unnamed"}):
    - type: ${node.data.type}
    - originalType: ${node.data.originalType}
    - definition.type: ${node.data?.definition?.type}
    - has mcp_info: ${!!node.data?.definition?.mcp_info}
    - path: ${node.data?.definition?.path}
    - RESULT: ${isMCP ? "IS MCP COMPONENT" : "NOT MCP COMPONENT"}`);

  return !!isMCP; // Convert to boolean
}

/**
 * Determines if a field should be considered required based on component type and field properties
 *
 * @param node The node containing the field
 * @param input The input definition
 * @param isHandleConnected Optional flag indicating if this handle input is connected to another node
 * @returns True if the field should be considered required
 */
export function isFieldRequired(
  node: Node<WorkflowNodeData>,
  input: InputDefinition,
  isHandleConnected: boolean = false
): boolean {
  console.log(`[${getTimestamp()}] [isFieldRequired] Checking if field is required:
    - Node: ${node.id} (${node.data.label || "Unnamed"})
    - Field: ${input.name} (${input.display_name || input.name})
    - Input type: ${input.input_type}
    - Explicitly required: ${input.required === true ? "YES" : "NO"}
    - Is handle: ${input.is_handle ? "YES" : "NO"}
    - Is handle connected: ${isHandleConnected ? "YES" : "NO"}
    - Ends with _handle: ${input.name.endsWith("_handle") ? "YES" : "NO"}
    - Has requirement rules: ${input.requirement_rules && input.requirement_rules.length > 0 ? "YES" : "NO"}`);

  // Check requirement rules first - if they exist and are satisfied, the field is required
  if (input.requirement_rules && input.requirement_rules.length > 0) {
    const config = node.data.config || {};
    const isRequiredByRules = evaluateRequirementRules(
      input.requirement_rules,
      input.requirement_logic,
      config
    );

    if (isRequiredByRules) {
      console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is required by requirement rules`);

      // For handle inputs, they're only required if not connected
      if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
        if (isHandleConnected) {
          console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and is connected, not required for direct input despite requirement rules`);
          return false;
        } else {
          console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input but NOT connected, required for direct input due to requirement rules`);
          return true;
        }
      }

      return true;
    }
  }

  // If explicitly marked as required, it's required
  if (input.required === true) {
    // For handle inputs, they're only required if not connected
    if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
      if (isHandleConnected) {
        console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and is connected, not required for direct input`);
        return false;
      } else {
        console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input but NOT connected, required for direct input`);
        return true;
      }
    }

    console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is explicitly marked as required`);
    return true;
  }

  // For handle inputs that aren't explicitly required, they're never required for direct input
  if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
    console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is a handle input and not explicitly required, not required for direct input`);
    return false;
  }

  // For MCP components, we need special handling
  const isMCP = isMCPMarketplaceComponent(node);
  if (isMCP) {
    console.log(`[${getTimestamp()}] [isFieldRequired] Node is an MCP component, applying special rules for field ${input.name}`);

    // Common optional fields in MCP components
    const commonOptionalFields = ["link", "api_key", "base_url"];
    if (commonOptionalFields.includes(input.name)) {
      console.log(`[${getTimestamp()}] [isFieldRequired] Field ${input.name} is in the common optional fields list for MCP components`);
      return false;
    }

    // For MCP components, consider fields required if they are explicitly marked as required: true
    // OR if they are not explicitly marked as optional (required: false)
    const isRequired = input.required !== false;
    console.log(`[${getTimestamp()}] [isFieldRequired] MCP component field ${input.name} required status: ${isRequired ? "REQUIRED" : "OPTIONAL"} (required !== false: ${input.required !== false})`);
    return isRequired;
  }

  // For standard components, consider fields required if they are explicitly marked as required: true
  // OR if they are not explicitly marked as optional (required: false)
  const isRequired = input.required !== false;
  console.log(`[${getTimestamp()}] [isFieldRequired] Standard component field ${input.name} required status: ${isRequired ? "REQUIRED" : "OPTIONAL"} (required !== false: ${input.required !== false})`);
  return isRequired;
}

/**
 * Determines if a field value should be considered empty
 *
 * @param value The field value
 * @param inputType The type of the input
 * @returns True if the value should be considered empty
 */
function isValueEmpty(value: any, inputType: string): boolean {
  console.log(`[${getTimestamp()}] [isValueEmpty] Checking if value is empty:
    - Input type: ${inputType}
    - Value type: ${typeof value}
    - Value: ${JSON.stringify(value)}
    - Is undefined: ${value === undefined ? "YES" : "NO"}
    - Is null: ${value === null ? "YES" : "NO"}`);

  // Undefined or null values are always empty
  if (value === undefined || value === null) {
    console.log(`[${getTimestamp()}] [isValueEmpty] Value is undefined or null, considered EMPTY`);
    return true;
  }

  // For boolean values, they're never empty (false is a valid value)
  if (inputType === "boolean" || inputType === "bool") {
    console.log(`[${getTimestamp()}] [isValueEmpty] Boolean value ${value}, considered NOT EMPTY (false is a valid value)`);
    return false;
  }

  // For numeric values, 0 is a valid value
  if (inputType === "number" || inputType === "int" || inputType === "float") {
    console.log(`[${getTimestamp()}] [isValueEmpty] Numeric value ${value}, considered NOT EMPTY (0 is a valid value)`);
    return false;
  }

  // For strings, empty string is empty
  if (inputType === "string" || inputType === "text") {
    const isEmpty = value === "";
    console.log(`[${getTimestamp()}] [isValueEmpty] String value "${value}", considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
    return isEmpty;
  }

  // For objects and arrays, empty objects/arrays are considered empty
  if (
    (inputType === "object" || inputType === "dict" || inputType === "json") &&
    typeof value === "object"
  ) {
    const isEmpty = Object.keys(value).length === 0;
    console.log(`[${getTimestamp()}] [isValueEmpty] Object value with ${Object.keys(value).length} keys, considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
    return isEmpty;
  }

  if ((inputType === "array" || inputType === "list") && Array.isArray(value)) {
    const isEmpty = value.length === 0;
    console.log(`[${getTimestamp()}] [isValueEmpty] Array value with ${value.length} items, considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
    return isEmpty;
  }

  // For string representations of objects/arrays, check if they're empty
  if (
    (inputType === "object" || inputType === "dict" || inputType === "json" ||
     inputType === "array" || inputType === "list") &&
    typeof value === "string"
  ) {
    const trimmed = value.trim();
    const isEmpty = trimmed === "" || trimmed === "{}" || trimmed === "[]";
    console.log(`[${getTimestamp()}] [isValueEmpty] String representation of object/array: "${trimmed}", considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
    return isEmpty;
  }

  // Default case - empty string is empty
  const isEmpty = value === "";
  console.log(`[${getTimestamp()}] [isValueEmpty] Default case, value: "${value}", considered ${isEmpty ? "EMPTY" : "NOT EMPTY"}`);
  return isEmpty;
}

/**
 * Checks if a handle input is connected to another node
 *
 * @param nodeId The ID of the node containing the handle
 * @param inputName The name of the handle input
 * @param edges The array of edges in the workflow
 * @returns True if the handle is connected to another node
 */
function isHandleConnected(nodeId: string, inputName: string, edges: Edge[]): boolean {
  // For target handles, the connection would be to a target handle with the input name
  const isConnected = edges.some(edge =>
    edge.target === nodeId && edge.targetHandle === inputName
  );

  console.log(`[${getTimestamp()}] [isHandleConnected] Checking if handle ${nodeId}.${inputName} is connected: ${isConnected ? "YES" : "NO"}`);

  return isConnected;
}

/**
 * Collects all fields (both required and optional) from connected nodes
 *
 * @param nodes The array of nodes to check
 * @param connectedNodes Set of node IDs connected to the start node
 * @param edges Optional array of edges in the workflow
 * @returns An array of field objects (both required and optional)
 */
export function collectAllFields(
  nodes: Node<WorkflowNodeData>[],
  connectedNodes: Set<string>,
  edges: Edge[] = []
): MissingField[] {
  console.log(`[${getTimestamp()}] [collectAllFields] ========== STARTING ALL FIELDS COLLECTION ==========`);
  console.log(`[${getTimestamp()}] [collectAllFields] Total nodes: ${nodes.length}, Connected nodes: ${connectedNodes.size}, Edges: ${edges.length}`);
  console.log(`[${getTimestamp()}] [collectAllFields] Connected node IDs: ${Array.from(connectedNodes).join(', ')}`);

  // Find the StartNode
  const startNode = nodes.find(node => node.data.originalType === "StartNode");

  // Get directly connected nodes and fields if StartNode exists
  let directlyConnectedNodes = new Set<string>();
  let directlyConnectedFields = new Map<string, Set<string>>();
  if (startNode) {
    // Import the functions from utils
    const { getDirectlyConnectedNodes, getDirectlyConnectedFields } = require("./utils");
    directlyConnectedNodes = getDirectlyConnectedNodes(nodes, edges, startNode.id);
    directlyConnectedFields = getDirectlyConnectedFields(nodes, edges, startNode.id);
    console.log(`[${getTimestamp()}] [collectAllFields] Directly connected node IDs: ${Array.from(directlyConnectedNodes).join(', ')}`);

    // Log directly connected fields
    directlyConnectedFields.forEach((fieldNames, nodeId) => {
      console.log(`[${getTimestamp()}] [collectAllFields] Node ${nodeId} has directly connected fields: ${Array.from(fieldNames).join(', ')}`);
    });
  }

  const allFields: MissingField[] = [];

  // Check each node
  nodes.forEach(node => {
    console.log(`[${getTimestamp()}] [collectAllFields] Examining node: ${node.id} (${node.data.label || "Unnamed"})`);

    // Skip nodes that are not connected to the start node
    if (!connectedNodes.has(node.id)) {
      console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - not connected to start node`);
      return;
    }

    // Skip nodes with no definition or inputs
    if (!node.data?.definition?.inputs) {
      console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - no definition or inputs`);
      return;
    }

    // CRITICAL FIX: Skip nodes that are connected as tools to agentic components
    // Tool parameters should be handled internally by the agent runtime, not prompted to users
    if (isNodeConnectedAsTool(node.id, edges)) {
      console.log(`[${getTimestamp()}] [collectAllFields] Skipping node ${node.id} - connected as tool to agentic component (tool parameters handled internally)`);
      return;
    }

    const inputs = node.data.definition.inputs;
    const config = node.data.config || {};

    // Log detailed node information
    console.log(`[${getTimestamp()}] [collectAllFields] Node details:
      - ID: ${node.id}
      - Label: ${node.data.label || "Unnamed"}
      - Type: ${node.data.type}
      - Original Type: ${node.data.originalType}
      - Position: (${node.position.x}, ${node.position.y})
      - Total inputs: ${inputs.length}
      - Config keys: ${Object.keys(config).join(', ') || "none"}
      - Is MCP component: ${isMCPMarketplaceComponent(node) ? "YES" : "NO"}`);

    // Check each input
    inputs.forEach(input => {
      console.log(`[${getTimestamp()}] [collectAllFields] Examining input: ${input.name} (${input.display_name || input.name})`);

      // Check if the input should be visible based on visibility rules
      const isVisible = checkInputVisibility(input, node, config);
      if (!isVisible) {
        console.log(`[${getTimestamp()}] [collectAllFields] Skipping input ${input.name} - not visible based on visibility rules`);
        return;
      }

      // For handle inputs, check if they're connected to other nodes
      let isConnected = false;
      if (input.is_handle) {
        isConnected = isHandleConnected(node.id, input.name, edges);

        // We no longer skip connected handles - we need to include them in the field list
        // but mark them as connected so they don't show up in the execution dialog
        if (isConnected) {
          console.log(`[${getTimestamp()}] [collectAllFields] Handle input ${input.name} is connected, including in field list`);
        } else {
          console.log(`[${getTimestamp()}] [collectAllFields] Handle input ${input.name} is not connected, including in field list`);
        }
      }

      // Log detailed input information
      console.log(`[${getTimestamp()}] [collectAllFields] Input details:
        - Name: ${input.name}
        - Display Name: ${input.display_name || input.name}
        - Type: ${input.input_type}
        - Required flag: ${input.required === undefined ? "undefined" : input.required}
        - Is handle: ${input.is_handle ? "YES" : "NO"}
        - Is connected: ${isConnected ? "YES" : "NO"}
        - Has info: ${input.info ? "YES" : "NO"}
        - Current config value: ${JSON.stringify(config[input.name])}`);

      // Determine if this field is required
      const required = isFieldRequired(node, input, isConnected);

      // Check if the field has a value
      const isEmpty = isValueEmpty(config[input.name], input.input_type);

      console.log(`[${getTimestamp()}] [collectAllFields] Field ${input.name} details:
        - Required: ${required ? "YES" : "NO"}
        - Is Empty: ${isEmpty ? "YES" : "NO"}
        - Value: ${JSON.stringify(config[input.name])}`);

      // Check if this specific field is directly connected to the Start node
      const isNodeDirectlyConnected = directlyConnectedNodes.has(node.id);
      const isFieldDirectlyConnected = directlyConnectedFields.get(node.id)?.has(input.name) || false;
      const isRequired = input.required !== false; // Consider required unless explicitly marked as optional

      // Log detailed connection information for debugging
      console.log(`[${getTimestamp()}] [collectAllFields] Field ${node.data.label || ""}.${input.name} direct connection check:
        - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
        - Field handle: ${input.name}
        - Field directly connected fields map has node: ${directlyConnectedFields.has(node.id) ? "YES" : "NO"}
        - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}
        - All directly connected fields for this node: ${directlyConnectedFields.get(node.id) ? Array.from(directlyConnectedFields.get(node.id) || []).join(', ') : "none"}`);

      // FIXED: Always include directly connected fields, regardless of whether they're required
      // For other fields, include them if they're required
      if (isRequired || isFieldDirectlyConnected) {
        // Ensure we have valid values for all fields
        const nodeName = node.data.label || "Unknown Node";
        const fieldName = input.name || "unnamed_field";
        const displayName = input.display_name || input.name || "Unnamed Field";
        const inputType = input.input_type || "string";

        console.log(`[${getTimestamp()}] [collectAllFields] Adding field with validated properties:
          - Node Name: ${nodeName}
          - Field Name: ${fieldName}
          - Display Name: ${displayName}
          - Input Type: ${inputType}
          - Required: ${isRequired ? "YES" : "NO"}
          - Directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}
          - Is Empty: ${isEmpty ? "YES" : "NO"}`);

        // Extract schema information for JSON objects
        let schema = undefined;
        if (input.input_type === 'json' || input.input_type === 'object' || input.input_type === 'dict') {
          // Check if this is an MCP component with schema information
          if (node.data.definition?.mcp_info?.input_schema?.properties?.[input.name]) {
            // Get schema from MCP input schema
            const mcpSchema = node.data.definition.mcp_info.input_schema.properties[input.name];
            schema = {
              type: mcpSchema.type,
              properties: mcpSchema.properties || {},
              required: mcpSchema.required || []
            };
            console.log(`[${getTimestamp()}] [collectAllFields] Found MCP schema for field ${input.name}:`, schema);
          } else if (input.properties) {
            // Get schema from input properties
            schema = {
              type: 'object',
              properties: input.properties,
              required: input.required_keys || []
            };
            console.log(`[${getTimestamp()}] [collectAllFields] Using input properties as schema for field ${input.name}:`, schema);
          } else if (input.name === 'keywords') {
            // Special case for keywords field
            schema = {
              type: 'object',
              properties: {
                time: { type: 'string', description: 'Time for the script' },
                objective: { type: 'string', description: 'Objective of the script' },
                audience: { type: 'string', description: 'Audience for the script' },
                gender: { type: 'string', description: 'Gender for the script' },
                tone: { type: 'string', description: 'Tone of the script' },
                speakers: { type: 'string', description: 'Speaker in the script' }
              },
              required: []
            };
            console.log(`[${getTimestamp()}] [collectAllFields] Using predefined schema for keywords field:`, schema);
          }
        }

        console.log(`[${getTimestamp()}] [collectAllFields] Field ${nodeName}.${fieldName} connection status:
          - Node connected to Start: ${connectedNodes.has(node.id) ? "YES" : "NO"}
          - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
          - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}`);

        allFields.push({
          nodeId: node.id,
          nodeName: nodeName,
          name: fieldName,
          displayName: displayName,
          info: input.info || undefined,
          inputType: inputType,
          connected_to_start: connectedNodes.has(node.id),
          directly_connected_to_start: isFieldDirectlyConnected,
          required: isRequired || isFieldDirectlyConnected, // Set required to true if directly connected
          isEmpty: isEmpty,
          currentValue: isEmpty ? undefined : unwrapDualPurposeValue(config[input.name]), // Include current value if not empty, unwrapped
          options: input.options,
          schema: schema,
          // Add handle connection properties
          is_handle: input.is_handle || false,
          is_connected: isConnected
        });

        console.log(`[${getTimestamp()}] [collectAllFields] Added field ${node.data.label || ""}.${input.name} to all fields list`);
      } else {
        console.log(`[${getTimestamp()}] [collectAllFields] Skipping field ${input.name} - not required and not directly connected to Start node`);
      }
    });
  });

  console.log(`[${getTimestamp()}] [collectAllFields] ========== ALL FIELDS COLLECTION COMPLETE ==========`);
  console.log(`[${getTimestamp()}] [collectAllFields] Total fields found: ${allFields.length}`);

  return allFields;
}

/**
 * Collects missing required fields from all nodes
 *
 * @param nodes The array of nodes to check
 * @param connectedNodes Set of node IDs connected to the start node
 * @param edges Optional array of edges in the workflow (needed to check if handles are connected)
 * @returns An array of missing field objects
 */
export function collectMissingRequiredFields(
  nodes: Node<WorkflowNodeData>[],
  connectedNodes: Set<string>,
  edges: Edge[] = []
): MissingField[] {
  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] ========== STARTING MISSING FIELDS COLLECTION ==========`);
  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Total nodes: ${nodes.length}, Connected nodes: ${connectedNodes.size}, Edges: ${edges.length}`);
  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Connected node IDs: ${Array.from(connectedNodes).join(', ')}`);

  // Find the StartNode
  const startNode = nodes.find(node => node.data.originalType === "StartNode");

  // Get directly connected nodes and fields if StartNode exists
  let directlyConnectedNodes = new Set<string>();
  let directlyConnectedFields = new Map<string, Set<string>>();
  if (startNode) {
    // Import the functions from utils
    const { getDirectlyConnectedNodes, getDirectlyConnectedFields } = require("./utils");
    directlyConnectedNodes = getDirectlyConnectedNodes(nodes, edges, startNode.id);
    directlyConnectedFields = getDirectlyConnectedFields(nodes, edges, startNode.id);
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Directly connected node IDs: ${Array.from(directlyConnectedNodes).join(', ')}`);

    // Log directly connected fields
    directlyConnectedFields.forEach((fieldNames, nodeId) => {
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Node ${nodeId} has directly connected fields: ${Array.from(fieldNames).join(', ')}`);
    });
  }

  const missingFields: MissingField[] = [];

  // Check each node
  nodes.forEach(node => {
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Examining node: ${node.id} (${node.data.label || "Unnamed"})`);

    // Skip nodes that are not connected to the start node
    if (!connectedNodes.has(node.id)) {
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - not connected to start node`);
      return;
    }

    // Skip nodes with no definition or inputs
    if (!node.data?.definition?.inputs) {
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - no definition or inputs`);
      return;
    }

    // CRITICAL FIX: Skip nodes that are connected as tools to agentic components
    // Tool parameters should be handled internally by the agent runtime, not prompted to users
    if (isNodeConnectedAsTool(node.id, edges)) {
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping node ${node.id} - connected as tool to agentic component (tool parameters handled internally)`);
      return;
    }

    const inputs = node.data.definition.inputs;
    const config = node.data.config || {};

    // Log detailed node information
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Node details:
      - ID: ${node.id}
      - Label: ${node.data.label || "Unnamed"}
      - Type: ${node.data.type}
      - Original Type: ${node.data.originalType}
      - Position: (${node.position.x}, ${node.position.y})
      - Total inputs: ${inputs.length}
      - Config keys: ${Object.keys(config).join(', ') || "none"}
      - Is MCP component: ${isMCPMarketplaceComponent(node) ? "YES" : "NO"}`);

    // Check each input
    inputs.forEach(input => {
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Examining input: ${input.name} (${input.display_name || input.name})`);

      // Check if the input should be visible based on visibility rules
      const isVisible = checkInputVisibility(input, node, config);
      if (!isVisible) {
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Skipping input ${input.name} - not visible based on visibility rules`);
        return;
      }

      // Check if this is a handle input and if it's connected
      let isConnected = false;
      if (input.is_handle) {
        isConnected = isHandleConnected(node.id, input.name, edges);

        if (isConnected) {
          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Handle input ${input.name} is connected`);
        } else {
          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Handle input ${input.name} is not connected, checking if it needs a value`);
        }
      }

      // Log detailed input information
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Input details:
        - Name: ${input.name}
        - Display Name: ${input.display_name || input.name}
        - Type: ${input.input_type}
        - Required flag: ${input.required === undefined ? "undefined" : input.required}
        - Is handle: ${input.is_handle ? "YES" : "NO"}
        - Is connected: ${isConnected ? "YES" : "NO"}
        - Has info: ${input.info ? "YES" : "NO"}
        - Current config value: ${JSON.stringify(config[input.name])}`);

      // Determine if this field is required, passing the connection status for handle inputs
      const required = isFieldRequired(node, input, isConnected);

      // Check if the field has a value
      const isEmpty = isValueEmpty(config[input.name], input.input_type);

      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} validation result:
        - Required: ${required ? "YES" : "NO"}
        - Is Empty: ${isEmpty ? "YES" : "NO"}
        - Value: ${JSON.stringify(config[input.name])}`);

      // Check if this specific field is directly connected to the Start node
      const isNodeDirectlyConnected = directlyConnectedNodes.has(node.id);
      const isFieldDirectlyConnected = directlyConnectedFields.get(node.id)?.has(input.name) || false;

      // Log detailed connection information for debugging
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${node.data.label || ""}.${input.name} direct connection check:
        - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
        - Field handle: ${input.name}
        - Field directly connected fields map has node: ${directlyConnectedFields.has(node.id) ? "YES" : "NO"}
        - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}
        - All directly connected fields for this node: ${directlyConnectedFields.get(node.id) ? Array.from(directlyConnectedFields.get(node.id) || []).join(', ') : "none"}`);

      // FIXED: Add fields that are either (required AND empty) OR directly connected to the Start node
      // This ensures that fields with direct connections from Start node are always included
      if ((required && isEmpty) || isFieldDirectlyConnected) {
        // Ensure we have valid values for all fields
        const nodeName = node.data.label || "Unknown Node";
        const fieldName = input.name || "unnamed_field";
        const displayName = input.display_name || input.name || "Unnamed Field";
        const inputType = input.input_type || "string";

        if (required && isEmpty) {
          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] FOUND MISSING REQUIRED FIELD: ${node.data.label || ""}.${input.name}`);
        } else if (isFieldDirectlyConnected) {
          console.log(`[${getTimestamp()}] [collectMissingRequiredFields] FOUND DIRECTLY CONNECTED FIELD: ${node.data.label || ""}.${input.name}`);
        }

        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Adding field with validated properties:
          - Node Name: ${nodeName}
          - Field Name: ${fieldName}
          - Display Name: ${displayName}
          - Input Type: ${inputType}
          - Is Empty: ${isEmpty ? "YES" : "NO"}
          - Current Value: ${JSON.stringify(config[input.name])}
          - Directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}`);

        // Extract schema information for JSON objects
        let schema = undefined;
        if (input.input_type === 'json' || input.input_type === 'object' || input.input_type === 'dict') {
          // Check if this is an MCP component with schema information
          if (node.data.definition?.mcp_info?.input_schema?.properties?.[input.name]) {
            // Get schema from MCP input schema
            const mcpSchema = node.data.definition.mcp_info.input_schema.properties[input.name];
            schema = {
              type: mcpSchema.type,
              properties: mcpSchema.properties || {},
              required: mcpSchema.required || []
            };
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Found MCP schema for field ${input.name}:`, schema);
          } else if (input.properties) {
            // Get schema from input properties
            schema = {
              type: 'object',
              properties: input.properties,
              required: input.required_keys || []
            };
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Using input properties as schema for field ${input.name}:`, schema);
          } else if (input.name === 'keywords') {
            // Special case for keywords field
            schema = {
              type: 'object',
              properties: {
                time: { type: 'string', description: 'Time for the script' },
                objective: { type: 'string', description: 'Objective of the script' },
                audience: { type: 'string', description: 'Audience for the script' },
                gender: { type: 'string', description: 'Gender for the script' },
                tone: { type: 'string', description: 'Tone of the script' },
                speakers: { type: 'string', description: 'Speaker in the script' }
              },
              required: []
            };
            console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Using predefined schema for keywords field:`, schema);
          }
        }

        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${nodeName}.${fieldName} connection status:
          - Node connected to Start: ${connectedNodes.has(node.id) ? "YES" : "NO"}
          - Node directly connected to Start: ${isNodeDirectlyConnected ? "YES" : "NO"}
          - Field directly connected to Start: ${isFieldDirectlyConnected ? "YES" : "NO"}`);

        missingFields.push({
          nodeId: node.id,
          nodeName: nodeName,
          name: fieldName,
          displayName: displayName,
          info: input.info || undefined,
          inputType: inputType,
          // Add handle connection properties
          is_handle: input.is_handle || false,
          is_connected: isConnected,
          connected_to_start: connectedNodes.has(node.id),
          directly_connected_to_start: isFieldDirectlyConnected,
          required: required || isFieldDirectlyConnected, // Set required to true if directly connected
          isEmpty: isEmpty, // Include whether the field is empty
          currentValue: isEmpty ? undefined : unwrapDualPurposeValue(config[input.name]), // Include current value if not empty, unwrapped
          schema: schema,
          options: input.options
        });
      } else if (required && !isEmpty) {
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} is required but already has a value, skipping`);
      } else {
        console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Field ${input.name} is not required, skipping`);
      }
    });
  });

  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] ========== MISSING FIELDS COLLECTION COMPLETE ==========`);
  console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Total missing fields found: ${missingFields.length}`);
  if (missingFields.length > 0) {
    console.log(`[${getTimestamp()}] [collectMissingRequiredFields] Missing fields summary:`);
    missingFields.forEach((field, index) => {
      console.log(`[${getTimestamp()}] [collectMissingRequiredFields]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);
    });
  }

  return missingFields;
}
