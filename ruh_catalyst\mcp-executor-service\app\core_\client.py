import asyncio
import sys
import os
import tempfile
import base64
import time
import json
import subprocess
from typing import Any, Optional, Dict, Dict, List, Union, Callable, Literal
from urllib.parse import urlparse, urlencode
from dataclasses import dataclass, field
from enum import Enum
import logging
from contextlib import asynccontextmanager

import mcp.types as types  # type: ignore
from mcp.client.session import ClientSession  # type: ignore
from mcp.client.sse import sse_client  # type: ignore
from mcp.client.stdio import stdio_client  # type: ignore
from mcp.client.streamable_http import streamablehttp_client  # type: ignore
from mcp import StdioServerParameters  # type: ignore
from app.config.config import settings
from app.services.authentication_manager import AuthenticationManager
from app.schemas.client import ConnectionConfig, AuthenticationError
from app.schemas.client import ProtocolError
from app.services.ssh_manager import SecureSSHKeyManager, get_global_ssh_manager
from app.schemas.authentication_manager import TokenValidationError

logger = logging.getLogger(__name__)


class MCPClient:
    """
    Enhanced MCP client with comprehensive authentication and protocol support.

    Features:
    - Multiple authentication schemes (Bearer, API keys, custom)
    - Robust error handling and retry logic
    - Connection pooling and health checks
    - Full MCP protocol compliance
    - Backward compatibility
    """

    def __init__(
        self,
        server_url: Optional[str] = None,
        connection_type: str = "auto",
        headers: Optional[Dict[str, str]] = None,
        docker_image: str = "mcp-text-server",
        container_command: str = None,
        auth_manager: Optional[AuthenticationManager] = None,
        connection_config: Optional[ConnectionConfig] = None,
        enable_health_check: bool = True,
        credential_service: Optional[Any] = None,  # Integration with existing service
        mcp_config: Optional[
            Dict[str, Any]
        ] = None,  # MCP configuration for smart routing
        use_fallback_ssh: bool = True,  # Enable fallback to custom SSH client
    ):
        """
        Initialize MCP Client for SSE, Streamable HTTP, or SSH Docker connection.

        Args:
            server_url: HTTP/HTTPS URL for SSE/Streamable HTTP connection
            connection_type: Connection type ("auto", "sse", "streamable_http", "ssh_docker")
            headers: Optional headers for HTTP connections (e.g., Authorization)
            docker_image: Docker image name to run (default: mcp-text-server)

        Note:
            SSH configuration is read from the application config (settings),
            which loads values from environment variables or .env file.
        """
        # Read SSH parameters from application config
        ssh_host = settings.ssh_host if settings.ssh_host else None
        ssh_user = settings.ssh_user if settings.ssh_user else None
        ssh_port = settings.ssh_port

        # Use SSH key content (base64 encoded) from .env
        ssh_key_content = settings.ssh_key_content if settings.ssh_key_content else None

        # Validate connection parameters
        has_server_url = server_url is not None
        has_ssh_params = ssh_host is not None and ssh_user is not None

        if not has_server_url and not has_ssh_params:
            # Provide helpful error message about configuration
            config_vars_available = []
            if settings.ssh_host:
                config_vars_available.append("SSH_HOST")
            if settings.ssh_user:
                config_vars_available.append("SSH_USER")

            error_msg = (
                "Either server_url must be provided for HTTP connections, "
                "or both SSH_HOST and SSH_USER must be configured for SSH Docker connection"
            )
            if config_vars_available:
                error_msg += (
                    f" (Found config values: {', '.join(config_vars_available)})"
                )
            else:
                error_msg += " (No SSH_HOST or SSH_USER configuration found)"

            raise ValueError(error_msg)

        # Validate and determine connection type
        if server_url is not None:
            parsed = urlparse(server_url)
            if parsed.scheme not in ("http", "https"):
                raise ValueError("Server URL must start with http:// or https://")

            # Auto-detect connection type based on URL path if not specified
            if connection_type == "auto":
                if "/sse" in server_url:
                    connection_type = "sse"
                elif "/mcp" in server_url:
                    connection_type = "streamable_http"
                else:
                    # Default to SSE for backward compatibility
                    connection_type = "sse"
        elif has_ssh_params:
            connection_type = "ssh_docker"

        # Store connection parameters
        self.server_url = server_url
        self.connection_type = connection_type
        self.headers = headers or {}

        # SSH Docker connection parameters
        self.ssh_host = ssh_host
        self.ssh_user = ssh_user
        self.ssh_port = ssh_port
        self.ssh_key_content = ssh_key_content
        self.docker_image = docker_image
        self.container_command = container_command

        # For SSH Docker connections, docker_image serves as container_name
        self.container_name = docker_image

        # SSH key manager for secure key handling
        self._ssh_key_manager: Optional[SecureSSHKeyManager] = None

        # Connection contexts
        self.auth_manager = auth_manager or AuthenticationManager()
        self.connection_config = connection_config or ConnectionConfig()
        self.credential_service = credential_service
        self.enable_health_check = enable_health_check
        self.mcp_config = mcp_config
        self.use_fallback_ssh = use_fallback_ssh

        # Initialize logger
        self.logger = logging.getLogger(f"{__name__}.MCPClient")

        # Debug logging for container parameters (after logger is initialized)
        self.logger.info(f"🔧 MCPClient initialized with:")
        self.logger.info(f"   docker_image: {docker_image}")
        self.logger.info(f"   container_command: {container_command}")
        self.logger.info(f"   connection_type: {connection_type}")

        # Debug SSH configuration from .env
        self.logger.debug(f"🔧 SSH Configuration from .env:")
        self.logger.debug(f"   SSH_HOST: {self.ssh_host}")
        self.logger.debug(f"   SSH_USER: {self.ssh_user}")
        self.logger.debug(f"   SSH_PORT: {self.ssh_port}")
        self.logger.debug(
            f"   SSH_KEY_CONTENT configured: {bool(self.ssh_key_content)}"
        )

        # Connection state tracking
        self._is_connected = False
        self._connection_attempts = 0
        self._last_health_check = 0.0

        # Backward compatibility: convert headers to auth manager
        if headers:
            self.auth_manager.add_custom_auth(headers=headers, name="legacy_headers")
            self.logger.debug(
                f"Legacy headers converted to auth manager: {list(headers.keys())}"
            )

        # Connection state
        self._sse_context: Optional[sse_client] = None
        self._streamable_context: Optional[streamablehttp_client] = None
        self._stdio_context: Optional[stdio_client] = None
        self._custom_ssh_client: Optional[Any] = None  # Custom SSH client for fallback
        self._fallback_mode = False  # Track if we're using fallback SSH client

        # Common session and streams
        self._session: Optional[ClientSession] = None
        self._streams: Optional[tuple] = None

    def _build_ssh_command(self):
        """Build SSH command to run Docker container on remote server."""
        ssh_cmd = ["ssh"]

        # Add SSH options with enhanced connectivity parameters
        ssh_cmd.extend(
            [
                "-o",
                "StrictHostKeyChecking=no",
                "-o",
                "UserKnownHostsFile=/dev/null",
                "-o",
                "ConnectTimeout=30",
                "-o",
                "IdentitiesOnly=yes",
                "-p",
                str(self.ssh_port),
            ]
        )

        # Handle SSH key securely using global SSH manager
        if self.ssh_key_content:
            # Use global SSH key manager
            global_ssh_manager = get_global_ssh_manager()
            actual_key_path = global_ssh_manager.get_ssh_key_path()

            if actual_key_path:
                ssh_cmd.extend(["-i", actual_key_path])
                logger.debug(
                    f"Using global SSH key for authentication: {actual_key_path}"
                )
            else:
                logger.warning(
                    "Global SSH key not available, falling back to local SSH manager"
                )
                # Fallback to local SSH key manager if global one is not initialized
                if self._ssh_key_manager is None:
                    self._ssh_key_manager = SecureSSHKeyManager()
                actual_key_path = self._ssh_key_manager.get_ssh_key_path(
                    self.ssh_key_content
                )
                ssh_cmd.extend(["-i", actual_key_path])
                logger.debug("Using local SSH key for authentication")

        # Add user@host
        ssh_cmd.append(f"{self.ssh_user}@{self.ssh_host}")

        # Always use docker exec for existing containers (API-managed containers)
        # The docker_image parameter now contains the container name/ID
        if self.container_command:
            docker_cmd = f"docker exec -i {self.docker_image} {self.container_command}"
            self.logger.info(
                f"Building SSH command for container exec: {self.docker_image} with command: {self.container_command}"
            )
        else:
            # If no specific command, use the dynamically detected command
            # This will be detected by _get_container_command method
            detected_command = (
                self.container_command or "node dist/index.js"
            )  # Default fallback
            docker_cmd = f"docker exec -i {self.docker_image} {detected_command}"
            self.logger.info(
                f"Building SSH command for container exec with detected command: {detected_command}"
            )

        ssh_cmd.append(docker_cmd)

        # Log the complete SSH command for debugging
        self.logger.info(f"Complete SSH command: {' '.join(ssh_cmd)}")

        # Also log individual components for debugging
        self.logger.debug(f"SSH host: {self.ssh_host}")
        self.logger.debug(f"SSH user: {self.ssh_user}")
        self.logger.debug(f"SSH port: {self.ssh_port}")
        self.logger.debug(f"Docker image/container: {self.docker_image}")
        self.logger.debug(f"Container command: {self.container_command}")

        return ssh_cmd

    def _create_temp_key_file(self):
        """Create temporary SSH key file and return its path."""
        # Use global SSH key manager first
        global_ssh_manager = get_global_ssh_manager()
        actual_key_path = global_ssh_manager.get_ssh_key_path()

        if actual_key_path:
            return actual_key_path
        else:
            # Fallback to local SSH key manager
            if self._ssh_key_manager is None:
                self._ssh_key_manager = SecureSSHKeyManager()
            return self._ssh_key_manager.get_ssh_key_path(self.ssh_key_content)

    async def _check_container_exists(self):
        """Check if the container exists and is running."""
        try:
            import subprocess

            key_file = self._create_temp_key_file()

            # Check if container exists and is running
            check_cmd = [
                "ssh",
                "-o",
                "StrictHostKeyChecking=no",
                "-o",
                "UserKnownHostsFile=/dev/null",
                "-o",
                "ConnectTimeout=10",
                "-i",
                key_file,
                f"{self.ssh_user}@{self.ssh_host}",
                f"docker ps --filter name={self.container_name} --format '{{{{.Names}}}}'",
            ]

            result = subprocess.run(
                check_cmd, capture_output=True, text=True, timeout=15
            )

            if result.returncode == 0:
                container_names = [
                    line.strip()
                    for line in result.stdout.strip().split("\n")
                    if line.strip()
                ]
                if self.container_name in container_names:
                    self.logger.info(f"Container {self.container_name} is running")
                    return True
                else:
                    self.logger.warning(
                        f"Container {self.container_name} not found in running containers"
                    )
                    self.logger.info(f"Available containers: {container_names}")
                    return False
            else:
                self.logger.error(f"Failed to check container status: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Error checking container existence: {e}")
            return False

    async def _get_container_command(self):
        """
        Get the command that the container is running using simplified two-step approach.
        Based on working peer implementation with global SSH key usage.
        """
        if self.container_command is not None:
            return self.container_command

        try:
            import subprocess
            import json

            # Use global SSH key (created on startup)
            from app.services.ssh_manager import get_global_ssh_manager

            global_ssh_manager = get_global_ssh_manager()
            key_file = global_ssh_manager.get_ssh_key_path()

            if not key_file:
                self.logger.warning(
                    "Global SSH key not available, falling back to temp key"
                )
                key_file = self._create_temp_key_file()

            # Step 1: Try Cmd only (primary method)
            self.logger.info("Step 1: Trying docker inspect for Cmd only")
            cmd_inspect = [
                "ssh",
                "-i",
                key_file,
                f"{self.ssh_user}@{self.ssh_host}",
                f"docker inspect {self.container_name} --format='{{{{json .Config.Cmd}}}}'",
            ]

            result = subprocess.run(
                cmd_inspect, capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                cmd_json = result.stdout.strip().strip("'\"")
                self.logger.info(f"Cmd JSON: {cmd_json}")

                if cmd_json and cmd_json != "null":
                    try:
                        cmd_list = json.loads(cmd_json)
                        if cmd_list:
                            self.container_command = " ".join(cmd_list)
                            self.logger.info(
                                f"✅ Detected command from Cmd: {self.container_command}"
                            )
                            return self.container_command
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse Cmd JSON: {e}")

            # Step 2: Fallback to Entrypoint + Cmd (proven to work)
            self.logger.info(
                "Step 2: Cmd detection failed, trying Entrypoint + Cmd fallback"
            )

            entrypoint_cmd = [
                "ssh",
                "-i",
                key_file,
                f"{self.ssh_user}@{self.ssh_host}",
                f"docker inspect {self.container_name} --format='{{{{json .Config.Entrypoint}}}} {{{{json .Config.Cmd}}}}'",
            ]

            result = subprocess.run(
                entrypoint_cmd, capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                parts = result.stdout.strip().split(" ", 1)
                entrypoint_json = parts[0].strip("'\"")
                cmd_json = parts[1].strip("'\"") if len(parts) > 1 else "null"

                self.logger.info(f"Entrypoint JSON: {entrypoint_json}")
                self.logger.info(f"Cmd JSON: {cmd_json}")

                command_parts = []

                # Add entrypoint if it exists
                if entrypoint_json and entrypoint_json != "null":
                    try:
                        entrypoint = json.loads(entrypoint_json)
                        if entrypoint:
                            command_parts.extend(entrypoint)
                            self.logger.info(f"Added entrypoint: {entrypoint}")
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse entrypoint JSON: {e}")

                # Add cmd if it exists
                if cmd_json and cmd_json != "null":
                    try:
                        cmd = json.loads(cmd_json)
                        if cmd:
                            command_parts.extend(cmd)
                            self.logger.info(f"Added cmd: {cmd}")
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse cmd JSON: {e}")

                if command_parts:
                    self.container_command = " ".join(command_parts)
                    self.logger.info(
                        f"✅ Detected command from Entrypoint+Cmd: {self.container_command}"
                    )
                    return self.container_command

            # Step 3: Final fallback (prevents complete failure)
            self.logger.warning("Could not determine container command, using default")
            self.container_command = "python server.py"
            self.logger.info(f"Using default command: {self.container_command}")
            return self.container_command

        except Exception as e:
            self.logger.error(f"Error getting container command: {e}")
            self.logger.info("Using default command: python server.py")
            self.container_command = "python server.py"
            return self.container_command

    async def _establish_ssh_docker_connection(self) -> None:
        """Establish SSH Docker connection using proven working approach."""
        # Get dynamic container command first
        if not self.container_command:
            # Dynamically detect container command if not provided
            self.container_command = await self._get_container_command()
            self.logger.info(
                f"Dynamically detected container command: {self.container_command}"
            )

        # Build simple SSH command (like working implementation)
        ssh_command = self._build_simple_ssh_command()

        self.logger.info(f"Using simple SSH approach: {' '.join(ssh_command)}")

        # Use the proven working approach - direct MCP STDIO client
        await self._establish_simple_stdio_connection(ssh_command)

    async def _try_standard_stdio_connection(self, ssh_command: list) -> None:
        """Try standard MCP STDIO client connection."""
        from mcp import StdioServerParameters

        self.logger.info(
            f"Trying standard STDIO connection with command: {' '.join(ssh_command)}"
        )

        # Add debugging for SSH key file
        ssh_key_path = None
        for i, arg in enumerate(ssh_command):
            if arg == "-i" and i + 1 < len(ssh_command):
                ssh_key_path = ssh_command[i + 1]
                break

        if ssh_key_path:
            import os

            if os.path.exists(ssh_key_path):
                self.logger.info(f"SSH key file exists: {ssh_key_path}")
                # Check file permissions
                stat_info = os.stat(ssh_key_path)
                permissions = oct(stat_info.st_mode)[-3:]
                self.logger.info(f"SSH key file permissions: {permissions}")
            else:
                self.logger.error(f"SSH key file not found: {ssh_key_path}")

        server_params = StdioServerParameters(
            command=ssh_command[0],  # "ssh"
            args=ssh_command[1:],  # All SSH arguments and remote command
            env=None,
        )

        self.logger.info("Initializing SSH stdio client...")
        self._stdio_context = stdio_client(server_params)

        self.logger.info("Entering SSH stdio context...")
        self._streams = await self._stdio_context.__aenter__()

        self.logger.info("SSH stdio streams established successfully")

    async def _try_fallback_ssh_connection(self, ssh_command: list) -> None:
        """Try fallback SSH connection using custom implementation."""
        self.logger.info(
            f"Using fallback SSH connection with command: {' '.join(ssh_command)}"
        )

        # Import the custom SSH MCP client implementation
        from app.core_.custom_ssh_client import CustomSSHMCPClient

        # Create custom SSH client
        self._custom_ssh_client = CustomSSHMCPClient(
            ssh_command=ssh_command,
            container_name=self.container_name,
            container_command=self.container_command,
            logger=self.logger,
        )

        # Establish connection and get streams
        self._streams = await self._custom_ssh_client.connect()

        self.logger.info("Fallback SSH connection established successfully")

    def _build_simple_ssh_command(self) -> List[str]:
        """Build simple SSH command using global SSH key."""
        # Use global SSH key (created on startup)
        from app.services.ssh_manager import get_global_ssh_manager

        global_ssh_manager = get_global_ssh_manager()
        key_file = global_ssh_manager.get_ssh_key_path()

        if not key_file:
            self.logger.warning(
                "Global SSH key not available, falling back to temp key"
            )
            key_file = self._create_temp_key_file()

        # Simple SSH command (like working peers' implementation)
        ssh_command = [
            "ssh",
            "-i",
            key_file,
            f"{self.ssh_user}@{self.ssh_host}",
            f"docker exec -i {self.docker_image} {self.container_command}",
        ]

        self.logger.info(f"Built simple SSH command: {' '.join(ssh_command)}")
        return ssh_command

    async def _establish_simple_stdio_connection(self, ssh_command: List[str]) -> None:
        """Establish connection using direct SSH (bypassing problematic MCP SDK on Windows)."""
        self.logger.info("Using direct SSH approach (bypassing MCP SDK)")

        # Use asyncio subprocess for proper async communication
        self.logger.info(f"Starting SSH process: {' '.join(ssh_command)}")

        # Start SSH process using asyncio subprocess
        self._ssh_process = await asyncio.create_subprocess_exec(
            *ssh_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )

        # Wait a moment for connection to establish
        await asyncio.sleep(1)

        # Check if process is still running
        if self._ssh_process.returncode is not None:
            stderr_output = (
                await self._ssh_process.stderr.read()
                if self._ssh_process.stderr
                else b""
            )
            stderr_text = stderr_output.decode()

            # Check for specific SSH errors and provide helpful messages
            if "Host key verification failed" in stderr_text:
                raise ConnectionError(
                    f"SSH Host key verification failed. This may be resolved by setting up host key verification. Error: {stderr_text}"
                )
            elif "Permission denied" in stderr_text:
                raise ConnectionError(
                    f"SSH authentication failed. Check SSH key and server configuration. Error: {stderr_text}"
                )
            elif "Connection refused" in stderr_text:
                raise ConnectionError(
                    f"SSH connection refused. Check if SSH server is running and accessible. Error: {stderr_text}"
                )
            else:
                raise ConnectionError(
                    f"SSH process terminated immediately. Error: {stderr_text}"
                )

        # Create custom streams that work with the direct SSH process
        from app.core_.direct_ssh_streams import (
            DirectSSHReadStream,
            DirectSSHWriteStream,
        )

        read_stream = DirectSSHReadStream(self._ssh_process, self.logger)
        write_stream = DirectSSHWriteStream(self._ssh_process, self.logger)

        self._streams = (read_stream, write_stream)

        self.logger.info("Direct SSH connection established successfully")

    async def _setup_ssh_host_verification(self) -> None:
        """Setup SSH host key verification to prevent connection failures."""
        try:
            from app.services.ssh_manager import (
                setup_global_ssh_host_verification_async,
            )

            self.logger.info(
                f"Setting up SSH host key verification for {self.ssh_user}@{self.ssh_host}"
            )

            # Setup host key verification using the global SSH manager
            await setup_global_ssh_host_verification_async(self.ssh_host, self.ssh_user)

            self.logger.info("SSH host key verification setup completed successfully")

        except Exception as e:
            # Log the error but don't fail the connection - the SSH options should handle it
            self.logger.warning(
                f"SSH host key verification setup failed for {self.ssh_host}, continuing with connection: {str(e)}"
            )

    def _build_ssh_docker_command(self, container_command: str):
        """Build SSH command to execute command in existing Docker container using global SSH key."""
        # Use global SSH key (created on startup)
        from app.services.ssh_manager import get_global_ssh_manager

        global_ssh_manager = get_global_ssh_manager()
        key_file = global_ssh_manager.get_ssh_key_path()

        if not key_file:
            self.logger.warning(
                "Global SSH key not available, falling back to temp key"
            )
            key_file = self._create_temp_key_file()

        return [
            "ssh",
            "-i",
            key_file,
            f"{self.ssh_user}@{self.ssh_host}",
            f"docker exec -i {self.container_name} {container_command}",
        ]

    async def __aenter__(self):
        """Enhanced connection with authentication and retry logic."""
        await self._connect_with_retry()
        return self

    async def _connect_with_retry(self) -> None:
        """Connect to MCP server with retry logic and authentication."""
        last_error = None

        for attempt in range(self.connection_config.max_retries + 1):
            try:
                self.logger.info(
                    f"Connecting to MCP server (attempt {attempt + 1}/{self.connection_config.max_retries + 1})"
                )
                await self._establish_connection()
                self._is_connected = True
                self._connection_attempts = attempt + 1
                self.logger.info(
                    f"Successfully connected to MCP server after {attempt + 1} attempts"
                )
                return

            except Exception as e:
                last_error = e
                self.logger.warning(f"Connection attempt {attempt + 1} failed: {e}")

                if attempt < self.connection_config.max_retries:
                    delay = self.connection_config.retry_delay * (
                        2**attempt
                    )  # Exponential backoff
                    self.logger.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    break

        # All attempts failed
        error_msg = f"Failed to connect to MCP server after {self.connection_config.max_retries + 1} attempts"
        if last_error:
            error_msg += f". Last error: {last_error}"

        self.logger.error(error_msg)
        raise ConnectionError(error_msg)

    async def _establish_connection(self) -> None:
        """Establish the actual connection with authentication."""
        try:
            if self.connection_type in ["sse", "streamable_http"]:
                # HTTP-based connections (SSE or Streamable HTTP)
                # Get authentication headers
                auth_headers = await self.auth_manager.get_headers()

                # Get query parameters for authentication
                query_params = await self.auth_manager.get_query_params()

                # Modify URL with query parameters if needed
                connection_url = self.server_url
                if query_params:
                    separator = "&" if "?" in connection_url else "?"
                    query_string = "&".join(
                        [f"{k}={v}" for k, v in query_params.items()]
                    )
                    connection_url = f"{connection_url}{separator}{query_string}"
                    self.logger.debug(
                        f"Added query parameters to URL: {list(query_params.keys())}"
                    )

                # Log authentication info (without exposing sensitive data)
                if auth_headers:
                    safe_headers = {
                        k: (
                            "***"
                            if "authorization" in k.lower()
                            or "token" in k.lower()
                            or "key" in k.lower()
                            else v
                        )
                        for k, v in auth_headers.items()
                    }
                    self.logger.debug(f"Using authentication headers: {safe_headers}")

                if self.connection_type == "sse":
                    # SSE connection
                    self._sse_context = sse_client(
                        connection_url,
                        headers=auth_headers,
                        timeout=self.connection_config.timeout,
                        sse_read_timeout=self.connection_config.sse_read_timeout,
                    )
                    self._streams = await self._sse_context.__aenter__()
                elif self.connection_type == "streamable_http":
                    # Streamable HTTP connection
                    self._streamable_context = streamablehttp_client(
                        connection_url, headers=auth_headers
                    )
                    streams_and_session = await self._streamable_context.__aenter__()
                    # streamablehttp_client returns (read, write, session)
                    self._streams = (streams_and_session[0], streams_and_session[1])

            elif self.connection_type == "ssh_docker":
                # Setup host key verification before SSH connection
                await self._setup_ssh_host_verification()
                # SSH Docker connection with fallback support
                await self._establish_ssh_docker_connection()

            else:
                raise ValueError(f"Unsupported connection type: {self.connection_type}")

            # Create and initialize session
            if self.connection_type == "ssh_docker":
                # Use direct MCP session for SSH connections (bypasses problematic SDK)
                from app.core_.direct_ssh_streams import DirectMCPSession

                self._session = DirectMCPSession(self._streams[0], self._streams[1])
                await self._session.__aenter__()
                await self._session.initialize()
            else:
                # Use regular MCP session for other connection types
                self._session = ClientSession(self._streams[0], self._streams[1])
                await self._session.__aenter__()
                await self._session.initialize()

            self.logger.info("MCP session initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to establish connection: {e}")
            self.logger.error(f"Exception type: {type(e).__name__}")
            self.logger.error(f"Exception details: {str(e)}")

            # Log SSH command details for debugging
            if self.connection_type == "ssh_docker":
                self.logger.error(f"SSH connection failed - Debug info:")
                self.logger.error(f"  SSH Host: {self.ssh_host}")
                self.logger.error(f"  SSH User: {self.ssh_user}")
                self.logger.error(f"  SSH Port: {self.ssh_port}")
                self.logger.error(f"  Docker Image/Container: {self.docker_image}")
                self.logger.error(f"  Container Command: {self.container_command}")

            await self._cleanup_partial_connection()

            # Convert specific errors to appropriate types
            if "authentication" in str(e).lower() or "unauthorized" in str(e).lower():
                raise AuthenticationError(f"Authentication failed: {e}")
            elif "timeout" in str(e).lower():
                raise ConnectionError(f"Connection timeout: {e}")
            else:
                raise ConnectionError(f"Connection failed: {e}")

    async def _cleanup_partial_connection(self) -> None:
        """Clean up partial connection state."""
        try:
            if self._session:
                await self._session.__aexit__(None, None, None)
                self._session = None
            if self._sse_context:
                await self._sse_context.__aexit__(None, None, None)
                self._sse_context = None
            if self._streamable_context:
                await self._streamable_context.__aexit__(None, None, None)
                self._streamable_context = None
            if self._stdio_context:
                await self._stdio_context.__aexit__(None, None, None)
                self._stdio_context = None
            if self._custom_ssh_client:
                await self._custom_ssh_client.close()
                self._custom_ssh_client = None
            if hasattr(self, "_ssh_process") and self._ssh_process:
                self._ssh_process.terminate()
                await self._ssh_process.wait()
                self._ssh_process = None
            self._streams = None
        except Exception as e:
            self.logger.warning(f"Error during connection cleanup: {e}")

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Parameters are required by the async context manager protocol
        # but not used in this implementation
        _ = exc_type, exc_val, exc_tb
        await self.close()

    async def close(self) -> None:
        """Enhanced close with proper cleanup and logging."""
        self.logger.info("Closing MCP client connection")

        try:
            if self._session is not None:
                await self._session.__aexit__(None, None, None)
                self._session = None
                self.logger.debug("MCP session closed")

            if self._sse_context is not None:
                await self._sse_context.__aexit__(None, None, None)
                self._sse_context = None
                self.logger.debug("SSE context closed")

            if self._streamable_context is not None:
                await self._streamable_context.__aexit__(None, None, None)
                self._streamable_context = None
                self.logger.debug("Streamable HTTP context closed")

            if self._stdio_context is not None:
                await self._stdio_context.__aexit__(None, None, None)
                self._stdio_context = None
                self.logger.debug("STDIO context closed")

            if self._custom_ssh_client is not None:
                await self._custom_ssh_client.close()
                self._custom_ssh_client = None
                self.logger.debug("Custom SSH client closed")

            # Clean up direct SSH process
            if hasattr(self, "_ssh_process") and self._ssh_process is not None:
                self._ssh_process.terminate()
                await self._ssh_process.wait()
                self._ssh_process = None
                self.logger.debug("Direct SSH process closed")

            # Clean up SSH key manager
            if self._ssh_key_manager is not None:
                self._ssh_key_manager.cleanup_temp_key_file()
                self._ssh_key_manager = None

            self._streams = None
            self._is_connected = False
            self.logger.info("MCP client connection closed successfully")

        except Exception as e:
            self.logger.error(f"Error during client close: {e}")
            raise

    async def _ensure_connected(self) -> None:
        """Enhanced connection check with health monitoring."""
        if not self._is_connected or self._session is None:
            raise ConnectionError("Client is not connected to MCP server")

        # Check connection type specific contexts
        if self.connection_type == "sse" and self._sse_context is None:
            raise ConnectionError("SSE client is not connected")
        elif (
            self.connection_type == "streamable_http"
            and self._streamable_context is None
        ):
            raise ConnectionError("Streamable HTTP client is not connected")
        elif self.connection_type == "ssh_docker":
            # Check for direct SSH process (new approach)
            if hasattr(self, "_ssh_process") and self._ssh_process is not None:
                if self._ssh_process.returncode is not None:
                    raise ConnectionError("SSH Docker process has terminated")
            # Check for legacy approaches
            elif not self._fallback_mode and self._stdio_context is None:
                raise ConnectionError("SSH Docker STDIO client is not connected")
            elif self._fallback_mode and self._custom_ssh_client is None:
                raise ConnectionError("SSH Docker fallback client is not connected")

        # Perform health check if enabled
        if self.enable_health_check:
            await self._perform_health_check()

    async def _perform_health_check(self) -> None:
        """Perform health check if needed."""
        current_time = time.time()

        if (
            current_time - self._last_health_check
        ) >= self.connection_config.health_check_interval:
            try:
                # Simple ping to check connection health
                await self._session.send_ping()
                self._last_health_check = current_time
                self.logger.debug("Health check passed")

            except Exception as e:
                self.logger.warning(f"Health check failed: {e}")
                # Could implement reconnection logic here if needed
                raise ConnectionError(f"Health check failed: {e}")

    # Authentication Integration Methods
    async def set_oauth_credentials(
        self,
        access_token: str,
        expires_at: Optional[float] = None,
        refresh_callback: Optional[Callable[[], str]] = None,
    ) -> None:
        """Set OAuth credentials for authentication."""
        if not self.auth_manager.validate_token(access_token):
            raise TokenValidationError("Invalid access token format")

        self.auth_manager.add_bearer_token(
            token=access_token, expires_at=expires_at, refresh_callback=refresh_callback
        )
        self.logger.info("OAuth credentials configured")

    async def set_api_key(
        self,
        api_key: str,
        location: Literal["header", "query", "body"] = "header",
        key_name: str = "X-API-Key",
    ) -> None:
        """Set API key authentication."""
        self.auth_manager.add_api_key(
            api_key=api_key, location=location, key_name=key_name
        )
        self.logger.info(f"API key configured for {location}")

    async def integrate_credential_service(self, user_id: str, tool_name: str) -> bool:
        """
        Integrate with existing credential service using simplified composite key format.

        Args:
            user_id: The user identifier
            tool_name: The name of the tool requiring authentication

        Returns:
            bool: True if credentials were successfully integrated, False otherwise
        """
        if not self.credential_service:
            self.logger.warning("No credential service configured")
            return False

        try:
            # Use the simplified composite key format (without mcp_id)
            composite_key = f"{user_id}_{tool_name}"
            self.logger.info(
                f"Integrating credentials for composite key: {composite_key}"
            )

            # Retrieve credentials using the credential service
            credentials = await self.credential_service.get_oauth_credentials(
                user_id=user_id, tool_name=tool_name
            )

            if credentials and credentials.get("success"):
                access_token = credentials.get("access_token")
                token_type = credentials.get("token_type", "Bearer")
                expires_in = credentials.get("expires_in", 0)

                if access_token:
                    # Calculate expiration time if provided
                    expires_at = None
                    if expires_in and expires_in > 0:
                        import time

                        expires_at = time.time() + expires_in

                    # Set OAuth credentials with proper token type validation
                    if token_type.lower() != "bearer":
                        self.logger.warning(
                            f"Unexpected token type: {token_type}, treating as Bearer"
                        )

                    await self.set_oauth_credentials(
                        access_token=access_token, expires_at=expires_at
                    )

                    self.logger.info(
                        f"Successfully integrated OAuth credentials for: {composite_key}"
                    )
                    return True
                else:
                    self.logger.warning(
                        f"No access token in credential service response for: {composite_key}"
                    )
                    return False
            else:
                error_msg = (
                    credentials.get("message", "Unknown error")
                    if credentials
                    else "No response"
                )
                self.logger.warning(
                    f"Failed to retrieve credentials for {composite_key}: {error_msg}"
                )
                return False

        except Exception as e:
            self.logger.error(
                f"Error integrating credential service for {user_id}_{tool_name}: {e}"
            )
            # Don't raise exception here - let the caller decide how to handle missing credentials
            return False

    # Enhanced MCP Protocol Methods with Error Handling and Logging

    async def list_resources(self) -> list[types.Resource]:
        await self._ensure_connected()
        try:
            self.logger.debug("Listing MCP resources")
            resources_result = await self._session.list_resources()  # type: ignore

            # Extract the actual resources list from the result
            if hasattr(resources_result, "resources"):
                resources = resources_result.resources
            else:
                # Fallback for different MCP SDK versions
                resources = resources_result

            self.logger.info(f"Retrieved {len(resources)} resources")
            return resources
        except Exception as e:
            self.logger.error(f"Failed to list resources: {e}")
            raise ProtocolError(f"Failed to list resources: {e}")

    async def read_resource(
        self, resource_uri: str
    ) -> List[Union[types.TextResourceContents, types.BlobResourceContents]]:
        """Read resource content with enhanced error handling."""
        await self._ensure_connected()
        try:
            self.logger.debug(f"Reading resource: {resource_uri}")
            content = await self._session.read_resource(resource_uri)  # type: ignore
            self.logger.info(f"Successfully read resource: {resource_uri}")
            return content
        except Exception as e:
            self.logger.error(f"Failed to read resource {resource_uri}: {e}")
            raise ProtocolError(f"Failed to read resource {resource_uri}: {e}")

    async def list_tools(self) -> List[types.Tool]:
        """List available tools with enhanced error handling."""
        await self._ensure_connected()
        try:
            self.logger.debug("Listing MCP tools")
            tools_result = await self._session.list_tools()  # type: ignore

            # Extract the actual tools list from the result
            if hasattr(tools_result, "tools"):
                tools = tools_result.tools
            else:
                # Fallback for different MCP SDK versions
                tools = tools_result

            self.logger.info(f"Retrieved {len(tools)} tools")
            return tools
        except Exception as e:
            self.logger.error(f"Failed to list tools: {e}")
            raise ProtocolError(f"Failed to list tools: {e}")

    async def call_tool(
        self, tool_name: str, arguments: Dict[str, Any]
    ) -> types.CallToolResult:
        """Call tool with enhanced error handling and authentication."""
        await self._ensure_connected()
        try:
            self.logger.debug(f"Calling tool: {tool_name}")

            # Add authentication parameters if needed
            enhanced_arguments = arguments.copy()
            body_params = await self.auth_manager.get_body_params()
            if body_params:
                enhanced_arguments.update(body_params)
                self.logger.debug("Added authentication parameters to tool arguments")

            result = await self._session.call_tool(tool_name, enhanced_arguments)  # type: ignore
            self.logger.info(f"Successfully called tool: {tool_name}")
            return result

        except Exception as e:
            self.logger.error(f"Failed to call tool {tool_name}: {e}")

            # Check if it's an authentication error
            if "authentication" in str(e).lower() or "unauthorized" in str(e).lower():
                raise AuthenticationError(
                    f"Authentication failed for tool {tool_name}: {e}"
                )
            else:
                raise ProtocolError(f"Failed to call tool {tool_name}: {e}")

    async def fetch_tools(self):
        """Fetch tools list using MCP stdio SDK."""
        try:
            # Get the container's command
            container_command = await self._get_container_command()

            # Build SSH + Docker command
            ssh_command = self._build_ssh_docker_command(container_command)

            self.logger.info(f"Fetching tools using command: {' '.join(ssh_command)}")

            # Use the existing connection mechanism but with dynamic command
            # This integrates with the existing _establish_connection flow
            return await self.list_tools()

        except Exception as e:
            self.logger.error(f"Failed to fetch tools: {e}")
            raise ProtocolError(f"Failed to fetch tools: {e}")

    async def list_prompts(self) -> List[types.Prompt]:
        """List available prompts with enhanced error handling."""
        await self._ensure_connected()
        try:
            self.logger.debug("Listing MCP prompts")
            prompts_result = await self._session.list_prompts()  # type: ignore

            # Extract the actual prompts list from the result
            if hasattr(prompts_result, "prompts"):
                prompts = prompts_result.prompts
            else:
                # Fallback for different MCP SDK versions
                prompts = prompts_result

            self.logger.info(f"Retrieved {len(prompts)} prompts")
            return prompts
        except Exception as e:
            self.logger.error(f"Failed to list prompts: {e}")
            raise ProtocolError(f"Failed to list prompts: {e}")

    async def get_prompt(
        self, prompt_name: str, arguments: Optional[Dict[str, Any]] = None
    ) -> List[types.PromptMessage]:
        """Get prompt with enhanced error handling."""
        await self._ensure_connected()
        try:
            self.logger.debug(f"Getting prompt: {prompt_name}")
            messages = await self._session.get_prompt(prompt_name, arguments or {})  # type: ignore
            self.logger.info(f"Successfully retrieved prompt: {prompt_name}")
            return messages
        except Exception as e:
            self.logger.error(f"Failed to get prompt {prompt_name}: {e}")
            raise ProtocolError(f"Failed to get prompt {prompt_name}: {e}")

    async def subscribe_resource(self, resource_uri: str) -> Any:
        """Subscribe to resource with enhanced error handling."""
        await self._ensure_connected()
        try:
            self.logger.debug(f"Subscribing to resource: {resource_uri}")
            result = await self._session.subscribe_resource(resource_uri)  # type: ignore
            self.logger.info(f"Successfully subscribed to resource: {resource_uri}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to subscribe to resource {resource_uri}: {e}")
            raise ProtocolError(f"Failed to subscribe to resource {resource_uri}: {e}")

    async def unsubscribe_resource(self, resource_uri: str) -> Any:
        """Unsubscribe from resource with enhanced error handling."""
        await self._ensure_connected()
        try:
            self.logger.debug(f"Unsubscribing from resource: {resource_uri}")
            result = await self._session.unsubscribe_resource(resource_uri)  # type: ignore
            self.logger.info(f"Successfully unsubscribed from resource: {resource_uri}")
            return result
        except Exception as e:
            self.logger.error(
                f"Failed to unsubscribe from resource {resource_uri}: {e}"
            )
            raise ProtocolError(
                f"Failed to unsubscribe from resource {resource_uri}: {e}"
            )

    # Additional utility methods

    async def ping(self) -> bool:
        """Ping the MCP server to check connectivity."""
        await self._ensure_connected()
        try:
            await self._session.send_ping()
            self.logger.debug("Ping successful")
            return True
        except Exception as e:
            self.logger.warning(f"Ping failed: {e}")
            return False

    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information and statistics."""
        return {
            "server_url": self.server_url,
            "is_connected": self._is_connected,
            "connection_attempts": self._connection_attempts,
            "last_health_check": self._last_health_check,
            "auth_configured": bool(self.auth_manager.default_config),
            "health_check_enabled": self.enable_health_check,
            "timeout": self.connection_config.timeout,
            "sse_read_timeout": self.connection_config.sse_read_timeout,
            "max_retries": self.connection_config.max_retries,
        }

    async def test_authentication(self) -> bool:
        """Test if authentication is working by attempting to list tools."""
        try:
            await self.list_tools()
            self.logger.info("Authentication test passed")
            return True
        except AuthenticationError:
            self.logger.warning("Authentication test failed")
            return False
        except Exception as e:
            self.logger.warning(f"Authentication test inconclusive: {e}")
            return False


# Factory Functions for Easy Integration


def create_authenticated_client(
    server_url: str,
    access_token: Optional[str] = None,
    api_key: Optional[str] = None,
    api_key_location: Literal["header", "query", "body"] = "header",
    api_key_name: str = "X-API-Key",
    custom_headers: Optional[Dict[str, str]] = None,
    timeout: float = 30.0,
    max_retries: int = 3,
    enable_health_check: bool = True,
    credential_service: Optional[Any] = None,
) -> MCPClient:
    """
    Factory function to create an authenticated MCP client.

    Args:
        server_url: MCP server URL
        access_token: OAuth Bearer token
        api_key: API key for authentication
        api_key_location: Where to place the API key (header, query, body)
        api_key_name: Name of the API key parameter
        custom_headers: Custom headers for authentication
        timeout: Connection timeout
        max_retries: Maximum retry attempts
        enable_health_check: Enable health check monitoring
        credential_service: Existing credential service integration

    Returns:
        Configured MCPClient instance
    """
    auth_manager = AuthenticationManager()

    # Configure authentication
    if access_token:
        auth_manager.add_bearer_token(access_token)

    if api_key:
        auth_manager.add_api_key(
            api_key=api_key, location=api_key_location, key_name=api_key_name
        )

    if custom_headers:
        auth_manager.add_custom_auth(headers=custom_headers)

    # Configure connection
    connection_config = ConnectionConfig(
        timeout=timeout,
        max_retries=max_retries,
        enable_health_check=enable_health_check,
    )

    return MCPClient(
        server_url=server_url,
        auth_manager=auth_manager,
        connection_config=connection_config,
        enable_health_check=enable_health_check,
        credential_service=credential_service,
    )


def create_legacy_client(
    server_url: str, headers: Optional[Dict[str, str]] = None
) -> MCPClient:
    """
    Factory function for backward compatibility with the original MCPClient interface.

    Args:
        server_url: MCP server URL
        headers: Legacy headers (converted to auth manager)

    Returns:
        MCPClient instance with legacy compatibility
    """
    return MCPClient(server_url=server_url, headers=headers)


def create_client_with_credential_service(
    server_url: str,
    user_id: str,
    tool_name: str,
    credential_service: Optional[Any] = None,
    timeout: float = 30.0,
    max_retries: int = 3,
    enable_health_check: bool = True,
) -> MCPClient:
    """
    Factory function to create an MCP client with automatic credential service integration.

    This function creates a client and automatically attempts to retrieve and configure
    OAuth credentials using the simplified composite key format.

    Args:
        server_url: MCP server URL
        user_id: User identifier
        tool_name: Tool name requiring authentication
        credential_service: Credential service instance (if None, creates new one)
        timeout: Connection timeout
        max_retries: Maximum retry attempts
        enable_health_check: Enable health check monitoring

    Returns:
        Configured MCPClient instance with authentication if credentials are available
    """
    # Create credential service if not provided
    if credential_service is None:
        try:
            from app.services.credential_service import CredentialService

            credential_service = CredentialService()
        except ImportError:
            # Credential service not available, create basic client
            logger.warning(
                "Credential service not available, creating client without authentication"
            )
            return MCPClient(
                server_url=server_url,
                connection_config=ConnectionConfig(
                    timeout=timeout,
                    max_retries=max_retries,
                    enable_health_check=enable_health_check,
                ),
            )

    # Create client with credential service integration
    auth_manager = AuthenticationManager()
    connection_config = ConnectionConfig(
        timeout=timeout,
        max_retries=max_retries,
        enable_health_check=enable_health_check,
    )

    client = MCPClient(
        server_url=server_url,
        auth_manager=auth_manager,
        connection_config=connection_config,
        enable_health_check=enable_health_check,
        credential_service=credential_service,
    )

    logger.info(f"Created MCP client with credential service for {user_id}_{tool_name}")
    return client


# Enhanced example usage
async def main(server_url: Optional[str] = None, docker_image: str = "mcp-text-server"):
    """Enhanced example with authentication and error handling."""
    try:
        # Example 1: Basic client (backward compatible)
        logger.info("=== Basic Client Example ===")

        if server_url:
            async with create_legacy_client(server_url) as client:
                logger.info(f"Connected to MCP server at {server_url}")
                # Test basic functionality
                tools = await client.list_tools()
                logger.info(f"Available tools: {[tool.name for tool in tools]}")

                resources = await client.list_resources()
                logger.info(f"Available resources: {len(resources)}")

                prompts = await client.list_prompts()
                logger.info(f"Available prompts: {len(prompts)}")
        else:
            # SSH Docker connection example
            async with MCPClient(
                docker_image=docker_image, connection_type="ssh_docker"
            ) as client:
                logger.info("Connected to SSH Docker MCP server using config settings")

                tools = await client.list_tools()
                logger.info(f"Available tools: {[tool.name for tool in tools]}")

                resources = await client.list_resources()
                logger.info(f"Available resources: {len(resources)}")

                prompts = await client.list_prompts()
                logger.info(f"Available prompts: {len(prompts)}")

        # Example 2: Authenticated client
        logger.info("=== Authenticated Client Example ===")

        # This would typically come from environment or credential service
        access_token = "your-access-token-here"  # Replace with actual token

        if server_url:
            auth_client = create_authenticated_client(
                server_url=server_url,
                access_token=access_token,
                enable_health_check=True,
                max_retries=3,
            )

            async with auth_client as client:
                # Test authentication
                auth_working = await client.test_authentication()
                logger.info(
                    f"Authentication test: {'PASSED' if auth_working else 'FAILED'}"
                )

                # Get connection info
                info = client.get_connection_info()
                logger.info(f"Connection info: {info}")

                # Test tool call with authentication
                tools = await client.list_tools()
                if tools:
                    # Example tool call (replace with actual tool)
                    # result = await client.call_tool("example_tool", {"param": "value"})
                    logger.info("Tool call would be executed here")

    except AuthenticationError as e:
        logger.error(f"Authentication error: {e}")
        sys.exit(1)
    except ConnectionError as e:
        logger.error(f"Connection error: {e}")
        sys.exit(1)
    except ProtocolError as e:
        logger.error(f"Protocol error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) == 2:
        # SSE connection
        asyncio.run(main(sys.argv[1]))
    elif len(sys.argv) >= 3:
        # SSH Docker connection - basic example
        # Usage: python client.py ssh <host> <user> [port] [key_path] [image]
        if sys.argv[1] == "ssh":
            # SSH Docker connection - uses config settings
            docker_image = sys.argv[2] if len(sys.argv) > 2 else "mcp-text-server"
            asyncio.run(main(docker_image=docker_image))
        else:
            asyncio.run(main(sys.argv[1]))
    else:
        logger.info("Enhanced MCP Client")
        logger.info("Usage:")
        logger.debug("  SSE: python client.py <server_url>")
        logger.debug("  SSH: uv run client.py ssh [docker_image]")
        logger.info("Examples:")
        logger.debug("  python client.py http://localhost:8080/sse")
        logger.info("")
        logger.info("Features:")
        logger.info("- Multiple authentication schemes (Bearer, API keys, custom)")
        logger.info("- Robust error handling and retry logic")
        logger.info("- Connection health monitoring")
        logger.info("- Full MCP protocol compliance")
        logger.info("- Backward compatibility")
        logger.debug("  uv run client.py ssh mcp-text-server")
        logger.debug(
            "Note: SSH connection uses config settings (SSH_HOST, SSH_USER, etc.)"
        )
        sys.exit(1)
