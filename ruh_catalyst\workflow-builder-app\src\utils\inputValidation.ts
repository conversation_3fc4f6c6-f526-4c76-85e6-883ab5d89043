import { InputDefinition } from "@/types";
import { z } from "zod";
import { DynamicInputValidator } from "@/lib/dynamicInputValidation";

/**
 * Validates an input value based on its definition
 * Now uses the universal dynamic validation system
 * @param inputDef The input definition
 * @param value The value to validate
 * @returns Validation result with isValid flag and message
 */
export function validateInput(
  inputDef: InputDefinition,
  value: any
): { isValid: boolean; message: string } {
  try {
    // Use the universal dynamic validation system
    const result = DynamicInputValidator.validateInput(inputDef, value);
    return { isValid: result.isValid, message: result.message };
  } catch (error) {
    console.error(`[INPUT VALIDATION] Dynamic validation failed for ${inputDef.name}:`, error);

    // Emergency fallback - basic validation
    if (inputDef.required && (value === undefined || value === null || value === "")) {
      return { isValid: false, message: "This field is required" };
    }

    return { isValid: true, message: "Validation unavailable - value accepted" };
  }
}

// CLEANED UP: Legacy validation functions removed - now handled by universal dynamic validation system

/**
 * Validates all inputs in a node configuration
 * @param inputs Array of input definitions
 * @param config The current configuration values
 * @returns Object with validation results for each input
 */
export function validateAllInputs(
  inputs: InputDefinition[],
  config: Record<string, any>
): Record<string, { isValid: boolean; message: string }> {
  const errors: Record<string, { isValid: boolean; message: string }> = {};

  // Validate each input
  inputs.forEach((inputDef) => {
    // Skip handle inputs
    if (inputDef.is_handle) return;

    // Get current value
    const value = config[inputDef.name];

    // Validate
    errors[inputDef.name] = validateInput(inputDef, value);
  });

  return errors;
}

/**
 * Creates a Zod schema for an input definition
 * Now uses the universal dynamic validation system
 * @param inputDef The input definition
 * @returns A Zod schema for the input
 */
export function createSchemaForInput(inputDef: InputDefinition): z.ZodTypeAny {
  try {
    // Use the dynamic validation system to create schema
    return DynamicInputValidator.createZodSchema(inputDef);
  } catch (error) {
    console.warn(`[INPUT VALIDATION] Failed to create dynamic schema for ${inputDef.name}, using fallback:`, error);

    // Emergency fallback - basic schema
    if (inputDef.required) {
      return z.any().refine(val => val !== undefined && val !== null && val !== "", {
        message: "This field is required"
      });
    } else {
      return z.any().optional();
    }
  }
}

/**
 * Creates a Zod schema for a complete node configuration
 * @param inputs Array of input definitions
 * @returns A Zod schema for the node configuration
 */
export function createNodeConfigSchema(inputs: InputDefinition[]): z.ZodObject<any> {
  const shape: Record<string, z.ZodTypeAny> = {};
  
  inputs.forEach((input) => {
    // Skip handle inputs
    if (input.is_handle) return;
    
    shape[input.name] = createSchemaForInput(input);
  });
  
  return z.object(shape);
}
