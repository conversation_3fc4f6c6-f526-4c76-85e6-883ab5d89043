# test_agent_executor.py

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, <PERSON><PERSON>, patch
from app.services.agent_executor import Agent<PERSON><PERSON><PERSON>or, AgentExecutionError


class TestAgentExecutor:
    """Test suite for AgentExecutor class."""

    @pytest.fixture
    def mock_producer(self):
        """Create a mock Kafka producer."""
        producer = Mo<PERSON>()
        producer._sender = <PERSON>ck()
        producer._sender._running = True
        producer.send = AsyncMock()
        return producer

    @pytest.fixture
    def agent_executor(self, mock_producer):
        """Create an AgentExecutor instance with mocked dependencies."""
        with patch("app.services.agent_executor.settings") as mock_settings:
            mock_settings.kafka_bootstrap_servers = "localhost:9092"
            mock_settings.kafka_agent_execution_request_topic = (
                "agent-execution-request"
            )
            mock_settings.kafka_agent_execution_result_topic = "agent_results"

            executor = AgentExecutor(producer=mock_producer)
            return executor

    def test_initialization(self, agent_executor, mock_producer):
        """Test successful initialization of Agent<PERSON>xe<PERSON><PERSON>."""
        assert agent_executor.producer == mock_producer
        assert agent_executor._bootstrap_servers == "localhost:9092"
        assert agent_executor._request_topic == "agent-execution-request"
        assert agent_executor._results_topic == "agent_results"
        assert agent_executor._consumer_group_id == "agent-executor-consumer"
        assert agent_executor._current_correlation_id is None
        assert agent_executor._current_user_id is None

    def test_initialization_with_none_producer(self):
        """Test initialization with None producer raises ValueError."""
        with pytest.raises(
            ValueError, match="A running AIOKafkaProducer instance must be provided"
        ):
            AgentExecutor(producer=None)

    def test_set_correlation_id(self, agent_executor):
        """Test setting correlation ID."""
        test_id = "test-correlation-123"
        agent_executor.set_correlation_id(test_id)
        assert agent_executor._current_correlation_id == test_id

    def test_set_user_id(self, agent_executor):
        """Test setting user ID."""
        test_user_id = "test-user-456"
        agent_executor.set_user_id(test_user_id)
        assert agent_executor._current_user_id == test_user_id

    @pytest.mark.asyncio
    async def test_execute_tool_basic(self, agent_executor, mock_producer):
        """Test basic tool execution with component agent."""
        # Mock the consumer and task
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        # Mock the future for the pending request
        mock_future = asyncio.Future()
        mock_future.set_result({"success": True, "content": "Hello from agent"})

        with patch("uuid.uuid4") as mock_uuid:
            mock_uuid.return_value.hex = "test-request-id"
            mock_uuid.return_value.__str__ = lambda x: "test-request-id"

            with patch("asyncio.Future", return_value=mock_future):
                result = await agent_executor.execute_tool(
                    server_script_path="test/path",
                    tool_name="test_agent",
                    tool_parameters={
                        "agent_type": "component",
                        "execution_type": "response",
                        "query": "Hello, agent!",
                        "agent_config": {"id": "test-agent", "name": "Test Agent"},
                    },
                )

                assert result == {"success": True, "content": "Hello from agent"}
                mock_producer.send.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_tool_with_agent_id(self, agent_executor, mock_producer):
        """Test tool execution with agent_id parameter."""
        # Mock the consumer and task
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        # Mock the future for the pending request
        mock_future = asyncio.Future()
        mock_future.set_result(
            {"success": True, "content": "Hello from specific agent"}
        )

        with patch("uuid.uuid4") as mock_uuid:
            mock_uuid.return_value.hex = "test-request-id-2"
            mock_uuid.return_value.__str__ = lambda x: "test-request-id-2"

            with patch("asyncio.Future", return_value=mock_future):
                result = await agent_executor.execute_tool(
                    server_script_path="test/path",
                    tool_name="test_agent",
                    tool_parameters={
                        "agent_type": "component",
                        "execution_type": "interactive",
                        "query": "Hello, specific agent!",
                    },
                    agent_id="specific-agent-123",
                )

                assert result == {
                    "success": True,
                    "content": "Hello from specific agent",
                }

                # Verify the call was made with agent_id in the payload
                call_args = mock_producer.send.call_args
                sent_payload = call_args[1]["value"]
                assert sent_payload["agent_id"] == "specific-agent-123"
                assert sent_payload["agent_type"] == "component"
                assert sent_payload["execution_type"] == "interactive"

    @pytest.mark.asyncio
    async def test_execute_tool_consumer_not_running(self, agent_executor):
        """Test execute_tool raises error when consumer is not running."""
        agent_executor._consumer = None

        with pytest.raises(
            RuntimeError, match="AgentExecutor's internal consumer is not running"
        ):
            await agent_executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "component",
                    "execution_type": "response",
                    "query": "test query",
                },
            )

    @pytest.mark.asyncio
    async def test_execute_tool_producer_not_running(self, agent_executor):
        """Test execute_tool raises error when producer is not running."""
        # Mock consumer as running
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        # Mock producer as not running
        agent_executor.producer._sender._running = False

        with pytest.raises(
            RuntimeError, match="The provided Kafka Producer is not running"
        ):
            await agent_executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "component",
                    "execution_type": "response",
                    "query": "test query",
                },
            )

    def test_get_default_agent_config(self, agent_executor):
        """Test the default agent configuration."""
        config = agent_executor._get_default_agent_config()

        assert config["id"] == "orchestration-agent-001"
        assert config["name"] == "Orchestration Assistant"
        assert "system_message" in config
        assert "model_config" in config
        assert config["model_config"]["model"] == "gpt-4o-mini"
        assert config["model_config"]["model_provider"] == "openai"
        assert config["termination_condition"] == "auto"
        assert config["tools"] == []
        assert "capabilities" in config

    @pytest.mark.asyncio
    async def test_invalid_agent_type(self, agent_executor):
        """Test execute_tool raises error for invalid agent type."""
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        with pytest.raises(ValueError, match="Invalid agent_type 'invalid'"):
            await agent_executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "invalid",
                    "execution_type": "response",
                    "query": "test query",
                },
            )

    @pytest.mark.asyncio
    async def test_invalid_execution_type(self, agent_executor):
        """Test execute_tool raises error for invalid execution type."""
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        with pytest.raises(ValueError, match="Invalid execution_type 'invalid'"):
            await agent_executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_agent",
                tool_parameters={
                    "agent_type": "component",
                    "execution_type": "invalid",
                    "query": "test query",
                },
            )

    @pytest.mark.asyncio
    async def test_employee_agent_type_placeholder(self, agent_executor, mock_producer):
        """Test employee agent type uses component fallback."""
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        mock_future = asyncio.Future()
        mock_future.set_result({"success": True, "content": "Employee agent response"})

        with patch("uuid.uuid4") as mock_uuid:
            mock_uuid.return_value.__str__ = lambda x: "test-request-id"

            with patch("asyncio.Future", return_value=mock_future):
                result = await agent_executor.execute_tool(
                    server_script_path="test/path",
                    tool_name="test_agent",
                    tool_parameters={
                        "agent_type": "employee",
                        "execution_type": "response",
                        "query": "Hello, employee agent!",
                    },
                )

                assert result == {"success": True, "content": "Employee agent response"}

                # Verify the call was made with employee agent_type
                call_args = mock_producer.send.call_args
                sent_payload = call_args[1]["value"]
                assert sent_payload["agent_type"] == "employee"

    @pytest.mark.asyncio
    async def test_interactive_session_execution(self, agent_executor, mock_producer):
        """Test interactive session execution with proper routing and session config."""
        # Mock the consumer and task
        agent_executor._consumer = Mock()
        agent_executor._consumer_task = Mock()
        agent_executor._consumer_task.done.return_value = False

        # Set correlation ID for interactive session
        agent_executor.set_correlation_id("workflow-123")

        # Mock the future for the pending request - for interactive, this would be the final session result
        mock_future = asyncio.Future()
        mock_future.set_result(
            {
                "success": True,
                "session_summary": "Interactive session completed successfully",
                "final_message": "Thank you for the conversation! I've gathered all the information needed.",
                "conversation_data": {
                    "key_points": ["User needs help with task", "Provided solution"],
                    "session_duration": 300,
                    "message_count": 8,
                },
            }
        )

        with patch("uuid.uuid4") as mock_uuid:
            mock_uuid.return_value.__str__ = lambda x: "interactive-session-id"

            with patch("asyncio.Future", return_value=mock_future):
                result = await agent_executor.execute_tool(
                    server_script_path="test/path",
                    tool_name="interactive_agent",
                    tool_parameters={
                        "agent_type": "component",
                        "execution_type": "interactive",
                        "query": "I need help with my project",
                        "session_timeout": 1200,  # 20 minutes
                    },
                )

                # For interactive sessions, we get the final session result
                assert result["success"] == True
                assert (
                    result["session_summary"]
                    == "Interactive session completed successfully"
                )
                assert "final_message" in result
                assert "conversation_data" in result

                # Verify the call was made with correct interactive session parameters
                call_args = mock_producer.send.call_args
                sent_payload = call_args[1]["value"]

                # Basic request fields
                assert sent_payload["agent_type"] == "component"
                assert sent_payload["execution_type"] == "interactive"
                assert sent_payload["query"] == "I need help with my project"

                # Interactive-specific fields
                assert sent_payload["mode"] == "interactive_session"
                assert sent_payload["response_routing"] == "direct_user"
                assert sent_payload["final_response_routing"] == "orchestration"
                assert sent_payload["workflow_correlation_id"] == "workflow-123"
                assert sent_payload["session_timeout"] == 1200

                # Session configuration
                session_config = sent_payload["session_config"]
                assert session_config["allow_user_termination"] == True
                assert "quit" in session_config["termination_keywords"]
                assert session_config["context_retention"] == True
                assert session_config["user_response_timeout"] == 300
