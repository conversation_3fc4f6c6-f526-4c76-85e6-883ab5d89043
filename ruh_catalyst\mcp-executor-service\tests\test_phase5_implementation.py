#!/usr/bin/env python3
"""
Test script for Phase 5 implementation: Advanced Error Handling & Monitoring.

Tests custom exceptions, metrics logging, and health check functionality.
"""

import asyncio
import logging
import pytest
from unittest.mock import patch, AsyncMock, MagicMock

from app.core_.exceptions import (
    MCPConfigNotFoundError, MCPConfigInvalidError, ContainerCreationError,
    ContainerExecutionError, SSHConnectionError, MCPServerUnreachableError,
    MCPAuthenticationError, MCPToolExecutionError, CredentialRetrievalError,
    get_exception_class, create_exception
)
from app.core_.metrics import (
    ExecutionType, ExecutionStatus, MetricType, ExecutionMetric,
    ConfigMetric, AuthenticationMetric, MetricsLogger, metrics_logger
)
from app.core_.health import HealthChecker, HealthStatus, get_health_status

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class TestCustomExceptions:
    """Test custom exception classes."""

    def test_mcp_config_not_found_error(self):
        """Test MCPConfigNotFoundError creation and attributes."""
        error = MCPConfigNotFoundError("test-mcp-id", {"status_code": 404})
        
        assert error.mcp_id == "test-mcp-id"
        assert error.details["status_code"] == 404
        assert "test-mcp-id" in str(error)
        assert "Details:" in str(error)

    def test_container_creation_error(self):
        """Test ContainerCreationError creation and attributes."""
        error = ContainerCreationError(
            "test-mcp-id", "test-user-id", "Container API unavailable",
            {"container_type": "stdio"}
        )
        
        assert error.mcp_id == "test-mcp-id"
        assert error.user_id == "test-user-id"
        assert error.reason == "Container API unavailable"
        assert error.details["container_type"] == "stdio"

    def test_ssh_connection_error(self):
        """Test SSHConnectionError creation and attributes."""
        error = SSHConnectionError(
            "ssh.example.com", "testuser", "Connection timeout",
            {"timeout_seconds": 30}
        )
        
        assert error.ssh_host == "ssh.example.com"
        assert error.ssh_user == "testuser"
        assert error.reason == "Connection timeout"
        assert "<EMAIL>" in str(error)

    def test_exception_mapping(self):
        """Test exception mapping functionality."""
        # Test getting exception class
        exception_class = get_exception_class("mcp_config_not_found")
        assert exception_class == MCPConfigNotFoundError
        
        # Test creating exception
        exception = create_exception("container_creation_failed", "test-mcp", "test-user", "API error")
        assert isinstance(exception, ContainerCreationError)
        assert exception.mcp_id == "test-mcp"
        assert exception.user_id == "test-user"
        assert exception.reason == "API error"

    def test_unknown_exception_type(self):
        """Test handling of unknown exception types."""
        from app.core_.exceptions import MCPExecutorError
        
        exception_class = get_exception_class("unknown_error_type")
        assert exception_class == MCPExecutorError
        
        exception = create_exception("unknown_error_type", "Test message")
        assert isinstance(exception, MCPExecutorError)


class TestMetricsLogging:
    """Test metrics logging functionality."""

    @pytest.fixture
    def metrics_logger_instance(self):
        """Create a test metrics logger instance."""
        return MetricsLogger("test_metrics")

    def test_execution_metric_creation(self):
        """Test ExecutionMetric data structure."""
        metric = ExecutionMetric(
            metric_type=MetricType.TIMER.value,
            execution_type=ExecutionType.URL.value,
            status=ExecutionStatus.SUCCESS.value,
            duration_ms=1500.0,
            mcp_id="test-mcp",
            user_id="test-user",
            tool_name="test_tool"
        )
        
        assert metric.metric_type == "timer"
        assert metric.execution_type == "url"
        assert metric.status == "success"
        assert metric.duration_ms == 1500.0
        assert metric.timestamp is not None

    def test_config_metric_creation(self):
        """Test ConfigMetric data structure."""
        metric = ConfigMetric(
            metric_type=MetricType.COUNTER.value,
            operation="fetch",
            mcp_id="test-mcp",
            duration_ms=250.0,
            cache_hit=False
        )
        
        assert metric.metric_type == "counter"
        assert metric.operation == "fetch"
        assert metric.cache_hit is False
        assert metric.timestamp is not None

    def test_authentication_metric_creation(self):
        """Test AuthenticationMetric data structure."""
        metric = AuthenticationMetric(
            metric_type=MetricType.COUNTER.value,
            operation="validate",
            status="success",
            user_id="test-user",
            mcp_id="test-mcp",
            duration_ms=100.0
        )
        
        assert metric.operation == "validate"
        assert metric.status == "success"
        assert metric.user_id == "test-user"

    def test_metrics_logger_execution_logging(self, metrics_logger_instance):
        """Test execution metrics logging."""
        with patch.object(metrics_logger_instance.logger, 'info') as mock_log:
            # Test execution start
            start_time = metrics_logger_instance.log_execution_start(
                ExecutionType.CONTAINER, "test-mcp", "test-user", "test_tool"
            )
            assert isinstance(start_time, float)
            mock_log.assert_called()
            
            # Test execution success
            metrics_logger_instance.log_execution_success(
                ExecutionType.CONTAINER, start_time, "test-mcp", "test-user", "test_tool", 1
            )
            assert mock_log.call_count == 2
            
            # Test execution failure
            metrics_logger_instance.log_execution_failure(
                ExecutionType.CONTAINER, start_time, "connection_error", 
                "test-mcp", "test-user", "test_tool", 2
            )
            assert mock_log.call_count == 3

    def test_metrics_logger_config_logging(self, metrics_logger_instance):
        """Test config metrics logging."""
        with patch.object(metrics_logger_instance.logger, 'info') as mock_log:
            # Test config fetch
            metrics_logger_instance.log_config_fetch("test-mcp", 150.0, success=True)
            mock_log.assert_called()
            
            # Test cache hit
            metrics_logger_instance.log_config_cache_hit("test-mcp")
            assert mock_log.call_count == 2
            
            # Test cache miss
            metrics_logger_instance.log_config_cache_miss("test-mcp")
            assert mock_log.call_count == 3

    def test_global_metrics_functions(self):
        """Test global metrics convenience functions."""
        from app.core_.metrics import log_execution_start, log_execution_success, log_execution_failure
        
        with patch.object(metrics_logger, 'log_execution_start') as mock_start, \
             patch.object(metrics_logger, 'log_execution_success') as mock_success, \
             patch.object(metrics_logger, 'log_execution_failure') as mock_failure:
            
            # Test global functions
            log_execution_start(ExecutionType.URL, "test-mcp", "test-user", "test_tool")
            mock_start.assert_called_once()
            
            log_execution_success(ExecutionType.URL, 123.45, "test-mcp", "test-user", "test_tool", 1)
            mock_success.assert_called_once()
            
            log_execution_failure(ExecutionType.URL, 123.45, "error", "test-mcp", "test-user", "test_tool", 2)
            mock_failure.assert_called_once()


class TestHealthChecker:
    """Test health check functionality."""

    @pytest.fixture
    def health_checker_instance(self):
        """Create a test health checker instance."""
        return HealthChecker()

    def test_health_status_enum(self):
        """Test HealthStatus enumeration."""
        assert HealthStatus.HEALTHY.value == "healthy"
        assert HealthStatus.UNHEALTHY.value == "unhealthy"
        assert HealthStatus.DEGRADED.value == "degraded"
        assert HealthStatus.UNKNOWN.value == "unknown"

    def test_service_health_check(self, health_checker_instance):
        """Test basic service health check."""
        result = health_checker_instance.check_service_health()
        
        assert result.component == "service"
        assert result.status == HealthStatus.HEALTHY.value
        assert result.duration_ms > 0
        assert result.timestamp is not None
        assert "uptime_seconds" in result.details

    @pytest.mark.asyncio
    async def test_kafka_connectivity_check_success(self, health_checker_instance):
        """Test Kafka connectivity check with mocked success."""
        with patch('kafka.KafkaProducer') as mock_producer, \
             patch('kafka.KafkaConsumer') as mock_consumer:
            
            # Mock successful connections
            mock_producer_instance = MagicMock()
            mock_producer_instance.list_topics.return_value = {"topic1": None, "topic2": None}
            mock_producer.return_value = mock_producer_instance
            
            mock_consumer_instance = MagicMock()
            mock_consumer.return_value = mock_consumer_instance
            
            result = await health_checker_instance.check_kafka_connectivity()
            
            assert result.component == "kafka"
            assert result.status == HealthStatus.HEALTHY.value
            assert "topics_available" in result.details

    @pytest.mark.asyncio
    async def test_kafka_connectivity_check_failure(self, health_checker_instance):
        """Test Kafka connectivity check with mocked failure."""
        from kafka.errors import KafkaError
        
        with patch('kafka.KafkaProducer') as mock_producer:
            mock_producer.side_effect = KafkaError("Connection failed")
            
            result = await health_checker_instance.check_kafka_connectivity()
            
            assert result.component == "kafka"
            assert result.status == HealthStatus.UNHEALTHY.value
            assert "Connection failed" in result.message

    @pytest.mark.asyncio
    async def test_container_api_connectivity_check(self, health_checker_instance):
        """Test Container API connectivity check."""
        with patch('aiohttp.ClientSession') as mock_session:
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            result = await health_checker_instance.check_container_api_connectivity()
            
            assert result.component == "container_api"
            # Status could be healthy or degraded depending on actual response
            assert result.status in [HealthStatus.HEALTHY.value, HealthStatus.DEGRADED.value]

    @pytest.mark.asyncio
    async def test_system_health_integration(self, health_checker_instance):
        """Test overall system health check."""
        with patch.object(health_checker_instance, 'check_kafka_connectivity') as mock_kafka, \
             patch.object(health_checker_instance, 'check_container_api_connectivity') as mock_container, \
             patch.object(health_checker_instance, 'check_mcp_config_service_connectivity') as mock_config:
            
            # Mock all checks as healthy
            from app.core_.health import HealthCheckResult
            mock_kafka.return_value = HealthCheckResult("kafka", "healthy", "OK", 100.0, "")
            mock_container.return_value = HealthCheckResult("container_api", "healthy", "OK", 150.0, "")
            mock_config.return_value = HealthCheckResult("mcp_config_service", "healthy", "OK", 120.0, "")
            
            system_health = await health_checker_instance.get_system_health()
            
            assert system_health.status == HealthStatus.HEALTHY.value
            assert len(system_health.checks) == 4  # service + 3 mocked checks
            assert system_health.summary["healthy"] == 4
            assert system_health.summary["total"] == 4
            assert system_health.uptime_seconds > 0

    @pytest.mark.asyncio
    async def test_get_health_status_function(self):
        """Test global get_health_status function."""
        health_status = await get_health_status()
        
        assert isinstance(health_status, dict)
        assert "status" in health_status
        assert "timestamp" in health_status
        assert "uptime_seconds" in health_status
        assert "checks" in health_status
        assert "summary" in health_status


if __name__ == "__main__":
    # Run a simple validation test
    async def run_validation():
        logger.info("🧪 Running Phase 5 implementation validation...")
        
        # Test custom exceptions
        logger.info("Testing custom exceptions...")
        error = MCPConfigNotFoundError("test-mcp", {"test": "data"})
        logger.info(f"✅ Custom exception created: {error}")
        
        # Test metrics logging
        logger.info("Testing metrics logging...")
        start_time = metrics_logger.log_execution_start(
            ExecutionType.URL, "test-mcp", "test-user", "test_tool"
        )
        metrics_logger.log_execution_success(
            ExecutionType.URL, start_time, "test-mcp", "test-user", "test_tool", 1
        )
        logger.info("✅ Metrics logging working")
        
        # Test health checking
        logger.info("Testing health checking...")
        health_status = await get_health_status()
        logger.info(f"✅ Health status: {health_status['status']}")
        
        logger.info("🎉 Phase 5 implementation validation completed successfully!")
    
    asyncio.run(run_validation())
