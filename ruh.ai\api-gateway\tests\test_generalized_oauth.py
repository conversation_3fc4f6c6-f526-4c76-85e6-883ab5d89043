"""
Test script for the Generalized OAuth System

This script demonstrates how to use the new OAuth system with multiple providers.
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1/v2/oauth"
TEST_USER_TOKEN = "your-test-user-jwt-token"  # Replace with actual token
TEST_MCP_ID = "test_mcp_123"
TEST_TOOL_NAME = "google_calendar"


class OAuthTestClient:
    """Test client for the generalized OAuth system."""
    
    def __init__(self, base_url: str, user_token: str = None):
        self.base_url = base_url
        self.headers = {}
        if user_token:
            self.headers["Authorization"] = f"Bearer {user_token}"
    
    async def list_providers(self) -> Dict[str, Any]:
        """List all available OAuth providers."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/providers")
            return response.json()
    
    async def get_tool_scopes(self, tool_name: str) -> Dict[str, Any]:
        """Get scope requirements for a specific tool."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/tools/{tool_name}/scopes")
            return response.json()
    
    async def initiate_oauth_flow(
        self, 
        provider: str, 
        mcp_id: str, 
        tool_name: str, 
        custom_scopes: str = None
    ) -> str:
        """Initiate OAuth flow and return authorization URL."""
        params = {
            "provider": provider,
            "mcp_id": mcp_id,
            "tool_name": tool_name,
        }
        if custom_scopes:
            params["scopes"] = custom_scopes
        
        async with httpx.AsyncClient(follow_redirects=False) as client:
            response = await client.post(
                f"{self.base_url}/authorize",
                params=params,
                headers=self.headers
            )
            
            if response.status_code == 302:
                return response.headers.get("location", "")
            else:
                return f"Error: {response.status_code} - {response.text}"
    
    async def get_credentials(
        self, 
        mcp_id: str, 
        tool_name: str, 
        provider: str = None
    ) -> Dict[str, Any]:
        """Get stored OAuth credentials."""
        params = {
            "mcp_id": mcp_id,
            "tool_name": tool_name,
        }
        if provider:
            params["provider"] = provider
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/credentials",
                params=params,
                headers=self.headers
            )
            return response.json()


async def test_oauth_system():
    """Test the generalized OAuth system."""
    
    print("🚀 Testing Generalized OAuth System")
    print("=" * 50)
    
    # Initialize test client
    client = OAuthTestClient(API_BASE_URL, TEST_USER_TOKEN)
    
    try:
        # Test 1: List available providers
        print("\n📋 Test 1: Listing available OAuth providers")
        providers_response = await client.list_providers()
        
        if providers_response.get("success"):
            providers = providers_response.get("providers", [])
            print(f"✅ Found {len(providers)} configured providers:")
            for provider in providers:
                print(f"   - {provider['name']} ({provider['provider']})")
                print(f"     Auth URL: {provider['auth_url']}")
                print(f"     Supported tools: {', '.join(provider['supported_tools'])}")
        else:
            print(f"❌ Failed to list providers: {providers_response}")
            return
        
        # Test 2: Get tool scope requirements
        print(f"\n🔍 Test 2: Getting scope requirements for '{TEST_TOOL_NAME}'")
        scopes_response = await client.get_tool_scopes(TEST_TOOL_NAME)
        
        if scopes_response.get("success"):
            provider_scopes = scopes_response.get("provider_scopes", {})
            print(f"✅ Scope requirements for {TEST_TOOL_NAME}:")
            for provider, scopes in provider_scopes.items():
                print(f"   - {provider}: {', '.join(scopes)}")
        else:
            print(f"❌ Failed to get tool scopes: {scopes_response}")
        
        # Test 3: Initiate OAuth flow (if providers are available)
        if providers:
            test_provider = providers[0]["provider"]
            print(f"\n🔗 Test 3: Initiating OAuth flow with {test_provider}")
            
            # Note: This will fail without proper authentication
            auth_url = await client.initiate_oauth_flow(
                provider=test_provider,
                mcp_id=TEST_MCP_ID,
                tool_name=TEST_TOOL_NAME
            )
            
            if auth_url.startswith("http"):
                print(f"✅ OAuth authorization URL generated:")
                print(f"   {auth_url}")
                print("   (User would be redirected to this URL)")
            else:
                print(f"❌ Failed to generate auth URL: {auth_url}")
        
        # Test 4: Try to get credentials (will likely fail without stored credentials)
        print(f"\n🔑 Test 4: Attempting to retrieve stored credentials")
        creds_response = await client.get_credentials(
            mcp_id=TEST_MCP_ID,
            tool_name=TEST_TOOL_NAME
        )
        
        if creds_response.get("success"):
            print("✅ Credentials retrieved successfully")
            print(f"   Token type: {creds_response.get('token_type')}")
            print(f"   Expires in: {creds_response.get('expires_in')} seconds")
            print(f"   Scope: {creds_response.get('scope')}")
        else:
            print(f"❌ No credentials found (expected): {creds_response.get('message')}")
        
        print("\n🎉 OAuth system tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")


async def test_provider_configurations():
    """Test provider configuration loading."""
    
    print("\n🔧 Testing Provider Configurations")
    print("=" * 40)
    
    try:
        from app.core.oauth_providers import oauth_provider_manager, OAuthProvider
        
        # Test provider loading
        supported_providers = oauth_provider_manager.get_supported_providers()
        print(f"✅ Loaded {len(supported_providers)} providers:")
        
        for provider in supported_providers:
            config = oauth_provider_manager.get_provider_config(provider)
            print(f"   - {provider.value}:")
            print(f"     Auth URL: {config.auth_url}")
            print(f"     Token URL: {config.token_url}")
            print(f"     Configured: {'Yes' if config.client_id else 'No'}")
        
        # Test tool scope mappings
        supported_tools = oauth_provider_manager.get_supported_tools()
        print(f"\n✅ Loaded {len(supported_tools)} tool mappings:")
        
        for tool in supported_tools[:3]:  # Show first 3 tools
            print(f"   - {tool}:")
            for provider in [OAuthProvider.GOOGLE, OAuthProvider.MICROSOFT]:
                scopes = oauth_provider_manager.get_tool_scopes(tool, provider)
                if scopes:
                    print(f"     {provider.value}: {', '.join(scopes[:2])}...")
        
        print("\n✅ Provider configuration tests passed!")
        
    except Exception as e:
        print(f"❌ Provider configuration test failed: {str(e)}")


def print_usage_examples():
    """Print usage examples for the OAuth system."""
    
    print("\n📖 Usage Examples")
    print("=" * 30)
    
    print("""
1. Frontend Integration:
   ```javascript
   // Get available providers
   const providers = await fetch('/api/v1/v2/oauth/providers').then(r => r.json());
   
   // Initiate OAuth flow
   window.location.href = '/api/v1/v2/oauth/authorize?provider=google&mcp_id=123&tool_name=google_calendar';
   
   // Get credentials after OAuth completion
   const creds = await fetch('/api/v1/v2/oauth/credentials?mcp_id=123&tool_name=google_calendar&provider=google')
       .then(r => r.json());
   ```

2. Backend Integration:
   ```python
   from app.services.generalized_oauth_service import GeneralizedOAuthService
   from app.core.oauth_providers import OAuthProvider
   
   oauth_service = GeneralizedOAuthService()
   
   # Get credentials for a tool
   result = oauth_service.get_oauth_credentials(
       db=db,
       user_id="user123",
       mcp_id="mcp456", 
       tool_name="google_calendar",
       provider=OAuthProvider.GOOGLE
   )
   ```

3. Adding Custom Providers:
   ```bash
   # Environment variable
   CUSTOM_OAUTH_PROVIDERS='[{
       "provider": "custom",
       "client_id": "your-client-id",
       "client_secret": "your-client-secret",
       "auth_url": "https://provider.com/oauth/authorize",
       "token_url": "https://provider.com/oauth/token"
   }]'
   ```
    """)


if __name__ == "__main__":
    print("🧪 Generalized OAuth System Test Suite")
    print("=" * 60)
    
    # Run configuration tests (these don't require a running server)
    asyncio.run(test_provider_configurations())
    
    # Print usage examples
    print_usage_examples()
    
    # Note about API tests
    print("\n⚠️  Note: API tests require a running server and valid authentication.")
    print("   Update TEST_USER_TOKEN and run the server to test API endpoints.")
    
    # Uncomment to run API tests (requires running server)
    # asyncio.run(test_oauth_system())
