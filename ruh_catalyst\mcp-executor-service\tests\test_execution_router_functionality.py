#!/usr/bin/env python3
"""
Test Execution Router Functionality

Tests the execution router's ability to:
1. Fetch MCP configuration
2. Determine execution method (SSE, STDIO, HTTP)
3. Route to appropriate MCP client type
4. Handle different server types correctly

Usage:
    poetry run python tests/test_execution_router_functionality.py
"""

import asyncio
import json
import logging
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.execution_router import ExecutionRouter
from app.services.mcp_config_client import MCPConfigClient
from app.core_.mcp_executor import MCP<PERSON>xecutor
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ExecutionRouterTester:
    """Test class for execution router functionality."""

    def __init__(self):
        self.execution_router = ExecutionRouter()
        self.mcp_config_client = MCPConfigClient()
        self.test_results = []

    async def test_mcp_config_fetch(self, mcp_id: str) -> Dict[str, Any]:
        """Test MCP configuration fetching."""
        logger.info(f"🧪 Testing MCP config fetch for ID: {mcp_id}")

        try:
            start_time = time.time()
            config = await self.mcp_config_client.get_mcp_config(mcp_id)
            end_time = time.time()

            result = {
                "test": "mcp_config_fetch",
                "mcp_id": mcp_id,
                "success": True,
                "response_time": end_time - start_time,
                "config": config,
                "urls_count": len(config.get("urls", [])),
                "error": None,
            }

            logger.info(
                f"✅ MCP config fetched successfully in {result['response_time']:.2f}s"
            )
            logger.info(f"   Found {result['urls_count']} URL configurations")

            return result

        except Exception as e:
            result = {
                "test": "mcp_config_fetch",
                "mcp_id": mcp_id,
                "success": False,
                "response_time": time.time() - start_time,
                "config": None,
                "urls_count": 0,
                "error": str(e),
            }

            logger.error(f"❌ MCP config fetch failed: {e}")
            return result

    async def test_execution_method_determination(self, mcp_id: str) -> Dict[str, Any]:
        """Test execution method determination."""
        logger.info(f"🧪 Testing execution method determination for ID: {mcp_id}")

        try:
            start_time = time.time()
            strategy = await self.execution_router.determine_execution_method(mcp_id)
            end_time = time.time()

            execution_params = self.execution_router.extract_execution_parameters(
                strategy
            )

            result = {
                "test": "execution_method_determination",
                "mcp_id": mcp_id,
                "success": True,
                "response_time": end_time - start_time,
                "execution_method": strategy.method,
                "config": strategy.config,
                "fallback_available": strategy.fallback_available,
                "execution_params": execution_params,
                "error": None,
            }

            logger.info(f"✅ Execution method determined: {strategy.method}")
            logger.info(f"   Fallback available: {strategy.fallback_available}")
            logger.info(f"   Config: {strategy.config}")

            return result

        except Exception as e:
            result = {
                "test": "execution_method_determination",
                "mcp_id": mcp_id,
                "success": False,
                "response_time": time.time() - start_time,
                "execution_method": None,
                "config": None,
                "fallback_available": False,
                "execution_params": None,
                "error": str(e),
            }

            logger.error(f"❌ Execution method determination failed: {e}")
            return result

    async def test_url_parsing_and_prioritization(self, mcp_id: str) -> Dict[str, Any]:
        """Test URL parsing and prioritization logic."""
        logger.info(f"🧪 Testing URL parsing and prioritization for ID: {mcp_id}")

        try:
            # First get the config
            config = await self.mcp_config_client.get_mcp_config(mcp_id)
            urls_array = config.get("urls", [])

            if not urls_array:
                return {
                    "test": "url_parsing_prioritization",
                    "mcp_id": mcp_id,
                    "success": False,
                    "error": "No URLs found in configuration",
                }

            # Test the parsing logic
            start_time = time.time()
            result_config = self.mcp_config_client.parse_urls(urls_array)
            end_time = time.time()

            # Analyze the URLs to understand prioritization
            url_analysis = []
            for i, url_entry in enumerate(urls_array):
                analysis = {
                    "index": i,
                    "entry": url_entry,
                    "has_image_name": "image_name" in url_entry,
                    "has_url": "url" in url_entry,
                    "type": url_entry.get("type", "unknown"),
                    "priority_score": self._calculate_priority_score(url_entry),
                }
                url_analysis.append(analysis)

            result = {
                "test": "url_parsing_prioritization",
                "mcp_id": mcp_id,
                "success": True,
                "response_time": end_time - start_time,
                "selected_method": result_config.execution_method,
                "selected_config": result_config.config,
                "total_urls": len(urls_array),
                "url_analysis": url_analysis,
                "error": None,
            }

            logger.info(
                f"✅ URL parsing completed: selected {result_config.execution_method}"
            )
            logger.info(f"   Selected config: {result_config.config}")

            return result

        except Exception as e:
            result = {
                "test": "url_parsing_prioritization",
                "mcp_id": mcp_id,
                "success": False,
                "response_time": 0,
                "selected_method": None,
                "selected_config": None,
                "total_urls": 0,
                "url_analysis": [],
                "error": str(e),
            }

            logger.error(f"❌ URL parsing failed: {e}")
            return result

    def _calculate_priority_score(self, url_entry: Dict[str, Any]) -> int:
        """Calculate priority score for URL entry based on routing logic."""
        score = 0

        if "image_name" in url_entry:
            score += 100  # Container-based gets higher priority
            if url_entry.get("type", "").lower() == "stdio":
                score += 50  # STDIO gets highest priority
        elif "url" in url_entry:
            score += 50  # URL-based gets lower priority
            if url_entry.get("type", "").lower() == "sse":
                score += 25  # SSE gets preference over other URL types

        return score

    async def test_mcp_client_execution(
        self, mcp_id: str, user_id: str
    ) -> Dict[str, Any]:
        """Test MCP client execution with determined method."""
        logger.info(f"🧪 Testing MCP client execution for ID: {mcp_id}")

        try:
            # Create a mock producer for the executor
            producer = MockKafkaProducer()
            executor = MCPExecutor(producer)

            start_time = time.time()

            # Execute the tool using the smart routing
            result = await executor.execute_tool(
                tool_name="git_directory_structure",
                tool_parameters={
                    "repo_url": "https://github.com/modelcontextprotocol/python-sdk.git"
                },
                user_id=user_id,
                mcp_id=mcp_id,
                retries=1,  # Reduce retries for testing
            )

            end_time = time.time()

            test_result = {
                "test": "mcp_client_execution",
                "mcp_id": mcp_id,
                "user_id": user_id,
                "success": True,
                "response_time": end_time - start_time,
                "result": result,
                "result_type": type(result).__name__,
                "result_length": len(result) if isinstance(result, (list, dict)) else 1,
                "error": None,
            }

            logger.info(
                f"✅ MCP client execution successful in {test_result['response_time']:.2f}s"
            )
            logger.info(f"   Result type: {test_result['result_type']}")
            logger.info(f"   Result length: {test_result['result_length']}")

            return test_result

        except Exception as e:
            test_result = {
                "test": "mcp_client_execution",
                "mcp_id": mcp_id,
                "user_id": user_id,
                "success": False,
                "response_time": time.time() - start_time,
                "result": None,
                "result_type": None,
                "result_length": 0,
                "error": str(e),
            }

            logger.error(f"❌ MCP client execution failed: {e}")
            return test_result

    async def test_fallback_mechanism(self, mcp_id: str) -> Dict[str, Any]:
        """Test fallback mechanism functionality."""
        logger.info(f"🧪 Testing fallback mechanism for ID: {mcp_id}")

        try:
            # Get the primary strategy
            strategy = await self.execution_router.determine_execution_method(mcp_id)

            # Test fallback strategy creation
            fallback_strategy = await self.execution_router.get_fallback_strategy(
                strategy
            )

            result = {
                "test": "fallback_mechanism",
                "mcp_id": mcp_id,
                "success": True,
                "primary_method": strategy.method,
                "primary_config": strategy.config,
                "fallback_available": strategy.fallback_available,
                "fallback_method": (
                    fallback_strategy.method if fallback_strategy else None
                ),
                "fallback_config": (
                    fallback_strategy.config if fallback_strategy else None
                ),
                "error": None,
            }

            if fallback_strategy:
                logger.info(
                    f"✅ Fallback available: {strategy.method} → {fallback_strategy.method}"
                )
            else:
                logger.info(f"ℹ️ No fallback available for {strategy.method}")

            return result

        except Exception as e:
            result = {
                "test": "fallback_mechanism",
                "mcp_id": mcp_id,
                "success": False,
                "primary_method": None,
                "primary_config": None,
                "fallback_available": False,
                "fallback_method": None,
                "fallback_config": None,
                "error": str(e),
            }

            logger.error(f"❌ Fallback mechanism test failed: {e}")
            return result

    async def run_comprehensive_tests(
        self, mcp_id: str, user_id: str
    ) -> Dict[str, Any]:
        """Run all execution router tests."""
        logger.info("🚀 Starting comprehensive execution router tests")
        logger.info("=" * 60)

        all_results = {
            "mcp_id": mcp_id,
            "user_id": user_id,
            "timestamp": time.time(),
            "tests": {},
        }

        # Test 1: MCP Config Fetch
        logger.info("\n📋 Test 1: MCP Configuration Fetch")
        all_results["tests"]["config_fetch"] = await self.test_mcp_config_fetch(mcp_id)

        # Test 2: Execution Method Determination
        logger.info("\n🎯 Test 2: Execution Method Determination")
        all_results["tests"]["method_determination"] = (
            await self.test_execution_method_determination(mcp_id)
        )

        # Test 3: URL Parsing and Prioritization
        logger.info("\n🔄 Test 3: URL Parsing and Prioritization")
        all_results["tests"]["url_parsing"] = (
            await self.test_url_parsing_and_prioritization(mcp_id)
        )

        # Test 4: Fallback Mechanism
        logger.info("\n🔄 Test 4: Fallback Mechanism")
        all_results["tests"]["fallback"] = await self.test_fallback_mechanism(mcp_id)

        # Test 5: MCP Client Execution (if previous tests passed)
        if all_results["tests"]["method_determination"]["success"]:
            logger.info("\n⚡ Test 5: MCP Client Execution")
            all_results["tests"]["client_execution"] = (
                await self.test_mcp_client_execution(mcp_id, user_id)
            )
        else:
            logger.warning(
                "⚠️ Skipping MCP client execution due to method determination failure"
            )
            all_results["tests"]["client_execution"] = {
                "test": "mcp_client_execution",
                "success": False,
                "error": "Skipped due to method determination failure",
            }

        # Calculate overall success
        successful_tests = sum(
            1 for test in all_results["tests"].values() if test.get("success", False)
        )
        total_tests = len(all_results["tests"])
        all_results["overall_success"] = successful_tests == total_tests
        all_results["success_rate"] = successful_tests / total_tests

        return all_results

    def print_test_summary(self, results: Dict[str, Any]):
        """Print comprehensive test summary."""
        print("\n" + "=" * 80)
        print("📊 EXECUTION ROUTER TEST SUMMARY")
        print("=" * 80)

        print(f"MCP ID: {results['mcp_id']}")
        print(f"User ID: {results['user_id']}")
        print(f"Overall Success: {'✅' if results['overall_success'] else '❌'}")
        print(f"Success Rate: {results['success_rate']:.1%}")

        print(f"\n📋 Individual Test Results:")
        for test_name, test_result in results["tests"].items():
            status = "✅" if test_result.get("success", False) else "❌"
            print(f"   {status} {test_name}: {test_result.get('error', 'Success')}")

            # Print specific details for key tests
            if test_name == "method_determination" and test_result.get("success"):
                print(
                    f"      → Execution Method: {test_result.get('execution_method')}"
                )
                print(
                    f"      → Fallback Available: {test_result.get('fallback_available')}"
                )

            if test_name == "url_parsing" and test_result.get("success"):
                print(f"      → Selected Method: {test_result.get('selected_method')}")
                print(f"      → Total URLs: {test_result.get('total_urls')}")

        print("=" * 80)


class MockKafkaProducer:
    """Mock Kafka producer for testing."""

    async def send(self, topic: str, value: Any, headers: Optional[list] = None):
        """Mock send method."""
        logger.debug(f"Mock producer: sending to {topic}: {value}")
        return None


async def main():
    """Main test execution."""
    # Test parameters
    mcp_id = "********-f358-4595-9993-33abd9afee06"
    user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"

    print("🧪 Execution Router Functionality Tests")
    print("   Testing smart routing, MCP config fetch, and client execution")
    print("")

    tester = ExecutionRouterTester()

    try:
        results = await tester.run_comprehensive_tests(mcp_id, user_id)
        tester.print_test_summary(results)

        # Save results to file
        results_file = Path(__file__).parent / "execution_router_test_results.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Test results saved to: {results_file}")

        return 0 if results["overall_success"] else 1

    except KeyboardInterrupt:
        logger.info("⏹️ Tests cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Tests cancelled")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
