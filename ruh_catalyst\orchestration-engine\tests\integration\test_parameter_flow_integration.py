import pytest
from unittest.mock import Mock, AsyncMock, patch
from app.core_.workflow_utils import WorkflowUtils
from app.core_.state_manager import WorkflowStateManager


class TestParameterFlowIntegration:
    """
    Integration tests to verify null value filtering works correctly
    across the complete parameter processing flow.
    """

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        with patch("app.core_.workflow_utils.load_schema") as mock_load_schema:
            mock_load_schema.return_value = {
                "type": "object",
                "properties": {
                    "nodes": {"type": "array"},
                    "transitions": {"type": "array"},
                },
                "required": ["nodes", "transitions"],
            }
            
            state_manager = Mock(spec=WorkflowStateManager)
            state_manager.get_transition_result = Mock(return_value={})
            
            utils = WorkflowUtils(workflow_id="test-workflow")
            utils.state_manager = state_manager
            return utils

    @pytest.mark.asyncio
    async def test_complete_parameter_flow_with_null_filtering(self, workflow_utils):
        """
        Test the complete parameter processing flow from input to final output
        with null value filtering at each stage.
        """
        # Arrange - Mock previous transition results with null values
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={
                "result": {
                    "valid_handle": "mapped_value",
                    "null_handle": None,
                    "empty_handle": "",
                }
            }
        )

        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {"field_name": "required_field", "required": True, "data_type": {"type": "string"}},
                    {"field_name": "optional_field", "required": False, "data_type": {"type": "string"}},
                    {"field_name": "mapped_field", "required": False, "data_type": {"type": "string"}},
                ]
            },
            "output_schema": {"type": "object"}
        }

        input_data_configs = [
            {
                "from_transition_id": "prev_transition",
                "handle_mappings": [
                    {"source_handle_id": "valid_handle", "target_handle_id": "mapped_field", "edge_id": "edge1"},
                    {"source_handle_id": "null_handle", "target_handle_id": "null_mapped", "edge_id": "edge2"},
                    {"source_handle_id": "empty_handle", "target_handle_id": "empty_mapped", "edge_id": "edge3"},
                ]
            }
        ]

        current_tool_params = {
            "required_field": "required_value",
            "optional_field": None,  # Should be filtered out
            "empty_field": "",       # Should be filtered out
            "null_string": "null",   # Should be filtered out
            "valid_number": 42,      # Should be kept
            "empty_dict": {},        # Should be filtered out
        }

        # Mock the _extract_data_by_handle method
        def mock_extract_data(source_results, handle_id, source_transition_id):
            if isinstance(source_results, dict) and "result" in source_results:
                result_data = source_results["result"]
                if isinstance(result_data, dict) and handle_id in result_data:
                    return result_data[handle_id]
            return None

        workflow_utils._extract_data_by_handle = mock_extract_data

        # Act
        result = await workflow_utils._format_tool_parameters(
            node_tool_info,
            input_data_configs,
            "test_transition",
            current_tool_params
        )

        # Assert
        expected_result = {
            "required_field": "required_value",
            "valid_number": 42,
            "mapped_field": "mapped_value",
            # All null/empty values should be filtered out:
            # - optional_field (None)
            # - empty_field ("")
            # - null_string ("null")
            # - empty_dict ({})
            # - null_mapped (None from handle mapping)
            # - empty_mapped ("" from handle mapping)
        }

        assert result == expected_result

    def test_convert_params_integration_with_filtering(self, workflow_utils):
        """
        Test parameter conversion integration with null filtering.
        """
        # Test list format conversion with null filtering
        list_params = [
            {"field_name": "valid_field", "field_value": "valid_value"},
            {"field_name": "null_field", "field_value": None},
            {"field_name": "empty_field", "field_value": ""},
            {"field_name": "number_field", "field_value": 42},
            {"field_name": "boolean_field", "field_value": False},  # Should be kept
        ]

        result = workflow_utils._convert_params_to_dict(list_params)
        
        expected = {
            "valid_field": "valid_value",
            "number_field": 42,
            "boolean_field": False,
        }
        
        assert result == expected

    def test_schema_formatting_integration_with_filtering(self, workflow_utils):
        """
        Test schema formatting integration with null filtering.
        """
        node_tool_info = {
            "input_schema": {
                "predefined_fields": [
                    {"field_name": "required_field", "required": True, "data_type": {"type": "string"}},
                    {"field_name": "optional_field", "required": False, "data_type": {"type": "string"}},
                    {"field_name": "nested_field", "required": False, "data_type": {"type": "object"}},
                ]
            }
        }

        params = {
            "required_field": "required_value",
            "optional_field": None,  # Should be filtered out
            "nested_field": {
                "valid_nested": "nested_value",
                "null_nested": None,
                "empty_nested": "",
            },
            "extra_field": "extra_value",  # Not in schema, should be ignored
        }

        result = workflow_utils._format_params_according_to_schema(node_tool_info, params)
        
        expected = {
            "required_field": "required_value",
            "nested_field": {
                "valid_nested": "nested_value",
            }
        }
        
        assert result == expected

    def test_placeholder_processing_integration_with_filtering(self, workflow_utils):
        """
        Test placeholder processing integration with null filtering.
        """
        params = {
            "valid_field": "${valid_placeholder}",
            "null_field": None,
            "empty_field": "",
            "static_field": "static_value",
            "unresolved_field": "${unknown_placeholder}",
        }

        flattened_results = {
            "valid_placeholder": "resolved_value",
        }

        result, has_unresolved = workflow_utils._process_params_for_placeholders(
            params, flattened_results
        )

        expected = {
            "valid_field": "resolved_value",
            "static_field": "static_value",
            "unresolved_field": "${unknown_placeholder}",
            # null_field and empty_field should be filtered out
        }

        assert result == expected
        assert has_unresolved is True

    def test_handle_data_resolution_integration_with_filtering(self, workflow_utils):
        """
        Test handle data resolution integration with null filtering.
        """
        current_tool_params = {
            "static_param": "static_value",
            "null_static": None,
            "empty_static": "",
            "valid_number": 42,
        }

        all_previous_results = {
            "transition-1": {
                "result": {
                    "valid_handle": "mapped_value",
                    "null_handle": None,
                    "empty_handle": "",
                    "number_handle": 123,
                }
            }
        }

        handle_mappings = {
            "transition-1": [
                {"source_handle_id": "valid_handle", "target_handle_id": "mapped_param", "edge_id": "edge-1"},
                {"source_handle_id": "null_handle", "target_handle_id": "null_mapped", "edge_id": "edge-2"},
                {"source_handle_id": "empty_handle", "target_handle_id": "empty_mapped", "edge_id": "edge-3"},
                {"source_handle_id": "number_handle", "target_handle_id": "number_mapped", "edge_id": "edge-4"},
            ]
        }

        # Mock the _extract_data_by_handle method
        def mock_extract_data(source_results, handle_id, source_transition_id):
            if isinstance(source_results, dict) and "result" in source_results:
                result_data = source_results["result"]
                if isinstance(result_data, dict) and handle_id in result_data:
                    return result_data[handle_id]
            return None

        workflow_utils._extract_data_by_handle = mock_extract_data

        result = workflow_utils._resolve_handle_data(
            current_tool_params, all_previous_results, handle_mappings
        )

        expected = {
            "static_param": "static_value",
            "valid_number": 42,
            "mapped_param": "mapped_value",
            "number_mapped": 123,
            # null_static, empty_static, null_mapped, empty_mapped should all be filtered out
        }

        assert result == expected

    def test_edge_case_integration_with_filtering(self, workflow_utils):
        """
        Test edge cases in parameter processing with null filtering.
        """
        # Test with completely empty parameters
        result = workflow_utils._convert_params_to_dict({})
        assert result == {}

        # Test with all null parameters
        all_null_params = {
            "null_field": None,
            "empty_field": "",
            "null_string": "null",
            "empty_dict": {},
            "empty_list": [],
        }
        result = workflow_utils._convert_params_to_dict(all_null_params)
        assert result == {}

        # Test with nested empty structures
        nested_empty = {
            "level1": {
                "level2": {
                    "all_null": None,
                    "all_empty": "",
                }
            }
        }
        result = workflow_utils._filter_null_empty_values(nested_empty)
        assert result == {}  # Should be completely filtered out
