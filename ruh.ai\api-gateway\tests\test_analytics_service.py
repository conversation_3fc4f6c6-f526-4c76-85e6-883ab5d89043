import pytest
from unittest.mock import Mock, patch
import grpc
from app.services.analytics_service import AnalyticsServiceClient
from app.grpc_ import analytics_pb2


@pytest.mark.asyncio
class TestAnalyticsService:
    
    @pytest.fixture
    def mock_analytics_stub(self):
        with patch('app.services.analytics_service.analytics_pb2_grpc.AnalyticsServiceStub') as mock:
            yield mock

    @pytest.fixture
    def grpc_error(self):
        return grpc.RpcError("Test gRPC error")

    async def test_track_event_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Event tracked successfully"
        mock_response.event_id = "event_123"
        mock_analytics_stub.return_value.TrackEvent.return_value = mock_response

        # Act
        result = await client.track_event(
            event_type="usage",
            service_type="agent",
            entity_id="agent_123",
            user_id="user_123",
            metadata='{"test": "data"}'
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Event tracked successfully"
        assert result["event_id"] == "event_123"

    async def test_track_event_error(self, mock_analytics_stub, grpc_error):
        # Arrange
        client = AnalyticsServiceClient()
        mock_analytics_stub.return_value.TrackEvent.side_effect = grpc_error

        # Act
        result = await client.track_event(
            event_type="usage",
            service_type="agent",
            entity_id="agent_123",
            user_id="user_123"
        )

        # Assert
        assert result["success"] is False
        assert "gRPC error" in result["message"]
        assert result["event_id"] is None

    async def test_get_service_metrics_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Metrics retrieved successfully"
        
        # Mock metrics data
        mock_metrics = Mock()
        mock_metrics.entity_id = "agent_123"
        mock_metrics.service_type = analytics_pb2.ServiceType.SERVICE_TYPE_AGENT
        mock_metrics.usage_count = 10
        mock_metrics.average_rating = 4.5
        mock_metrics.rating_count = 5
        mock_metrics.usage_time_series = []
        
        mock_response.metrics = mock_metrics
        mock_analytics_stub.return_value.GetServiceMetrics.return_value = mock_response

        # Act
        result = await client.get_service_metrics(
            service_type="agent",
            entity_id="agent_123",
            time_period_days=7
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Metrics retrieved successfully"
        assert result["metrics"]["entity_id"] == "agent_123"
        assert result["metrics"]["usage_count"] == 10
        assert result["metrics"]["average_rating"] == 4.5

    async def test_get_user_activity_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Activity retrieved successfully"
        
        # Mock activity data
        mock_activity = Mock()
        mock_activity.user_id = "user_123"
        mock_activity.mcp_usage_count = 5
        mock_activity.workflow_usage_count = 3
        mock_activity.agent_usage_count = 8
        mock_activity.mcp_creation_count = 2
        mock_activity.workflow_creation_count = 1
        mock_activity.agent_creation_count = 3
        mock_activity.last_activity_date = "2024-01-01T00:00:00Z"
        
        mock_response.activity = mock_activity
        mock_analytics_stub.return_value.GetUserActivity.return_value = mock_response

        # Act
        result = await client.get_user_activity(
            user_id="user_123",
            time_period_days=30
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Activity retrieved successfully"
        assert result["activity"]["user_id"] == "user_123"
        assert result["activity"]["mcp_usage_count"] == 5
        assert result["activity"]["workflow_usage_count"] == 3
        assert result["activity"]["agent_usage_count"] == 8

    async def test_track_activation_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Activation tracked successfully"
        mock_response.activation_id = "activation_123"
        mock_analytics_stub.return_value.TrackActivation.return_value = mock_response

        # Act
        result = await client.track_activation(
            user_id="user_123",
            event_type="user_signup",
            entity_id="signup_123",
            metadata='{"source": "web"}'
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Activation tracked successfully"
        assert result["activation_id"] == "activation_123"

    async def test_create_webhook_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Webhook created successfully"
        
        # Mock webhook data
        mock_webhook = Mock()
        mock_webhook.id = "webhook_123"
        mock_webhook.user_id = "user_123"
        mock_webhook.application_id = "app_123"
        mock_webhook.url = "https://example.com/webhook"
        mock_webhook.event_types = [analytics_pb2.WebhookEventType.WEBHOOK_EVENT_TYPE_AGENT_EXECUTION]
        mock_webhook.description = "Test webhook"
        mock_webhook.is_active = True
        mock_webhook.status = analytics_pb2.WebhookStatus.WEBHOOK_STATUS_ACTIVE
        mock_webhook.created_at = "2024-01-01T00:00:00Z"
        mock_webhook.updated_at = "2024-01-01T00:00:00Z"
        
        mock_response.webhook = mock_webhook
        mock_analytics_stub.return_value.CreateWebhook.return_value = mock_response

        # Act
        result = await client.create_webhook(
            user_id="user_123",
            url="https://example.com/webhook",
            event_types=["agent_execution"],
            application_id="app_123",
            description="Test webhook"
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Webhook created successfully"
        assert result["webhook"]["id"] == "webhook_123"
        assert result["webhook"]["url"] == "https://example.com/webhook"
        assert result["webhook"]["is_active"] is True

    async def test_get_dashboard_overview_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Dashboard overview retrieved successfully"
        
        # Mock overview data
        mock_overview = Mock()
        mock_overview.user_id = "user_123"
        mock_overview.active_agents = 5
        mock_overview.credit_usage = 100.5
        mock_overview.agent_requests = 25
        mock_overview.workflow_requests = 15
        mock_overview.custom_mcps = 3
        mock_overview.credit_breakdown = {"agent": 60.0, "workflow": 40.5}
        mock_overview.app_credit_usage = []
        mock_overview.agent_performance = []
        mock_overview.recent_events = []
        
        mock_response.overview = mock_overview
        mock_analytics_stub.return_value.GetDashboardOverview.return_value = mock_response

        # Act
        result = await client.get_dashboard_overview(
            user_id="user_123",
            time_period_days=7
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Dashboard overview retrieved successfully"
        assert result["overview"]["user_id"] == "user_123"
        assert result["overview"]["active_agents"] == 5
        assert result["overview"]["credit_usage"] == 100.5
        assert result["overview"]["agent_requests"] == 25

    async def test_record_api_request_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "API request recorded successfully"
        mock_response.event_id = "event_456"
        mock_analytics_stub.return_value.RecordApiRequest.return_value = mock_response

        # Act
        result = await client.record_api_request(
            request_type="api_call",
            endpoint="/api/v1/agents",
            status="success",
            duration_ms=250,
            user_id="user_123",
            user_email="<EMAIL>",
            method="GET",
            credits_used=1.5,
            cost=0.05
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "API request recorded successfully"
        assert result["event_id"] == "event_456"

    async def test_record_system_activity_success(self, mock_analytics_stub):
        # Arrange
        client = AnalyticsServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "System activity recorded successfully"
        mock_response.activity_id = "activity_789"
        mock_analytics_stub.return_value.RecordSystemActivity.return_value = mock_response

        # Act
        result = await client.record_system_activity(
            activity_type="user_action",
            title="User Login",
            description="User successfully logged in",
            severity="info",
            status="completed",
            user_id="user_123"
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "System activity recorded successfully"
        assert result["activity_id"] == "activity_789"
