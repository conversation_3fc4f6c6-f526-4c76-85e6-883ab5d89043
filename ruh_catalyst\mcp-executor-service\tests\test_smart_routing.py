#!/usr/bin/env python3
"""
Test script for MCP smart routing implementation.
Tests the new execution router and MCP configuration client.
"""
import asyncio
import logging
import json
from unittest.mock import patch
from app.services.mcp_config_client import MCPConfigClient
from app.services.execution_router import ExecutionRouter
from app.core_.mcp_executor import MCPExecutor

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MockProducer:
    """Mock Kafka producer for testing."""

    async def send(self, topic, message, headers=None):
        logger.info(f"Mock producer sending to {topic}: {message}")

    async def send_and_wait(self, topic, value):
        logger.info(f"Mock producer sending and waiting to {topic}: {value}")


async def test_mcp_config_client():
    """Test the MCP Configuration Client."""
    logger.info("=" * 50)
    logger.info("Testing MCP Configuration Client")
    logger.info("=" * 50)

    client = MCPConfigClient()

    # Test URL parsing with different configurations
    test_cases = [
        {
            "name": "Image + stdio (highest priority)",
            "urls": [
                {"url": "http://example.com/sse", "type": "sse"},
                {"image_name": "my-mcp:latest", "type": "stdio"},
            ],
            "expected_method": "container",
        },
        {
            "name": "Image + other type",
            "urls": [
                {"url": "http://example.com/sse", "type": "sse"},
                {"image_name": "my-mcp:latest", "type": "http"},
            ],
            "expected_method": "container",
        },
        {
            "name": "URL + sse",
            "urls": [
                {"url": "http://example.com/sse", "type": "sse"},
                {"url": "http://example.com/http", "type": "http"},
            ],
            "expected_method": "url",
        },
        {
            "name": "URL + other type",
            "urls": [{"url": "http://example.com/http", "type": "http"}],
            "expected_method": "url",
        },
    ]

    for test_case in test_cases:
        logger.info(f"\nTesting: {test_case['name']}")
        try:
            result = client.parse_urls(test_case["urls"])
            logger.info(f"  Expected method: {test_case['expected_method']}")
            logger.info(f"  Actual method: {result.execution_method}")
            logger.info(f"  Selected config: {result.config}")

            if result.execution_method == test_case["expected_method"]:
                logger.info("  ✅ PASS")
            else:
                logger.error("  ❌ FAIL")

        except Exception as e:
            logger.error(f"  ❌ ERROR: {e}")


async def test_execution_router():
    """Test the Execution Router."""
    logger.info("=" * 50)
    logger.info("Testing Execution Router")
    logger.info("=" * 50)

    router = ExecutionRouter()

    # Test with mock MCP ID (this will fail since API is not available)
    test_mcp_id = "test-mcp-123"

    try:
        logger.info(f"Testing execution router with MCP ID: {test_mcp_id}")
        strategy = await router.determine_execution_method(test_mcp_id)

        logger.info(f"Execution method: {strategy.method}")
        logger.info(f"Config: {strategy.config}")
        logger.info(f"Fallback available: {strategy.fallback_available}")

        # Test parameter extraction
        params = router.extract_execution_parameters(strategy)
        logger.info(f"Extracted parameters: {params}")

        logger.info("✅ Execution router test passed")

    except Exception as e:
        logger.error(f"❌ Execution router test failed (expected): {e}")
        logger.info("This is expected since the MCP config API is not available")


async def test_mcp_executor_integration():
    """Test the MCP Executor with smart routing."""
    logger.info("=" * 50)
    logger.info("Testing MCP Executor Integration")
    logger.info("=" * 50)

    # Create mock producer
    mock_producer = MockProducer()

    # Create MCP executor
    executor = MCPExecutor(producer=mock_producer, logger=logger)

    # Test parameters with mcp_id (should trigger smart routing)
    test_params_with_mcp_id = {
        "tool_name": "test_tool",
        "tool_parameters": {"message": "Hello from smart routing test!"},
        "retries": 1,
        "correlation_id": "test-correlation-id",
        "user_id": "test-user-id",
        "mcp_id": "test-mcp-id",
    }

    try:
        logger.info("Testing MCP executor with smart routing (mcp_id provided)")
        result = await executor.execute_tool(**test_params_with_mcp_id)
        logger.info(f"✅ Smart routing result: {result}")
    except Exception as e:
        logger.error(f"❌ Smart routing failed (expected): {e}")
        logger.info("This is expected since the MCP config API is not available")

    # Test parameters without mcp_id (should use legacy logic)
    test_params_legacy = {
        "server_script_path": "http://localhost:8080/sse",
        "tool_name": "test_tool",
        "tool_parameters": {"message": "Hello from legacy test!"},
        "retries": 1,
        "correlation_id": "test-correlation-id-2",
    }

    try:
        logger.info("Testing MCP executor with legacy logic (no mcp_id)")
        result = await executor.execute_tool(**test_params_legacy)
        logger.info(f"✅ Legacy execution result: {result}")
    except Exception as e:
        logger.error(f"❌ Legacy execution failed (expected): {e}")
        logger.info("This is expected since no actual MCP server is running")


async def test_fallback_logic():
    """Test fallback logic in execution router."""
    logger.info("=" * 50)
    logger.info("Testing Fallback Logic")
    logger.info("=" * 50)

    router = ExecutionRouter()

    # Test fallback detection
    test_urls = [
        {"image_name": "my-mcp:latest", "type": "stdio"},
        {"url": "http://example.com/sse", "type": "sse"},
    ]

    # Check fallback options
    fallback_available, fallback_config = router._check_fallback_options(
        test_urls, "container"
    )

    logger.info(f"Primary method: container")
    logger.info(f"Fallback available: {fallback_available}")
    logger.info(f"Fallback config: {fallback_config}")

    if fallback_available:
        logger.info("✅ Fallback detection working correctly")
    else:
        logger.error("❌ Fallback detection failed")


async def test_end_to_end_scenarios():
    """Test end-to-end scenarios with different MCP configurations."""
    logger.info("=" * 50)
    logger.info("Testing End-to-End Scenarios")
    logger.info("=" * 50)

    # Test scenarios with different MCP configurations
    test_scenarios = [
        {
            "name": "Image-based MCP (Container Execution)",
            "mcp_config": {
                "id": "image-mcp",
                "urls": [{"image_name": "my-mcp:latest", "type": "stdio"}],
            },
            "expected_method": "container",
        },
        {
            "name": "URL-based MCP (Direct Execution)",
            "mcp_config": {
                "id": "url-mcp",
                "urls": [{"url": "http://example.com/sse", "type": "sse"}],
            },
            "expected_method": "url",
        },
        {
            "name": "Mixed MCP (Container Priority)",
            "mcp_config": {
                "id": "mixed-mcp",
                "urls": [
                    {"url": "http://example.com/sse", "type": "sse"},
                    {"image_name": "my-mcp:latest", "type": "stdio"},
                ],
            },
            "expected_method": "container",
        },
        {
            "name": "Fallback Scenario (Container with URL backup)",
            "mcp_config": {
                "id": "fallback-mcp",
                "urls": [
                    {"image_name": "my-mcp:latest", "type": "stdio"},
                    {"url": "http://example.com/sse", "type": "sse"},
                ],
            },
            "expected_method": "container",
            "has_fallback": True,
        },
    ]

    router = ExecutionRouter()

    for scenario in test_scenarios:
        logger.info(f"\n🧪 Testing: {scenario['name']}")

        try:
            # Mock the MCP config response
            with patch.object(
                router.mcp_config_client,
                "get_mcp_config",
                return_value=scenario["mcp_config"],
            ):
                strategy = await router.determine_execution_method(
                    scenario["mcp_config"]["id"]
                )

                logger.info(f"  Expected method: {scenario['expected_method']}")
                logger.info(f"  Actual method: {strategy.method}")
                logger.info(f"  Config: {strategy.config}")

                if "has_fallback" in scenario:
                    logger.info(f"  Fallback available: {strategy.fallback_available}")
                    if strategy.fallback_available:
                        logger.info(f"  Fallback config: {strategy.fallback_config}")

                if strategy.method == scenario["expected_method"]:
                    logger.info("  ✅ PASS")
                else:
                    logger.error("  ❌ FAIL")

        except Exception as e:
            logger.error(f"  ❌ ERROR: {e}")


async def test_error_scenarios():
    """Test error handling scenarios."""
    logger.info("=" * 50)
    logger.info("Testing Error Scenarios")
    logger.info("=" * 50)

    router = ExecutionRouter()

    # Test 1: Invalid MCP configuration
    logger.info("\n🧪 Testing: Invalid MCP Configuration")
    try:
        invalid_config = {"id": "invalid", "urls": "not_an_array"}
        with patch.object(
            router.mcp_config_client, "get_mcp_config", return_value=invalid_config
        ):
            await router.determine_execution_method("invalid-mcp")
        logger.error("  ❌ Should have failed with invalid config")
    except Exception as e:
        logger.info(f"  ✅ Correctly handled error: {e}")

    # Test 2: Empty URLs array
    logger.info("\n🧪 Testing: Empty URLs Array")
    try:
        empty_config = {"id": "empty", "urls": []}
        with patch.object(
            router.mcp_config_client, "get_mcp_config", return_value=empty_config
        ):
            await router.determine_execution_method("empty-mcp")
        logger.error("  ❌ Should have failed with empty URLs")
    except Exception as e:
        logger.info(f"  ✅ Correctly handled error: {e}")

    # Test 3: API unavailable
    logger.info("\n🧪 Testing: API Unavailable")
    try:
        with patch.object(
            router.mcp_config_client,
            "get_mcp_config",
            side_effect=Exception("API Error"),
        ):
            await router.determine_execution_method("api-error-mcp")
        logger.error("  ❌ Should have failed with API error")
    except Exception as e:
        logger.info(f"  ✅ Correctly handled error: {e}")


async def main():
    """Main test function."""
    logger.info("🚀 Starting MCP Smart Routing Tests")
    logger.info("=" * 60)

    try:
        # Test 1: MCP Configuration Client
        await test_mcp_config_client()

        # Test 2: Execution Router
        await test_execution_router()

        # Test 3: MCP Executor Integration
        await test_mcp_executor_integration()

        # Test 4: Fallback Logic
        await test_fallback_logic()

        # Test 5: End-to-End Scenarios
        await test_end_to_end_scenarios()

        # Test 6: Error Scenarios
        await test_error_scenarios()

    except Exception as e:
        logger.error(f"Test suite failed: {e}", exc_info=True)

    logger.info("=" * 60)
    logger.info("🏁 MCP Smart Routing Tests Completed")

    logger.info("\n📋 Test Summary:")
    logger.info("- MCP Configuration Client: URL parsing and prioritization")
    logger.info("- Execution Router: Strategy determination and parameter extraction")
    logger.info("- MCP Executor: Smart routing integration with fallback")
    logger.info("- Fallback Logic: Container-to-URL fallback detection")
    logger.info("- End-to-End Scenarios: Different MCP configuration types")
    logger.info("- Error Scenarios: Invalid configs and API failures")

    logger.info(
        "\n⚠️  Note: Some tests are expected to fail due to missing MCP config API"
    )
    logger.info(
        "   In production, ensure the MCP config API is available at the configured base URL"
    )


if __name__ == "__main__":
    asyncio.run(main())
