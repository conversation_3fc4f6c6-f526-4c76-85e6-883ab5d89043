import React from "react";
import { <PERSON><PERSON>ir<PERSON>, XCircle, Loader2 } from "lucide-react";
import { ValidationErrors } from "./ValidationErrors";
import { MissingFields } from "./MissingFields";
import { useWorkflowValidation } from "@/hooks/useWorkflowValidation";
import { cn } from "@/lib/utils";

interface ValidationSummaryProps {
  className?: string;
  onNavigateToNode?: (nodeId: string) => void;
}

/**
 * Component for displaying a summary of validation results
 */
export function ValidationSummary({
  className,
  onNavigateToNode,
}: ValidationSummaryProps) {
  const {
    isValid,
    errors,
    warnings,
    infos,
    missingFields,
    hasValidated,
    isValidating,
  } = useWorkflowValidation();

  // If validation hasn't run yet, don't show anything
  if (!hasValidated && !isValidating) {
    return null;
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2">
        {isValidating ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <span className="text-sm font-medium">Validating workflow...</span>
          </>
        ) : isValid && missingFields.length === 0 ? (
          <>
            <CheckCircle className="h-5 w-5 text-success" />
            <span className="text-sm font-medium">Workflow is valid</span>
          </>
        ) : (
          <>
            <XCircle className="h-5 w-5 text-destructive" />
            <span className="text-sm font-medium">
              Workflow has {errors.length > 0 ? "errors" : "missing fields"}
            </span>
          </>
        )}
      </div>

      {!isValidating && (
        <>
          <ValidationErrors
            errors={errors}
            warnings={warnings}
            infos={infos}
            className="mt-4"
          />

          <MissingFields
            missingFields={missingFields}
            className="mt-4"
            onNavigateToNode={onNavigateToNode}
          />
        </>
      )}
    </div>
  );
}

interface ValidationStatusProps {
  className?: string;
}

/**
 * Component for displaying a compact validation status
 */
export function ValidationStatus({ className }: ValidationStatusProps) {
  const { isValid, errors, warnings, missingFields, hasValidated, isValidating } =
    useWorkflowValidation();

  // If validation hasn't run yet, don't show anything
  if (!hasValidated && !isValidating) {
    return null;
  }

  return (
    <div
      className={cn(
        "flex items-center gap-2",
        className
      )}
    >
      {isValidating ? (
        <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
      ) : isValid && missingFields.length === 0 ? (
        <CheckCircle className="h-4 w-4 text-success" />
      ) : (
        <XCircle className="h-4 w-4 text-destructive" />
      )}

      <span className="text-xs">
        {isValidating
          ? "Validating..."
          : isValid && missingFields.length === 0
          ? "Valid"
          : errors.length > 0
          ? `${errors.length} errors`
          : `${missingFields.length} missing fields`}
      </span>

      {warnings.length > 0 && (
        <span className="text-xs text-warning">{warnings.length} warnings</span>
      )}
    </div>
  );
}
