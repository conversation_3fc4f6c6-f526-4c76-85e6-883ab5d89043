#!/usr/bin/env python3
"""
Test script for the enhanced MCP client with fallback support.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core_.client import MC<PERSON>lient
from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_enhanced_client_with_fallback():
    """Test the enhanced MCP client with fallback support."""
    logger.info("=== Testing Enhanced MCP Client with Fallback ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return False
    
    # Test with containers that should trigger fallback
    test_containers = [
        # This container should work with standard STDIO
        "ff5d4995-d431-4566-a843-59fee0521b15_91a237fd-0225-4e02-9e9f-805eff073b07",
        # This container should work with fallback SSH
        "********-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07",
    ]
    
    for container_name in test_containers:
        logger.info(f"\n--- Testing with container: {container_name} ---")
        
        try:
            # Create enhanced MCP client with fallback enabled
            client = MCPClient(
                docker_image=container_name,
                connection_type="ssh_docker",
                container_command=None,  # Let it auto-detect
                use_fallback_ssh=True,  # Enable fallback
            )
            
            logger.info("Attempting to connect to MCP server...")
            
            async with client:
                logger.info("✅ Successfully connected to MCP server!")
                
                # Test basic MCP operations
                logger.info("Testing list_tools...")
                tools = await client.list_tools()
                logger.info(f"Available tools: {[tool.name for tool in tools]}")
                
                logger.info("Testing list_resources...")
                resources = await client.list_resources()
                logger.info(f"Available resources: {len(resources)}")
                
                logger.info("Testing list_prompts...")
                prompts = await client.list_prompts()
                logger.info(f"Available prompts: {len(prompts)}")
                
                # Check which mode was used
                if client._fallback_mode:
                    logger.info("🔄 Used fallback SSH client")
                else:
                    logger.info("📡 Used standard STDIO client")
                
                logger.info("✅ All MCP operations completed successfully!")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to connect with container {container_name}: {e}")
            continue
    
    logger.error("❌ Failed to connect with any available container")
    return False


async def test_fallback_only_mode():
    """Test the client with fallback mode forced."""
    logger.info("\n=== Testing Fallback-Only Mode ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "********-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        # Create MCP client with fallback disabled (forces fallback if standard fails)
        client = MCPClient(
            docker_image=container_name,
            connection_type="ssh_docker",
            container_command=None,
            use_fallback_ssh=False,  # This will force fallback if standard fails
        )
        
        # Force fallback mode
        client._fallback_mode = True
        
        logger.info("Testing fallback-only mode...")
        
        async with client:
            logger.info("✅ Fallback-only connection successful!")
            
            # Test basic operations
            tools = await client.list_tools()
            logger.info(f"Tools via fallback: {[tool.name for tool in tools]}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Fallback-only test failed: {e}")
        return False


async def test_configuration_based_routing():
    """Test configuration-based routing (future enhancement)."""
    logger.info("\n=== Testing Configuration-Based Routing ===")
    
    # Mock MCP configuration
    mock_config = {
        "urls": [
            {
                "image_name": "********-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07",
                "type": "stdio"
            }
        ]
    }
    
    try:
        # Create client with MCP configuration
        client = MCPClient(
            docker_image="********-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07",
            connection_type="ssh_docker",
            mcp_config=mock_config,
            use_fallback_ssh=True,
        )
        
        logger.info("Testing configuration-based client...")
        
        async with client:
            logger.info("✅ Configuration-based connection successful!")
            
            # Test operations
            tools = await client.list_tools()
            logger.info(f"Tools via config: {[tool.name for tool in tools]}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Configuration-based test failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting Enhanced MCP Client Tests")
    
    # Test 1: Enhanced client with fallback
    success1 = await test_enhanced_client_with_fallback()
    
    # Test 2: Fallback-only mode
    success2 = await test_fallback_only_mode()
    
    # Test 3: Configuration-based routing
    success3 = await test_configuration_based_routing()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY:")
    logger.info("="*60)
    logger.info(f"Enhanced client with fallback: {'✅ PASS' if success1 else '❌ FAIL'}")
    logger.info(f"Fallback-only mode: {'✅ PASS' if success2 else '❌ FAIL'}")
    logger.info(f"Configuration-based routing: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 or success2 or success3:
        logger.info("🎉 At least one test passed - Enhanced client is working!")
    else:
        logger.error("💥 All tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
