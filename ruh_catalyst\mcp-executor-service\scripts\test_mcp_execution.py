#!/usr/bin/env python3
"""
MCP Execution Test Script

This script demonstrates and tests both SSE and SSH Docker connection types
by sending various MCP tool execution requests and validating responses.

Usage:
    python test_mcp_execution.py [options]
"""

import asyncio
import argparse
import json
import logging
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config.config import settings
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MCPExecutionTester:
    """Test class for MCP execution service."""

    def __init__(self, timeout: int = 30):
        self.timeout = timeout
        self.test_results: List[Dict[str, Any]] = []

    async def send_request(self, payload: Dict[str, Any]) -> str:
        """Send request to Kafka topic."""
        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await producer.start()

            request_id = payload["request_id"]
            await producer.send_and_wait(settings.kafka_consumer_topic, value=payload)

            connection_type = "SSE" if "server_script_path" in payload else "SSH Docker"
            logger.info(f"📤 Sent {connection_type} request: {request_id}")

            return request_id

        except Exception as e:
            logger.error(f"❌ Failed to send request: {e}")
            raise
        finally:
            if producer:
                await producer.stop()

    async def wait_for_response(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Wait for response from Kafka results topic."""
        consumer = None
        try:
            consumer = AIOKafkaConsumer(
                settings.kafka_results_topic,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=f"mcp-test-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await consumer.start()

            start_time = time.time()
            while time.time() - start_time < self.timeout:
                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                response = json.loads(msg.value.decode("utf-8"))
                                if response.get("request_id") == request_id:
                                    return response
                            except json.JSONDecodeError:
                                continue

                except Exception as e:
                    logger.warning(f"Error consuming messages: {e}")

                await asyncio.sleep(0.1)

            return None

        finally:
            if consumer:
                await consumer.stop()

    async def test_sse_connection(
        self,
        server_url: str,
        tool_name: str = "echo_tool",
        tool_parameters: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Test SSE connection."""
        if tool_parameters is None:
            tool_parameters = {"message": "Hello from SSE test!"}

        logger.info(f"🧪 Testing SSE connection to {server_url}")

        request_id = str(uuid.uuid4())
        payload = {
            "request_id": request_id,
            "server_script_path": server_url,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": 2,
        }

        start_time = time.time()

        try:
            await self.send_request(payload)
            response = await self.wait_for_response(request_id)

            end_time = time.time()
            response_time = end_time - start_time

            if response:
                success = response.get("mcp_status") == "success"

                result = {
                    "test_type": "SSE",
                    "request_id": request_id,
                    "server_url": server_url,
                    "tool_name": tool_name,
                    "success": success,
                    "response_time": response_time,
                    "response": response,
                }

                self.test_results.append(result)

                if success:
                    logger.info(f"✅ SSE test passed ({response_time:.2f}s)")
                    return True
                else:
                    error = response.get("error", "Unknown error")
                    logger.error(f"❌ SSE test failed: {error}")
                    return False
            else:
                logger.error(f"❌ SSE test failed: No response received")
                return False

        except Exception as e:
            logger.error(f"❌ SSE test failed with exception: {e}")
            return False

    async def test_ssh_docker_connection(
        self,
        ssh_host: str,
        ssh_user: str,
        ssh_port: int = 22,
        ssh_key_path: Optional[str] = None,
        docker_image: str = "mcp-text-server",
        tool_name: str = "echo_tool",
        tool_parameters: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Test SSH Docker connection."""
        if tool_parameters is None:
            tool_parameters = {"message": "Hello from SSH Docker test!"}

        logger.info(
            f"🧪 Testing SSH Docker connection to {ssh_user}@{ssh_host}:{ssh_port}"
        )

        request_id = str(uuid.uuid4())
        payload = {
            "request_id": request_id,
            "ssh_host": ssh_host,
            "ssh_user": ssh_user,
            "ssh_port": ssh_port,
            "docker_image": docker_image,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": 2,
        }

        if ssh_key_path:
            payload["ssh_key_path"] = ssh_key_path

        start_time = time.time()

        try:
            await self.send_request(payload)
            response = await self.wait_for_response(request_id)

            end_time = time.time()
            response_time = end_time - start_time

            if response:
                success = response.get("mcp_status") == "success"

                result = {
                    "test_type": "SSH_Docker",
                    "request_id": request_id,
                    "ssh_host": ssh_host,
                    "ssh_user": ssh_user,
                    "ssh_port": ssh_port,
                    "docker_image": docker_image,
                    "tool_name": tool_name,
                    "success": success,
                    "response_time": response_time,
                    "response": response,
                }

                self.test_results.append(result)

                if success:
                    logger.info(f"✅ SSH Docker test passed ({response_time:.2f}s)")
                    return True
                else:
                    error = response.get("error", "Unknown error")
                    logger.error(f"❌ SSH Docker test failed: {error}")
                    return False
            else:
                logger.error(f"❌ SSH Docker test failed: No response received")
                return False

        except Exception as e:
            logger.error(f"❌ SSH Docker test failed with exception: {e}")
            return False

    async def run_comprehensive_tests(
        self,
        sse_url: Optional[str] = None,
        ssh_host: Optional[str] = None,
        ssh_user: Optional[str] = None,
        ssh_port: int = 22,
        ssh_key_path: Optional[str] = None,
        docker_image: str = "mcp-text-server",
    ) -> bool:
        """Run comprehensive tests for both connection types."""
        logger.info("🚀 Starting comprehensive MCP execution tests")
        logger.info("=" * 60)

        all_passed = True

        # Test cases
        test_cases = [
            {
                "name": "Basic Echo Test",
                "tool": "echo_tool",
                "params": {"message": "Hello World!"},
            },
            {
                "name": "Text Processing Test",
                "tool": "text_processor",
                "params": {"text": "test string", "operation": "uppercase"},
            },
            {
                "name": "JSON Processing Test",
                "tool": "json_processor",
                "params": {"data": {"key": "value"}, "transform": "keys_uppercase"},
            },
        ]

        # Run SSE tests if URL provided
        if sse_url:
            logger.info(f"\n📡 Testing SSE connection: {sse_url}")
            logger.info("-" * 40)

            for test_case in test_cases:
                logger.info(f"Running {test_case['name']} via SSE...")
                success = await self.test_sse_connection(
                    server_url=sse_url,
                    tool_name=test_case["tool"],
                    tool_parameters=test_case["params"],
                )
                if not success:
                    all_passed = False

                await asyncio.sleep(1)  # Brief pause between tests

        # Run SSH Docker tests if host/user provided
        if ssh_host and ssh_user:
            logger.info(
                f"\n🐳 Testing SSH Docker connection: {ssh_user}@{ssh_host}:{ssh_port}"
            )
            logger.info("-" * 40)

            for test_case in test_cases:
                logger.info(f"Running {test_case['name']} via SSH Docker...")
                success = await self.test_ssh_docker_connection(
                    ssh_host=ssh_host,
                    ssh_user=ssh_user,
                    ssh_port=ssh_port,
                    ssh_key_path=ssh_key_path,
                    docker_image=docker_image,
                    tool_name=test_case["tool"],
                    tool_parameters=test_case["params"],
                )
                if not success:
                    all_passed = False

                await asyncio.sleep(1)  # Brief pause between tests

        # Print summary
        self.print_test_summary()

        return all_passed

    def print_test_summary(self):
        """Print comprehensive test summary."""
        logger.info("\n" + "=" * 80)
        logger.info("📊 TEST EXECUTION SUMMARY")
        logger.info("=" * 80)

        if not self.test_results:
            logger.info("No tests were executed")
            return

        sse_results = [r for r in self.test_results if r["test_type"] == "SSE"]
        ssh_results = [r for r in self.test_results if r["test_type"] == "SSH_Docker"]

        # Overall stats
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])

        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {passed_tests/total_tests:.1%}")

        # SSE stats
        if sse_results:
            sse_passed = sum(1 for r in sse_results if r["success"])
            sse_avg_time = sum(r["response_time"] for r in sse_results) / len(
                sse_results
            )
            logger.info(f"\n📡 SSE Tests: {sse_passed}/{len(sse_results)} passed")
            logger.info(f"   Average Response Time: {sse_avg_time:.2f}s")

        # SSH Docker stats
        if ssh_results:
            ssh_passed = sum(1 for r in ssh_results if r["success"])
            ssh_avg_time = sum(r["response_time"] for r in ssh_results) / len(
                ssh_results
            )
            logger.info(
                f"\n🐳 SSH Docker Tests: {ssh_passed}/{len(ssh_results)} passed"
            )
            logger.info(f"   Average Response Time: {ssh_avg_time:.2f}s")

        # Performance comparison
        if sse_results and ssh_results:
            sse_avg = sum(r["response_time"] for r in sse_results) / len(sse_results)
            ssh_avg = sum(r["response_time"] for r in ssh_results) / len(ssh_results)
            ratio = ssh_avg / sse_avg if sse_avg > 0 else 0
            logger.info(f"\n⚡ Performance Comparison:")
            logger.info(f"   SSH Docker is {ratio:.1f}x slower than SSE")

        logger.info("=" * 80)


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command-line interface parser."""
    parser = argparse.ArgumentParser(
        description="Test MCP execution service with both connection types",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test SSE connection only
  python test_mcp_execution.py --sse-url http://localhost:8080/sse
  
  # Test SSH Docker connection only
  python test_mcp_execution.py --ssh-host server.com --ssh-user ubuntu
  
  # Test both connection types
  python test_mcp_execution.py \\
    --sse-url http://localhost:8080/sse \\
    --ssh-host server.com --ssh-user ubuntu --ssh-key ~/.ssh/id_rsa
  
  # Quick single test
  python test_mcp_execution.py --sse-url http://localhost:8080/sse --single-test
        """,
    )

    # SSE options
    parser.add_argument("--sse-url", help="SSE server URL to test")

    # SSH Docker options
    parser.add_argument("--ssh-host", help="SSH server hostname")
    parser.add_argument("--ssh-user", help="SSH username")
    parser.add_argument("--ssh-port", type=int, default=22, help="SSH port")
    parser.add_argument("--ssh-key", help="SSH private key path")
    parser.add_argument(
        "--docker-image", default="mcp-text-server", help="Docker image name"
    )

    # Test options
    parser.add_argument(
        "--single-test",
        action="store_true",
        help="Run only one test per connection type",
    )
    parser.add_argument(
        "--timeout", type=int, default=30, help="Response timeout in seconds"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    return parser


async def main():
    """Main entry point."""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Validate arguments
    if not args.sse_url and not (args.ssh_host and args.ssh_user):
        logger.error(
            "❌ Must provide either --sse-url or both --ssh-host and --ssh-user"
        )
        parser.print_help()
        return 1

    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create tester
    tester = MCPExecutionTester(timeout=args.timeout)

    try:
        if args.single_test:
            # Run single tests
            all_passed = True

            if args.sse_url:
                success = await tester.test_sse_connection(args.sse_url)
                if not success:
                    all_passed = False

            if args.ssh_host and args.ssh_user:
                success = await tester.test_ssh_docker_connection(
                    ssh_host=args.ssh_host,
                    ssh_user=args.ssh_user,
                    ssh_port=args.ssh_port,
                    ssh_key_path=args.ssh_key,
                    docker_image=args.docker_image,
                )
                if not success:
                    all_passed = False

            tester.print_test_summary()
        else:
            # Run comprehensive tests
            all_passed = await tester.run_comprehensive_tests(
                sse_url=args.sse_url,
                ssh_host=args.ssh_host,
                ssh_user=args.ssh_user,
                ssh_port=args.ssh_port,
                ssh_key_path=args.ssh_key,
                docker_image=args.docker_image,
            )

        return 0 if all_passed else 1

    except KeyboardInterrupt:
        logger.info("⏹️ Tests cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    print("🧪 MCP Execution Test Suite")
    print("   Test both SSE and SSH Docker connections")
    print("")

    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Tests cancelled")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
