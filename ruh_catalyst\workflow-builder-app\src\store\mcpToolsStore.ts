import { create } from "zustand";
import { persist } from "zustand/middleware";

// Generic component state store that works for any component type
interface ComponentState {
  [key: string]: any;
}

interface ComponentStateStore {
  // Store state by nodeId and then by key
  nodes: Record<string, ComponentState>;
  // Set a value for a specific node and key
  setValue: (nodeId: string, key: string, value: any) => void;
  // Get a value for a specific node and key with optional default
  getValue: (nodeId: string, key: string, defaultValue?: any) => any;
  // Clear all state for a specific node
  clearNodeState: (nodeId: string) => void;
  // Clear state for a specific key in a node
  clearNodeKey: (nodeId: string, key: string) => void;
  // Clear all state for all nodes
  clearAllState: () => void;
}

export const useComponentStateStore = create<ComponentStateStore>()(
  persist(
    (set, get) => ({
      nodes: {},
      setValue: (nodeId, key, value) =>
        set((state) => ({
          nodes: {
            ...state.nodes,
            [nodeId]: {
              ...(state.nodes[nodeId] || {}),
              [key]: value,
            },
          },
        })),
      getValue: (nodeId, key, defaultValue = undefined) => {
        const state = get();
        if (!state.nodes[nodeId]) return defaultValue;
        return state.nodes[nodeId][key] !== undefined ? state.nodes[nodeId][key] : defaultValue;
      },
      clearNodeState: (nodeId) =>
        set((state) => {
          const newNodes = { ...state.nodes };
          delete newNodes[nodeId];
          return { nodes: newNodes };
        }),
      clearNodeKey: (nodeId, key) =>
        set((state) => {
          if (!state.nodes[nodeId]) return state;
          const nodeState = { ...state.nodes[nodeId] };
          delete nodeState[key];
          return {
            nodes: {
              ...state.nodes,
              [nodeId]: nodeState,
            },
          };
        }),
      clearAllState: () => set({ nodes: {} }),
    }),
    { name: "component-state-store" },
  ),
);

// Helper functions for MCP Tools component
export const getMcpToolsValue = (nodeId: string, key: string, defaultValue?: any) => {
  return useComponentStateStore.getState().getValue(nodeId, key, defaultValue);
};

export const setMcpToolsValue = (nodeId: string, key: string, value: any) => {
  useComponentStateStore.getState().setValue(nodeId, key, value);
};

export const clearMcpToolsState = (nodeId: string) => {
  // Clear from Zustand store
  useComponentStateStore.getState().clearNodeState(nodeId);

  console.log(`MCP tools state cleared for node ${nodeId}`);
};

// For backward compatibility
export const clearAllMcpToolsState = () => {
  try {
    // Clear from local storage directly
    localStorage.removeItem("mcp-tools-store");
    // Also clear from the new store
    useComponentStateStore.getState().clearAllState();
    console.log("All MCP tools state cleared from local storage");
  } catch (error) {
    console.error("Error clearing MCP tools state from local storage:", error);
  }
};
