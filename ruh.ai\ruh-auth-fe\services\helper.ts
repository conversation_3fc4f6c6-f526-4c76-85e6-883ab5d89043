import { getAccessToken, getRefreshToken } from "./authCookies";

export const setRedirectUrl = (redirectUrl: string) => {
  document.cookie = `redirect_url=${redirectUrl}; path=/; max-age=31536000; samesite=lax`;
};

export const getRedirectUrl = (): string | undefined => {
  const cookieValue = document.cookie
    .split("; ")
    .find((row) => row.startsWith("redirect_url="))
    ?.split("=")[1];
  return cookieValue || process.env.NEXT_PUBLIC_DEFAULT_REDIRECT!;
};

export const isAuthenticated = () => {
  const accessToken = getAccessToken();
  const refreshToken = getRefreshToken();
  return accessToken || refreshToken;
};
