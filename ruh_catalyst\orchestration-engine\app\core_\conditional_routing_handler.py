"""
Conditional Routing Handler - Processes routing decisions from conditional components.

This module handles the integration between conditional components and the orchestration engine:
- Detects conditional component execution results
- Extracts routing decisions from component results
- Processes routing decisions to determine next transitions
- Provides error handling for malformed or missing routing decisions

Following TDD methodology - Phase 2 Cycle 1: Conditional Component Detection and Result Processing
"""

import logging
from typing import Dict, Any, List, Optional


class ConditionalRoutingHandler:
    """
    Handles routing decisions from conditional components.
    
    This class provides the integration layer between conditional component execution
    results and the orchestration engine's transition routing logic.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the conditional routing handler.
        
        Args:
            logger: Logger instance for logging routing decisions and errors
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def is_conditional_component_result(self, execution_result: Dict[str, Any]) -> bool:
        """
        Check if execution result is from a conditional component.

        Conditional component results are identified by the presence of a 'routing_decision'
        key in the execution result, regardless of the status (success or error).

        Args:
            execution_result: The execution result from a component

        Returns:
            True if the result is from a conditional component, False otherwise
        """
        if not isinstance(execution_result, dict):
            self.logger.debug(f"🔍 is_conditional_component_result: Not a dict, type={type(execution_result)}")
            return False

        has_routing_decision = "routing_decision" in execution_result
        self.logger.debug(f"🔍 is_conditional_component_result: has_routing_decision={has_routing_decision}")
        self.logger.debug(f"🔍 execution_result keys: {list(execution_result.keys())}")

        return has_routing_decision
    
    async def handle_conditional_result(
        self,
        execution_result: Dict[str, Any],
        transition: Dict[str, Any]
    ) -> List[str]:
        """
        Process conditional component result and determine next transitions.

        Supports both single and multiple transition routing based on the conditional
        component's evaluation strategy and response format.

        Args:
            execution_result: The execution result from the conditional component
            transition: The transition configuration that was executed

        Returns:
            List of next transition IDs to execute
        """
        try:
            transition_id = transition.get("id", "unknown")
            self.logger.info(f"Processing conditional routing result for transition: {transition_id}")

            # Extract routing decision from result
            routing_decision = execution_result.get("routing_decision")

            if routing_decision is None:
                self.logger.warning(
                    f"No routing decision found in conditional component result for transition: {transition_id}"
                )
                return []

            # Handle both new (array) and legacy (single) formats
            target_transitions = self._extract_target_transitions(routing_decision, transition_id)

            if not target_transitions:
                self.logger.warning(
                    f"No target transitions found in routing decision for transition: {transition_id}"
                )
                return []

            # Log routing decision details
            self._log_routing_decision(routing_decision, transition_id, target_transitions)

            return target_transitions

        except Exception as e:
            self.logger.error(
                f"Error processing conditional routing result for transition {transition.get('id', 'unknown')}: {e}",
                exc_info=True
            )
            return []

    def _extract_target_transitions(self, routing_decision: Dict[str, Any], transition_id: str) -> List[str]:
        """
        Extract target transitions from routing decision, supporting both formats.

        Args:
            routing_decision: The routing decision from conditional component
            transition_id: The transition ID for logging

        Returns:
            List of target transition IDs
        """
        # Check for new array format first
        target_transitions = routing_decision.get("target_transitions")
        if target_transitions is not None:
            if isinstance(target_transitions, list):
                # Filter out None values and ensure all are strings
                valid_transitions = [t for t in target_transitions if t is not None]
                self.logger.debug(f"Found {len(valid_transitions)} target transitions in array format for {transition_id}")
                return valid_transitions
            else:
                self.logger.warning(f"target_transitions is not a list for {transition_id}: {type(target_transitions)}")

        # Fallback to legacy single transition format
        target_transition = routing_decision.get("target_transition")
        if target_transition is not None:
            self.logger.debug(f"Found single target transition in legacy format for {transition_id}")
            return [target_transition]

        return []

    def _log_routing_decision(
        self,
        routing_decision: Dict[str, Any],
        transition_id: str,
        target_transitions: List[str]
    ) -> None:
        """
        Log routing decision details for debugging and monitoring.

        Args:
            routing_decision: The routing decision from conditional component
            transition_id: The transition ID for logging
            target_transitions: List of target transition IDs
        """
        matched_conditions = routing_decision.get("matched_conditions", routing_decision.get("matched_condition"))
        condition_result = routing_decision.get("condition_result", False)
        execution_time_ms = routing_decision.get("execution_time_ms", 0)
        evaluation_strategy = routing_decision.get("evaluation_strategy", "unknown")

        if len(target_transitions) == 1:
            # Single transition logging
            self.logger.info(
                f"Conditional routing decision for {transition_id}: "
                f"target='{target_transitions[0]}', matched_condition={matched_conditions}, "
                f"result={condition_result}, execution_time={execution_time_ms:.2f}ms"
            )
        else:
            # Multiple transitions logging
            self.logger.info(
                f"Conditional routing decision for {transition_id}: "
                f"targets={target_transitions}, matched_conditions={matched_conditions}, "
                f"result={condition_result}, strategy={evaluation_strategy}, "
                f"execution_time={execution_time_ms:.2f}ms"
            )
