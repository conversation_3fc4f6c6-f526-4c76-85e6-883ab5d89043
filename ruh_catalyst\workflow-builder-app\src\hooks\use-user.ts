// src/hooks/use-user.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
  isAuthenticated: boolean;
}

interface UserState {
  user: User | null;
  setUser: (user: User) => void;
  clearUser: () => void;
  isAuthenticated: () => boolean;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: null,
      setUser: (user: User) => set({ user: { ...user, isAuthenticated: true } }),
      clearUser: () => set({ user: null }),
      isAuthenticated: () => !!get().user?.isAuthenticated,
    }),
    {
      name: "user-storage",
    },
  ),
);
