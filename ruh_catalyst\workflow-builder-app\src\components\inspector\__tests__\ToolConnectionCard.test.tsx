/**
 * Tests for Tool Connection Card component
 */

import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ToolConnectionCard } from "../ToolConnectionCard";

interface ToolConnection {
  nodeId: string;
  handleId: string;
  componentType: string;
  label: string;
}

describe("ToolConnectionCard", () => {
  const mockOnRemove = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    test("renders tool connection information correctly", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.getByText("Data Processing Tool")).toBeInTheDocument();
      expect(screen.getByText("DataProcessor")).toBeInTheDocument();
      expect(screen.getByText("tool_1")).toBeInTheDocument();
    });

    test("displays MCP badge for MCP marketplace components", () => {
      const mcpToolConnection: ToolConnection = {
        nodeId: "mcp1",
        handleId: "tool_1",
        componentType: "MCPMarketplace",
        label: "MCP Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={mcpToolConnection}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.getByText("MCP")).toBeInTheDocument();
      expect(screen.getByText("MCP Tool")).toBeInTheDocument();
    });

    test("shows connected status indicator", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.getByText("Connected")).toBeInTheDocument();
      // Should have green status indicator
      const statusIndicator = screen.getByTestId("connection-status");
      expect(statusIndicator).toHaveClass("bg-green-500");
    });

    test("displays tool handle ID with proper styling", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_5",
        componentType: "TextAnalyzer",
        label: "Text Analysis Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      const handleElement = screen.getByText("tool_5");
      expect(handleElement).toHaveClass("font-mono");
      expect(handleElement).toHaveClass("text-xs");
    });
  });

  describe("Interactions", () => {
    test("calls onRemove when remove button is clicked", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      const removeButton = screen.getByLabelText("Remove tool connection");
      fireEvent.click(removeButton);

      expect(mockOnRemove).toHaveBeenCalledWith("tool_1");
    });

    test("remove button has proper hover state", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      const removeButton = screen.getByLabelText("Remove tool connection");
      expect(removeButton).toHaveClass("hover:bg-red-100");
    });
  });

  describe("Component Type Styling", () => {
    test("applies correct styling for different component types", () => {
      const testCases = [
        { type: "DataProcessor", expectedIcon: "🔄" },
        { type: "TextAnalyzer", expectedIcon: "📝" },
        { type: "MCPMarketplace", expectedIcon: "🔌" },
        { type: "FileReader", expectedIcon: "📁" },
      ];

      testCases.forEach(({ type, expectedIcon }) => {
        const toolConnection: ToolConnection = {
          nodeId: "tool1",
          handleId: "tool_1",
          componentType: type,
          label: `${type} Tool`,
        };

        const { unmount } = render(
          <ToolConnectionCard
            toolConnection={toolConnection}
            onRemove={mockOnRemove}
          />
        );

        expect(screen.getByText(expectedIcon)).toBeInTheDocument();
        unmount();
      });
    });

    test("shows default icon for unknown component types", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "UnknownComponent",
        label: "Unknown Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.getByText("🔧")).toBeInTheDocument(); // Default tool icon
    });
  });

  describe("Accessibility", () => {
    test("has proper ARIA labels and roles", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.getByRole("article")).toBeInTheDocument();
      expect(screen.getByLabelText("Remove tool connection")).toBeInTheDocument();
      expect(screen.getByLabelText("Tool connection status")).toBeInTheDocument();
    });

    test("provides proper keyboard navigation", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      const removeButton = screen.getByLabelText("Remove tool connection");
      expect(removeButton).toHaveAttribute("tabIndex", "0");
    });

    test("has proper focus management", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      const removeButton = screen.getByLabelText("Remove tool connection");
      removeButton.focus();
      expect(removeButton).toHaveFocus();
    });
  });

  describe("Visual States", () => {
    test("shows loading state when specified", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
          isLoading={true}
        />
      );

      expect(screen.getByTestId("loading-spinner")).toBeInTheDocument();
    });

    test("shows error state when specified", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
          hasError={true}
        />
      );

      const statusIndicator = screen.getByTestId("connection-status");
      expect(statusIndicator).toHaveClass("bg-red-500");
      expect(screen.getByText("Error")).toBeInTheDocument();
    });

    test("shows disabled state when specified", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Data Processing Tool",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
          isDisabled={true}
        />
      );

      const removeButton = screen.getByLabelText("Remove tool connection");
      expect(removeButton).toBeDisabled();
      
      const card = screen.getByRole("article");
      expect(card).toHaveClass("opacity-50");
    });
  });

  describe("Responsive Design", () => {
    test("maintains proper layout on different screen sizes", () => {
      const toolConnection: ToolConnection = {
        nodeId: "tool1",
        handleId: "tool_1",
        componentType: "DataProcessor",
        label: "Very Long Tool Name That Should Wrap Properly",
      };

      render(
        <ToolConnectionCard
          toolConnection={toolConnection}
          onRemove={mockOnRemove}
        />
      );

      const card = screen.getByRole("article");
      expect(card).toHaveClass("flex");
      expect(card).toHaveClass("items-start");
    });
  });
});
