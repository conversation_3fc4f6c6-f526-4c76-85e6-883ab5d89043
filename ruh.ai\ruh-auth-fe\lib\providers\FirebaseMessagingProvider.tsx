"use client";

import { useEffect } from "react";
import { initializeApp } from "firebase/app";
import { getMessaging, onMessage } from "firebase/messaging";
import { firebaseConfig } from "@/app/shared/firebaseConfig";

/**
 * Firebase Messaging Provider component for handling foreground notifications
 * This component doesn't render anything visible but sets up FCM notification handling
 */
export function FirebaseMessagingProvider() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined") return;

    // Initialize Firebase if not already initialized
    try {
      // Check if Firebase is already initialized
      initializeApp(firebaseConfig);
    } catch (error: any) {
      // If Firebase is already initialized, get the existing instance
      if (error.code !== "app/duplicate-app") {
        return;
      }
    }

    // Get messaging instance
    const messaging = getMessaging();

    // Set up foreground message handler
    const unsubscribe = onMessage(messaging, (payload) => {
      if (payload.notification) {
        // show a native notification if possible
        if (Notification.permission === "granted") {
          try {
            new Notification(payload.notification.title || "New Notification", {
              body: payload.notification.body,
              icon: "/icon.png",
            });
          } catch (error) {
            // Silent error handling
          }
        }
      }
    });

    // Clean up
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // This component doesn't render anything visible
  return null;
}
