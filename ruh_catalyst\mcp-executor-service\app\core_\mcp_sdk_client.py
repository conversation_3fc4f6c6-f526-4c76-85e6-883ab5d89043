"""
MCP SDK-based client implementation.
Uses the official MCP Python SDK for communication.
"""

import asyncio
import logging
import json
from typing import Dict, Any, List, Optional, Tuple, Union

from .mcp_client_interface import (
    MCPClientInterface,
    MCPConnectionError,
    MCPProtocolError,
    MCPTimeoutError,
    MCPAuthenticationError,
)
from app.config.config import settings
from app.services.ssh_manager import get_global_ssh_manager

# MCP SDK imports
try:
    from mcp import ClientSession
    from mcp.client.stdio import stdio_client, StdioServerParameters
    from mcp.client.sse import sse_client
    from mcp.client.streamable_http import streamablehttp_client
    from mcp import types

    MCP_SDK_AVAILABLE = True
except ImportError as e:
    logging.getLogger(__name__).warning(f"MCP SDK not available: {e}")
    MCP_SDK_AVAILABLE = False

    # Create dummy classes for type hints
    class ClientSession:
        pass

    class StdioServerParameters:
        pass


logger = logging.getLogger(__name__)


class MCPSDKClient(MCPClientInterface):
    """
    MCP client implementation using the official MCP Python SDK.

    This implementation handles large responses better than custom JSON-RPC
    due to proper streaming and buffer management in the SDK.

    Supports multiple connection types:
    - SSH Docker: Connect to MCP server running in Docker container via SSH
    - HTTP/SSE: Connect to HTTP-based MCP servers with Server-Sent Events
    - Stdio: Connect to local MCP servers via standard input/output
    """

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MCPSDKClient")
        self._connected = False
        self._config: Optional[Dict[str, Any]] = None
        self._session: Optional[ClientSession] = None
        self._context: Optional[Any] = None
        self._streams: Optional[Tuple[Any, Any]] = None
        self._process: Optional[asyncio.subprocess.Process] = None

    async def connect(self, config: Dict[str, Any]) -> None:
        """
        Connect to MCP server using SDK - following custom client pattern exactly.
        """
        if not MCP_SDK_AVAILABLE:
            raise MCPConnectionError(
                "MCP SDK is not available. Please install with: pip install mcp"
            )

        # Store config (like custom client)
        self._config = config

        self.logger.info(
            f"🔗 Connecting using MCP SDK - Type: {config.get('connection_type', 'ssh_docker')}"
        )

        # Follow custom client pattern: connect with retry
        await self._connect_with_retry()

        # Mark as connected (like custom client)
        self._connected = True
        self.logger.info("✅ MCP SDK connection established successfully")

    async def list_tools(self) -> List[Dict[str, Any]]:
        """List tools using MCP SDK."""
        if not self._connected or not self._session:
            raise MCPConnectionError("Not connected - call connect() first")

        try:
            self.logger.debug("📋 Listing tools via MCP SDK")

            # Use SDK to list tools with timeout
            result = await asyncio.wait_for(
                self._session.list_tools(), timeout=settings.mcp_sdk_timeout
            )

            # Convert MCP types to dict format
            tools_list = []
            for tool in result.tools:
                tool_dict = {
                    "name": tool.name,
                    "description": tool.description or "",
                }
                if hasattr(tool, "inputSchema") and tool.inputSchema:
                    tool_dict["inputSchema"] = tool.inputSchema
                tools_list.append(tool_dict)

            self.logger.info(f"✅ Retrieved {len(tools_list)} tools via MCP SDK")
            return tools_list

        except asyncio.TimeoutError:
            raise MCPTimeoutError(
                f"List tools timed out after {settings.mcp_sdk_timeout}s"
            )
        except Exception as e:
            self.logger.error(f"❌ Failed to list tools via MCP SDK: {e}")
            raise MCPProtocolError(f"Failed to list tools: {e}") from e

    async def call_tool(
        self, tool_name: str, arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Call tool using MCP SDK."""
        if not self._connected or not self._session:
            raise MCPConnectionError("Not connected - call connect() first")

        try:
            self.logger.debug(f"🔧 Calling tool '{tool_name}' via MCP SDK")

            # Use SDK to call tool with timeout
            result = await asyncio.wait_for(
                self._session.call_tool(tool_name, arguments),
                timeout=settings.mcp_sdk_timeout,
            )

            # Convert MCP types to dict format
            result_dict = {"content": [], "isError": getattr(result, "isError", False)}

            # Process content
            if hasattr(result, "content") and result.content:
                for content_item in result.content:
                    if hasattr(content_item, "type") and hasattr(content_item, "text"):
                        result_dict["content"].append(
                            {"type": content_item.type, "text": content_item.text}
                        )
                    elif isinstance(content_item, dict):
                        result_dict["content"].append(content_item)
                    else:
                        # Handle other content types
                        result_dict["content"].append(
                            {"type": "text", "text": str(content_item)}
                        )

            self.logger.info(f"✅ Tool '{tool_name}' executed successfully via MCP SDK")
            return result_dict

        except asyncio.TimeoutError:
            raise MCPTimeoutError(
                f"Tool call '{tool_name}' timed out after {settings.mcp_sdk_timeout}s"
            )
        except Exception as e:
            self.logger.error(f"❌ Failed to call tool '{tool_name}' via MCP SDK: {e}")
            raise MCPProtocolError(f"Failed to call tool {tool_name}: {e}") from e

    async def close(self) -> None:
        """Close connection and clean up resources - simplified like custom client."""
        self.logger.info("🔒 Closing MCP SDK client")

        try:
            # Close MCP session (simple, no complex error handling)
            if self._session:
                try:
                    await self._session.__aexit__(None, None, None)
                except Exception as e:
                    self.logger.warning(f"Error closing MCP session: {e}")
                self._session = None

            # Close context (for HTTP/SSE/stdio connections)
            if self._context:
                try:
                    await self._context.__aexit__(None, None, None)
                except Exception as e:
                    self.logger.warning(f"Error closing MCP context: {e}")
                self._context = None

            # Close subprocess (for SSH connections)
            if self._process:
                try:
                    if self._process.returncode is None:
                        self._process.terminate()
                        try:
                            await asyncio.wait_for(self._process.wait(), timeout=5.0)
                        except asyncio.TimeoutError:
                            self.logger.warning(
                                "Process didn't terminate gracefully, killing..."
                            )
                            self._process.kill()
                            await self._process.wait()
                except Exception as e:
                    self.logger.warning(f"Error closing subprocess: {e}")
                self._process = None

            # Clear streams
            self._streams = None

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
        finally:
            self._connected = False
            self.logger.info("✅ MCP SDK client closed successfully")

    async def is_connected(self) -> bool:
        """Check if client is connected."""
        if not self._connected:
            return False

        # Additional checks for session health
        if not self._session:
            return False

        # For subprocess connections, check if process is still alive
        if self._process and self._process.returncode is not None:
            return False

        return True

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check using the SDK client."""
        base_health = await super().health_check()

        # Add SDK-specific health info
        base_health.update(
            {
                "implementation_type": "MCP_SDK",
                "sdk_version": "1.9.2",
                "buffer_size": settings.mcp_sdk_buffer_size,
                "timeout": settings.mcp_sdk_timeout,
                "stub_mode": False,  # Now fully implemented
                "session_active": self._session is not None,
                "process_active": self._process is not None
                and (self._process.returncode is None if self._process else False),
                "connection_type": (
                    self._config.get("connection_type", "unknown")
                    if self._config
                    else "unknown"
                ),
            }
        )

        return base_health

    async def _connect_with_retry(self) -> None:
        """
        Connect with retry logic - replicates custom client's _connect_with_retry exactly.
        """
        max_retries = 3  # Same as custom client
        last_error = None

        for attempt in range(max_retries):
            try:
                self.logger.info(
                    f"Connecting to MCP server (attempt {attempt + 1}/{max_retries})"
                )
                await self._establish_connection()
                self.logger.info(
                    f"Successfully connected to MCP server after {attempt + 1} attempts"
                )
                return

            except Exception as e:
                last_error = e
                self.logger.warning(f"Connection attempt {attempt + 1} failed: {e}")

                if attempt < max_retries - 1:
                    delay = 2**attempt  # Exponential backoff like custom client
                    self.logger.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    break

        # All attempts failed
        raise MCPConnectionError(
            f"Failed to connect after {max_retries} attempts. Last error: {last_error}"
        )

    async def _establish_connection(self) -> None:
        """
        Establish the actual connection - replicates custom client's _establish_connection.
        """
        connection_type = self._config.get("connection_type", "ssh_docker")

        if connection_type == "ssh_docker":
            await self._establish_ssh_docker_connection()
        elif connection_type == "stdio":
            await self._connect_stdio(self._config)
        elif connection_type in ["http", "sse", "streamable_http"]:
            await self._connect_http(self._config)
        elif connection_type == "test":
            await self._connect_test_mode(self._config)
        else:
            raise MCPConnectionError(f"Unsupported connection type: {connection_type}")

    async def _establish_ssh_docker_connection(self) -> None:
        """
        Establish SSH Docker connection - replicates custom client's flow exactly.
        """
        ssh_host = self._config.get("ssh_host")
        ssh_user = self._config.get("ssh_user")
        container_name = self._config.get("container_name")
        container_command = self._config.get("container_command")

        if not all([ssh_host, ssh_user, container_name]):
            raise MCPConnectionError(
                "SSH Docker connection requires ssh_host, ssh_user, and container_name"
            )

        # Auto-detect container command if not provided (like custom client)
        if not container_command:
            self.logger.info("🔍 Auto-detecting container command...")
            container_command = await self._detect_container_command(
                ssh_host, ssh_user, container_name
            )
            self.logger.info(f"✅ Detected container command: {container_command}")

        # Build SSH command exactly like custom client (simpler)
        ssh_command = self._build_simple_ssh_command(
            ssh_host, ssh_user, container_name, container_command
        )

        # Establish MCP SDK connection (replaces custom client's JSON-RPC)
        await self._establish_mcp_sdk_connection(ssh_command)

    def _build_simple_ssh_command(
        self, ssh_host: str, ssh_user: str, container_name: str, container_command: str
    ) -> List[str]:
        """
        Build simple SSH command - exactly like custom client's _build_simple_ssh_command.
        """
        # Get SSH key from global manager (like custom client)
        global_ssh_manager = get_global_ssh_manager()
        ssh_key_path = global_ssh_manager.get_ssh_key_path()

        if not ssh_key_path:
            raise MCPConnectionError("SSH key not available from global manager")

        # Build simple command exactly like custom client (no extra SSH options)
        ssh_command = [
            "ssh",
            "-i",
            ssh_key_path,
            f"{ssh_user}@{ssh_host}",
            f"docker exec -i {container_name} {container_command}",
        ]

        self.logger.info(f"🚀 Simple SSH command: {' '.join(ssh_command)}")
        return ssh_command

    async def _establish_mcp_sdk_connection(self, ssh_command: List[str]) -> None:
        """
        Establish MCP SDK connection - replaces custom client's JSON-RPC with MCP SDK.
        """
        # Create subprocess (same as custom client)
        self._process = await asyncio.create_subprocess_exec(
            *ssh_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            limit=settings.mcp_sdk_buffer_size,
        )

        # Wait for process to start (like custom client)
        await asyncio.sleep(2)

        # Check if process started successfully (like custom client)
        if self._process.returncode is not None:
            stderr_output = ""
            if self._process.stderr:
                stderr_data = await self._process.stderr.read()
                stderr_output = stderr_data.decode()
            raise MCPConnectionError(f"SSH process failed to start: {stderr_output}")

        # Create MCP SDK stream adapters
        class MCPReadStreamAdapter:
            def __init__(self, read_stream):
                self.read_stream = read_stream

            async def recv(self):
                line = await self.read_stream.readline()
                return line.decode("utf-8").strip()

            async def aclose(self):
                pass

        class MCPWriteStreamAdapter:
            def __init__(self, write_stream):
                self.write_stream = write_stream

            async def send(self, data):
                if hasattr(data, "message") and hasattr(
                    data.message, "model_dump_json"
                ):
                    json_data = data.message.model_dump_json()
                elif hasattr(data, "model_dump_json"):
                    json_data = data.model_dump_json()
                elif isinstance(data, str):
                    json_data = data
                else:
                    json_data = str(data)
                self.write_stream.write(json_data.encode("utf-8"))
                self.write_stream.write(b"\n")
                await self.write_stream.drain()

            async def aclose(self):
                if hasattr(self.write_stream, "close"):
                    self.write_stream.close()

        # Create adapters and streams
        self._read_adapter = MCPReadStreamAdapter(self._process.stdout)
        self._write_adapter = MCPWriteStreamAdapter(self._process.stdin)
        self._streams = (self._process.stdout, self._process.stdin)

        # Create and initialize MCP SDK session (replaces custom client's JSON-RPC)
        self._session = ClientSession(self._read_adapter, self._write_adapter)
        await self._session.__aenter__()

        # Test connection like custom client does
        self.logger.info("Testing MCP SDK connection...")
        await asyncio.wait_for(self._session.initialize(), timeout=30)
        self.logger.info("✅ MCP SDK session initialized")

        # Verify with list_tools (like custom client)
        await asyncio.wait_for(self._session.list_tools(), timeout=15)
        self.logger.info("✅ MCP SDK connection verified")

    async def _connect_ssh_docker_with_retry(self, config: Dict[str, Any]) -> None:
        """
        Connect via SSH to Docker container with retry logic like custom client.
        This method replicates the custom client's simple and effective approach.
        """
        ssh_host = config.get("ssh_host")
        ssh_user = config.get("ssh_user")
        container_name = config.get("container_name")
        container_command = config.get("container_command")

        if not all([ssh_host, ssh_user, container_name]):
            raise MCPConnectionError(
                "SSH Docker connection requires ssh_host, ssh_user, and container_name"
            )

        # Auto-detect container command if not provided
        if not container_command:
            self.logger.info("🔍 Auto-detecting container command...")
            container_command = await self._detect_container_command(
                ssh_host, ssh_user, container_name
            )
            self.logger.info(f"✅ Detected container command: {container_command}")

        # Get SSH key from global manager
        global_ssh_manager = get_global_ssh_manager()
        ssh_key_path = global_ssh_manager.get_ssh_key_path()

        if not ssh_key_path:
            raise MCPConnectionError("SSH key not available from global manager")

        # Try the detected command first, then alternatives if it fails
        commands_to_try = [container_command]

        # Add alternative commands (like custom client does)
        alternative_commands = [
            "python server.py",
            "node dist/index.js",
            "npm start",
            "node server.js",
            "python -m server",
            "node index.js",
            "python main.py",
            "node app.js",
        ]

        # Add alternatives that aren't the same as detected command
        for alt_cmd in alternative_commands:
            if alt_cmd != container_command and alt_cmd not in commands_to_try:
                commands_to_try.append(alt_cmd)

        self.logger.info(f"Will try {len(commands_to_try)} commands: {commands_to_try}")

        last_error = None
        for attempt, cmd in enumerate(commands_to_try):
            try:
                self.logger.info(
                    f"🔄 Attempt {attempt + 1}/{len(commands_to_try)}: Trying command '{cmd}'"
                )

                # Build SSH command
                ssh_command = [
                    "ssh",
                    "-i",
                    ssh_key_path,
                    "-o",
                    "StrictHostKeyChecking=no",
                    "-o",
                    "UserKnownHostsFile=/dev/null",
                    "-o",
                    "ConnectTimeout=30",
                    "-o",
                    "IdentitiesOnly=yes",
                    f"{ssh_user}@{ssh_host}",
                    f"docker exec -i {container_name} {cmd}",
                ]

                self.logger.info(f"🚀 SSH command: {' '.join(ssh_command)}")

                # Create subprocess
                self._process = await asyncio.create_subprocess_exec(
                    *ssh_command,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    limit=settings.mcp_sdk_buffer_size,
                )

                # Wait for process to start
                await asyncio.sleep(2)

                # Check if process started successfully
                if self._process.returncode is not None:
                    stderr_output = ""
                    if self._process.stderr:
                        stderr_data = await self._process.stderr.read()
                        stderr_output = stderr_data.decode()
                    raise MCPConnectionError(
                        f"SSH process failed to start: {stderr_output}"
                    )

                # Create stream adapters (define classes locally)
                class MCPReadStreamAdapter:
                    def __init__(self, read_stream):
                        self.read_stream = read_stream

                    async def recv(self):
                        line = await self.read_stream.readline()
                        return line.decode("utf-8").strip()

                    async def aclose(self):
                        pass

                class MCPWriteStreamAdapter:
                    def __init__(self, write_stream):
                        self.write_stream = write_stream

                    async def send(self, data):
                        if hasattr(data, "message") and hasattr(
                            data.message, "model_dump_json"
                        ):
                            json_data = data.message.model_dump_json()
                        elif hasattr(data, "model_dump_json"):
                            json_data = data.model_dump_json()
                        elif isinstance(data, str):
                            json_data = data
                        else:
                            json_data = str(data)
                        self.write_stream.write(json_data.encode("utf-8"))
                        self.write_stream.write(b"\n")
                        await self.write_stream.drain()

                    async def aclose(self):
                        if hasattr(self.write_stream, "close"):
                            self.write_stream.close()

                self._read_adapter = MCPReadStreamAdapter(self._process.stdout)
                self._write_adapter = MCPWriteStreamAdapter(self._process.stdin)
                self._streams = (self._process.stdout, self._process.stdin)

                # Test the connection by creating and initializing session
                self._session = ClientSession(self._read_adapter, self._write_adapter)
                await self._session.__aenter__()

                # Test initialization (like custom client does)
                self.logger.info(f"Testing MCP initialization for '{cmd}'...")
                await asyncio.wait_for(self._session.initialize(), timeout=30)
                self.logger.info(f"✅ MCP initialization successful for '{cmd}'")

                # Test list_tools to ensure it's working
                self.logger.info(f"Testing list_tools for '{cmd}'...")
                await asyncio.wait_for(self._session.list_tools(), timeout=15)
                self.logger.info(f"✅ List tools successful for '{cmd}'")

                # Success! Update config and return
                self._config["container_command"] = cmd
                self.logger.info(f"🎉 SUCCESS: Command '{cmd}' works perfectly!")
                self.logger.info("✅ SSH Docker connection established")
                return

            except Exception as e:
                last_error = e
                self.logger.warning(f"Command '{cmd}' failed: {e}")

                # Clean up failed attempt
                if hasattr(self, "_session") and self._session:
                    try:
                        await self._session.__aexit__(None, None, None)
                    except:
                        pass
                    self._session = None

                if hasattr(self, "_process") and self._process:
                    try:
                        if self._process.returncode is None:
                            self._process.terminate()
                            await asyncio.wait_for(self._process.wait(), timeout=5.0)
                    except:
                        pass
                    self._process = None

                # Continue to next command
                continue

        # All commands failed
        raise MCPConnectionError(
            f"All {len(commands_to_try)} commands failed. Last error: {last_error}"
        )

    # Old complex retry method removed - using simple custom client pattern now

    async def _detect_container_command(
        self, ssh_host: str, ssh_user: str, container_name: str
    ) -> str:
        """
        Detect the container command using docker inspect via SSH.
        Based on the proven approach from the custom client.
        """
        try:
            import subprocess
            import json

            # Get SSH key from global manager
            global_ssh_manager = get_global_ssh_manager()
            ssh_key_path = global_ssh_manager.get_ssh_key_path()

            if not ssh_key_path:
                self.logger.warning("SSH key not available, using default command")
                return "python server.py"  # Same default as custom client

            # Step 1: Always check Entrypoint + Cmd first (most accurate)
            self.logger.info("Step 1: Checking Entrypoint + Cmd (most accurate)")
            entrypoint_cmd = [
                "ssh",
                "-i",
                ssh_key_path,
                f"{ssh_user}@{ssh_host}",
                f"docker inspect {container_name} --format='{{{{json .Config.Entrypoint}}}} {{{{json .Config.Cmd}}}}'",
            ]

            result = subprocess.run(
                entrypoint_cmd, capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                parts = result.stdout.strip().split(" ", 1)
                entrypoint_json = parts[0].strip("'\"")
                cmd_json = parts[1].strip("'\"") if len(parts) > 1 else "null"

                command_parts = []

                # Add entrypoint if it exists
                if entrypoint_json and entrypoint_json != "null":
                    try:
                        entrypoint = json.loads(entrypoint_json)
                        if entrypoint:
                            command_parts.extend(entrypoint)
                            self.logger.info(f"Added entrypoint: {entrypoint}")
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse entrypoint JSON: {e}")

                # Add cmd if it exists
                if cmd_json and cmd_json != "null":
                    try:
                        cmd = json.loads(cmd_json)
                        if cmd:
                            command_parts.extend(cmd)
                            self.logger.info(f"Added cmd: {cmd}")
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse cmd JSON: {e}")

                if command_parts:
                    detected_command = " ".join(command_parts)
                    self.logger.info(
                        f"✅ Detected command from Entrypoint+Cmd: {detected_command}"
                    )
                    return detected_command

            # Step 2: Fallback to Cmd only if Entrypoint+Cmd failed
            self.logger.info(
                "Step 2: Entrypoint+Cmd failed, trying Cmd only as fallback"
            )

            cmd_inspect = [
                "ssh",
                "-i",
                ssh_key_path,
                f"{ssh_user}@{ssh_host}",
                f"docker inspect {container_name} --format='{{{{json .Config.Cmd}}}}'",
            ]

            result = subprocess.run(
                cmd_inspect, capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                cmd_json = result.stdout.strip().strip("'\"")
                self.logger.info(f"Cmd JSON: {cmd_json}")

                if cmd_json and cmd_json != "null":
                    try:
                        cmd_list = json.loads(cmd_json)
                        if cmd_list:
                            detected_command = " ".join(cmd_list)
                            self.logger.info(
                                f"✅ Detected command from Cmd only: {detected_command}"
                            )
                            return detected_command
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse Cmd JSON: {e}")

            # Step 3: Final fallback (same as custom client)
            self.logger.warning("Could not determine container command, using default")
            default_command = "python server.py"
            self.logger.info(f"Using default command: {default_command}")
            return default_command

        except Exception as e:
            self.logger.error(f"Error detecting container command: {e}")
            default_command = "python server.py"
            self.logger.info(f"Using default command due to error: {default_command}")
            return default_command

    async def _connect_ssh_docker(self, config: Dict[str, Any]) -> None:
        """Connect via SSH to Docker container using MCP SDK."""
        ssh_host = config.get("ssh_host")
        ssh_user = config.get("ssh_user")
        container_name = config.get("container_name")
        container_command = config.get("container_command")

        if not all([ssh_host, ssh_user, container_name]):
            raise MCPConnectionError(
                "SSH Docker connection requires ssh_host, ssh_user, and container_name"
            )

        # Auto-detect container command if not provided
        if not container_command:
            self.logger.info("🔍 Auto-detecting container command...")
            container_command = await self._detect_container_command(
                ssh_host, ssh_user, container_name
            )
            self.logger.info(f"✅ Detected container command: {container_command}")

        # Get SSH key from global manager
        global_ssh_manager = get_global_ssh_manager()
        ssh_key_path = global_ssh_manager.get_ssh_key_path()

        if not ssh_key_path:
            raise MCPConnectionError("SSH key not available from global manager")

        # Build SSH command for Docker exec
        ssh_command = [
            "ssh",
            "-i",
            ssh_key_path,
            "-o",
            "StrictHostKeyChecking=no",
            "-o",
            "UserKnownHostsFile=/dev/null",
            "-o",
            "ConnectTimeout=30",
            "-o",
            "IdentitiesOnly=yes",
            f"{ssh_user}@{ssh_host}",
            f"docker exec -i {container_name} {container_command}",
        ]

        self.logger.info(f"🚀 SSH command: {' '.join(ssh_command)}")

        # Create asyncio subprocess
        self._process = await asyncio.create_subprocess_exec(
            *ssh_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            limit=settings.mcp_sdk_buffer_size,  # Use configurable buffer size
        )

        # Wait a moment for connection to establish
        await asyncio.sleep(2)

        # Check if process started successfully
        if self._process.returncode is not None:
            stderr_output = ""
            if self._process.stderr:
                stderr_data = await self._process.stderr.read()
                stderr_output = stderr_data.decode()
            raise MCPConnectionError(f"SSH process failed to start: {stderr_output}")

        # Create stream adapters that bridge asyncio streams to MCP SDK interface
        # The MCP SDK expects streams with specific methods like send() and recv()

        class MCPReadStreamAdapter:
            """Read stream adapter for MCP SDK."""

            def __init__(self, read_stream):
                self.read_stream = read_stream

            async def recv(self):
                """Receive data from the read stream."""
                line = await self.read_stream.readline()
                return line.decode("utf-8").strip()

            async def aclose(self):
                """Close the read stream."""
                pass  # Read stream cleanup handled elsewhere

        class MCPWriteStreamAdapter:
            """Write stream adapter for MCP SDK."""

            def __init__(self, write_stream):
                self.write_stream = write_stream

            async def send(self, data):
                """Send data through the write stream."""
                # Handle different data types that MCP SDK might send
                if hasattr(data, "message") and hasattr(
                    data.message, "model_dump_json"
                ):
                    # SessionMessage with JSONRPCMessage
                    json_data = data.message.model_dump_json()
                elif hasattr(data, "model_dump_json"):
                    # Direct MCP message objects
                    json_data = data.model_dump_json()
                elif hasattr(data, "json"):
                    json_data = data.json()
                elif isinstance(data, str):
                    json_data = data
                else:
                    # Fallback - convert to string
                    json_data = str(data)

                # Write the JSON data
                self.write_stream.write(json_data.encode("utf-8"))
                self.write_stream.write(b"\n")  # Add newline for JSON-RPC
                await self.write_stream.drain()

            async def aclose(self):
                """Close the write stream."""
                if hasattr(self.write_stream, "close"):
                    self.write_stream.close()
                    if hasattr(self.write_stream, "wait_closed"):
                        await self.write_stream.wait_closed()

        # Create separate read and write adapters
        read_adapter = MCPReadStreamAdapter(self._process.stdout)
        write_adapter = MCPWriteStreamAdapter(self._process.stdin)

        # Store the adapters and raw streams
        self._read_adapter = read_adapter
        self._write_adapter = write_adapter
        self._streams = (self._process.stdout, self._process.stdin)

        self.logger.info("✅ SSH Docker connection established")

    async def _connect_stdio(self, config: Dict[str, Any]) -> None:
        """Connect via stdio using MCP SDK."""
        command = config.get("command")
        if not command:
            raise MCPConnectionError("Stdio connection requires command")

        # Use MCP SDK's stdio_client
        server_params = StdioServerParameters(
            command=command[0] if isinstance(command, list) else command,
            args=command[1:] if isinstance(command, list) and len(command) > 1 else [],
            env=config.get("env", {}),
        )

        self._context = stdio_client(server_params)
        result = await self._context.__aenter__()

        if isinstance(result, tuple):
            self._streams = result
        else:
            self._streams = (result, result)

        self.logger.info("✅ Stdio connection established")

    async def _connect_http(self, config: Dict[str, Any]) -> None:
        """Connect via HTTP/SSE using MCP SDK."""
        server_url = config.get("server_url")
        headers = config.get("headers", {})
        connection_type = config.get("connection_type", "http")

        if not server_url:
            raise MCPConnectionError("HTTP connection requires server_url")

        # Auto-detect SSE from URL if not explicitly specified
        if connection_type == "http":
            if "/sse" in server_url.lower() or server_url.endswith("/sse"):
                connection_type = "sse"
                self.logger.info(
                    f"🔍 Auto-detected SSE connection from URL: {server_url}"
                )
            elif "/stream" in server_url.lower():
                connection_type = "streamable_http"
                self.logger.info(
                    f"🔍 Auto-detected streamable HTTP from URL: {server_url}"
                )

        # Use appropriate MCP SDK client
        if connection_type == "sse":
            self.logger.info(f"🌊 Using SSE client for: {server_url}")
            self._context = sse_client(server_url, headers=headers)
        elif connection_type == "streamable_http":
            self.logger.info(f"📡 Using streamable HTTP client for: {server_url}")
            self._context = streamablehttp_client(server_url, headers=headers)
        else:
            # For generic HTTP, prefer SSE for MCP servers
            self.logger.info(f"🔗 Using SSE client (default for MCP) for: {server_url}")
            self._context = sse_client(server_url, headers=headers)
            connection_type = "sse"

        result = await self._context.__aenter__()

        if isinstance(result, tuple):
            self._streams = result
        else:
            self._streams = (result, result)

        self.logger.info(f"✅ {connection_type.upper()} connection established")

    async def _connect_test_mode(self, config: Dict[str, Any]) -> None:
        """Connect in test mode - creates mock session for testing."""
        self.logger.info("🧪 Connecting in test mode (mock session)")

        # Create mock streams for testing
        class MockStream:
            async def read(self, n=-1):
                return b'{"jsonrpc": "2.0", "result": {"tools": []}}'

            async def write(self, data):
                pass

            async def drain(self):
                pass

            async def send(self, data):
                pass

            async def recv(self):
                return b'{"jsonrpc": "2.0", "result": {"tools": []}}'

        self._streams = (MockStream(), MockStream())

        # Create mock session
        class MockSession:
            async def __aenter__(self):
                return self

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

            async def initialize(self):
                pass

            async def list_tools(self):
                class MockResult:
                    tools = [
                        type(
                            "MockTool",
                            (),
                            {
                                "name": "test_tool",
                                "description": "A test tool for SDK testing",
                                "inputSchema": {"type": "object", "properties": {}},
                            },
                        )()
                    ]

                return MockResult()

            async def call_tool(self, tool_name, arguments):
                class MockResult:
                    isError = False
                    content = [
                        type(
                            "MockContent",
                            (),
                            {
                                "type": "text",
                                "text": f"Mock result from {tool_name} with args: {arguments}",
                            },
                        )()
                    ]

                return MockResult()

        self._session = MockSession()
        await self._session.__aenter__()
        await self._session.initialize()

        self.logger.info("✅ Test mode connection established")

    async def _initialize_session(self) -> None:
        """Initialize the MCP session using the SDK."""
        if not self._streams:
            raise MCPConnectionError("No streams available for session initialization")

        # For SSH Docker connections, we need to create a proper stream interface
        # The MCP SDK expects streams that work with its protocol
        try:
            # Check if we have stream adapters (for SSH Docker connections)
            if hasattr(self, "_read_adapter") and hasattr(self, "_write_adapter"):
                self.logger.info("🔗 Using stream adapters for MCP SDK session")
                # Use the adapters that provide proper send/recv methods
                self._session = ClientSession(self._read_adapter, self._write_adapter)
            else:
                # Use raw streams for other connection types
                # Handle variable number of streams safely
                if len(self._streams) >= 2:
                    read_stream, write_stream = self._streams[0], self._streams[1]
                elif len(self._streams) == 1:
                    # Single stream for both read and write
                    read_stream = write_stream = self._streams[0]
                else:
                    raise MCPConnectionError("No streams available for session")

                self.logger.debug(
                    f"Using raw streams: read={type(read_stream).__name__}, write={type(write_stream).__name__}"
                )
                self._session = ClientSession(read_stream, write_stream)

            await self._session.__aenter__()

            # Initialize with timeout and better error detection
            try:
                await asyncio.wait_for(
                    self._session.initialize(), timeout=settings.mcp_sdk_timeout
                )
                self.logger.info("✅ MCP SDK session initialized")

                # Test the session with a simple operation to ensure it's working
                try:
                    # Try to list tools as a connectivity test
                    await asyncio.wait_for(self._session.list_tools(), timeout=10)
                    self.logger.info("✅ MCP SDK session verified with tools list")
                except Exception as test_error:
                    self.logger.warning(f"Session test failed: {test_error}")
                    # Don't fail here, just log the warning

            except Exception as init_error:
                # Check for TaskGroup errors during initialization
                error_str = str(init_error).lower()
                if "taskgroup" in error_str or "unhandled errors" in error_str:
                    self.logger.error(
                        f"🚨 TaskGroup error during initialization: {init_error}"
                    )
                    # Re-raise to trigger our retry logic below
                    raise init_error
                else:
                    # Other initialization errors
                    raise init_error

        except asyncio.TimeoutError:
            raise MCPTimeoutError(
                f"Session initialization timed out after {settings.mcp_sdk_timeout}s"
            )
        except Exception as e:
            # If the direct approach fails, it might be a stream compatibility issue
            self.logger.error(f"Session initialization failed: {e}")

            # Check if this is a TaskGroup error (common with MCP SDK)
            error_str = str(e).lower()
            if "taskgroup" in error_str or "unhandled errors" in error_str:
                self.logger.error(
                    "🚨 TaskGroup error detected - this indicates MCP SDK compatibility issues"
                )

                # For SSH Docker with TaskGroup errors, try alternative command
                if hasattr(self, "_process") and self._process:
                    self.logger.info(
                        "🔄 Attempting retry with alternative container command"
                    )

                    # Kill the current process
                    try:
                        if self._process.returncode is None:
                            self._process.terminate()
                            await asyncio.wait_for(self._process.wait(), timeout=5.0)
                    except:
                        pass

                    # Try with alternative commands
                    alternative_commands = [
                        "node dist/index.js",
                        "npm start",
                        "node server.js",
                    ]
                    for alt_cmd in alternative_commands:
                        try:
                            self.logger.info(
                                f"🔄 Trying alternative command: {alt_cmd}"
                            )

                            # Rebuild SSH command with alternative
                            ssh_host = self._config.get("ssh_host")
                            ssh_user = self._config.get("ssh_user")
                            container_name = self._config.get("container_name")

                            global_ssh_manager = get_global_ssh_manager()
                            ssh_key_path = global_ssh_manager.get_ssh_key_path()

                            ssh_command = [
                                "ssh",
                                "-i",
                                ssh_key_path,
                                "-o",
                                "StrictHostKeyChecking=no",
                                "-o",
                                "UserKnownHostsFile=/dev/null",
                                "-o",
                                "ConnectTimeout=30",
                                "-o",
                                "IdentitiesOnly=yes",
                                f"{ssh_user}@{ssh_host}",
                                f"docker exec -i {container_name} {alt_cmd}",
                            ]

                            # Create new process
                            self._process = await asyncio.create_subprocess_exec(
                                *ssh_command,
                                stdin=asyncio.subprocess.PIPE,
                                stdout=asyncio.subprocess.PIPE,
                                stderr=asyncio.subprocess.PIPE,
                                limit=settings.mcp_sdk_buffer_size,
                            )

                            await asyncio.sleep(2)

                            if self._process.returncode is None:
                                # Recreate adapters (classes are defined in this file)
                                class MCPReadStreamAdapter:
                                    def __init__(self, read_stream):
                                        self.read_stream = read_stream

                                    async def recv(self):
                                        line = await self.read_stream.readline()
                                        return line.decode("utf-8").strip()

                                    async def aclose(self):
                                        pass

                                class MCPWriteStreamAdapter:
                                    def __init__(self, write_stream):
                                        self.write_stream = write_stream

                                    async def send(self, data):
                                        if hasattr(data, "message") and hasattr(
                                            data.message, "model_dump_json"
                                        ):
                                            json_data = data.message.model_dump_json()
                                        elif hasattr(data, "model_dump_json"):
                                            json_data = data.model_dump_json()
                                        elif isinstance(data, str):
                                            json_data = data
                                        else:
                                            json_data = str(data)
                                        self.write_stream.write(
                                            json_data.encode("utf-8")
                                        )
                                        self.write_stream.write(b"\n")
                                        await self.write_stream.drain()

                                    async def aclose(self):
                                        if hasattr(self.write_stream, "close"):
                                            self.write_stream.close()

                                self._read_adapter = MCPReadStreamAdapter(
                                    self._process.stdout
                                )
                                self._write_adapter = MCPWriteStreamAdapter(
                                    self._process.stdin
                                )

                                # Try session again
                                self._session = ClientSession(
                                    self._read_adapter, self._write_adapter
                                )
                                await self._session.__aenter__()
                                await asyncio.wait_for(
                                    self._session.initialize(),
                                    timeout=settings.mcp_sdk_timeout,
                                )

                                self.logger.info(
                                    f"✅ MCP SDK session initialized with alternative command: {alt_cmd}"
                                )
                                return

                        except Exception as alt_error:
                            self.logger.warning(
                                f"Alternative command {alt_cmd} failed: {alt_error}"
                            )
                            continue

                    # All alternatives failed
                    raise MCPConnectionError(
                        f"All alternative commands failed. Original error: {e}"
                    ) from e

            # For other errors, raise immediately
            raise MCPConnectionError(f"Failed to initialize MCP session: {e}") from e
