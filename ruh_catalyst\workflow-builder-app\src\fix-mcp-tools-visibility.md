# Fix for MCP Tools Component Visibility Issues

## Problem

The MCP Tools component is showing both the SSE URL and Stdio Command inputs simultaneously, even though they should be mutually exclusive based on the selected mode.

## Root Cause

After analyzing the code, the issue appears to be in how the frontend is handling the visibility rules for the MCPToolsComponent. The visibility rules are correctly defined in the backend, but they're not being properly applied in the frontend.

## Solution

### 1. Update the InspectorPanel.tsx file

Add explicit handling for the MCP Tools component's mode-dependent inputs in the `checkInputVisibility` function:

```typescript
// In the checkInputVisibility function in InspectorPanel.tsx
const checkInputVisibility = (inputDef: InputDefinition, config: Record<string, any>): boolean => {
  // Special handling for MCP Tools component
  if (selectedNode?.data.type === "MCPToolsComponent") {
    // For command and fetch_stdio_tools, only show when mode is Stdio
    if (inputDef.name === "command" || inputDef.name === "fetch_stdio_tools") {
      return config.mode === "Stdio";
    }
    
    // For sse_url and fetch_sse_tools, only show when mode is SSE
    if (inputDef.name === "sse_url" || inputDef.name === "fetch_sse_tools") {
      return config.mode === "SSE";
    }
    
    // For selected_tool_name, check connection_status
    if (inputDef.name === "selected_tool_name") {
      const connectionStatus = getConfigValue("connection_status", "Not Connected");
      return connectionStatus === "Connected";
    }
    
    // For refresh_tools and disconnect buttons, always hide them
    if (inputDef.name === "refresh_tools" || inputDef.name === "disconnect") {
      return false;
    }
  }
  
  // Use the utility function for standard visibility rules
  return shouldShowInput(inputDef, config);
};
```

### 2. Ensure the config object has the correct mode value

Make sure the config object passed to `checkInputVisibility` has the correct `mode` value:

```typescript
// When rendering inputs in InspectorPanel.tsx
{selectedNode.data.definition.inputs
  .map(inputDef => {
    // Make sure we have the latest mode value in the config
    const configWithLatestMode = {
      ...selectedNode.data.config,
      mode: getConfigValue("mode", "Stdio") // Ensure mode is up-to-date
    };
    
    // Check if this input should be visible based on rules
    const isVisible = checkInputVisibility(inputDef, configWithLatestMode);
    if (!isVisible) return null;
    
    // Render the input...
  })
}
```

### 3. Update the mode handling in the dropdown

Ensure that when the mode changes, the UI is immediately updated:

```typescript
// In the mode dropdown onChange handler
onValueChange={(value) => {
  console.log(`MCP Tools mode changed to: ${value}`);
  
  // Clear state when switching modes
  if (selectedNode) {
    clearMcpToolsState(selectedNode.id);
  }
  
  // Update the config immediately
  handleConfigChange(inputDef.name, value);
  
  // Force a re-render to update visibility
  setForceUpdate(prev => prev + 1);
}}
```

### 4. Add a forceUpdate state to trigger re-renders

Add a state variable to force re-renders when needed:

```typescript
// At the top of the InspectorPanel component
const [forceUpdate, setForceUpdate] = useState(0);

// Use forceUpdate in a useEffect to trigger re-renders
useEffect(() => {
  // This effect will run whenever forceUpdate changes
  console.log('Forced update triggered:', forceUpdate);
}, [forceUpdate]);
```

## Implementation Steps

1. Locate the `InspectorPanel.tsx` file in the frontend codebase
2. Update the `checkInputVisibility` function with the special handling for MCP Tools component
3. Ensure the config object has the correct mode value when checking visibility
4. Update the mode dropdown handler to force a re-render
5. Add the forceUpdate state variable to trigger re-renders

## Testing

After implementing these changes, test the MCP Tools component by:

1. Adding an MCP Tools node to the workflow
2. Opening the inspector panel
3. Switching between Stdio and SSE modes
4. Verifying that only the appropriate inputs are shown based on the selected mode
