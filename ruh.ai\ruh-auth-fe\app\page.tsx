"use client";

import { LoaderIcon } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { loginRoute } from "./shared/routes";
import { Suspense } from "react";
import { setRedirectUrl } from "@/services/helper";
import { LoadingScreen } from "@/components/shared/LoadingScreen";

function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get redirect URL from query params or use default
    const redirect_url = searchParams.get("redirect_url");
    if (redirect_url) {
      setRedirectUrl(redirect_url);
    }

    // Redirect to the login page
    router.push(loginRoute);
  }, [router, searchParams]);

  return <LoadingScreen />;
}
export default function Home() {
  return (
    <Suspense
      fallback={
        <div className="flex flex-col items-center justify-center h-screen">
          <LoaderIcon className="w-20 h-20 animate-spin text-brand-primary" />
        </div>
      }
    >
      <HomeContent />
    </Suspense>
  );
}
