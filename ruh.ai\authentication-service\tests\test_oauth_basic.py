"""
Basic OAuth Service Tests

Simple tests to verify OAuth service functionality without external dependencies.
"""

import pytest
import os
from unittest.mock import Mock, patch

# Set test environment before importing app modules
os.environ["SECRET_MANAGER_ENABLED"] = "false"
os.environ["GOOGLE_PROJECT_ID"] = "test-project"
os.environ["REDIS_HOST"] = "localhost"
os.environ["DB_HOST"] = "localhost"

from app.core.oauth_providers import OAuthProvider, oauth_provider_manager


class TestOAuthProviders:
    """Test OAuth provider management."""
    
    def test_oauth_provider_enum(self):
        """Test OAuth provider enumeration."""
        assert OAuthProvider.GOOGLE == "google"
        assert OAuthProvider.MICROSOFT == "microsoft"
        assert OAuthProvider.GITHUB == "github"
        assert OAuthProvider.CUSTOM == "custom"
    
    def test_provider_manager_initialization(self):
        """Test provider manager initialization."""
        # Test that provider manager has default providers
        providers = oauth_provider_manager.get_all_providers()
        assert OAuthProvider.GOOGLE in providers
        assert OAuthProvider.MICROSOFT in providers
        assert OAuthProvider.GITHUB in providers
    
    def test_get_tool_scopes(self):
        """Test getting tool scopes."""
        # Test Google Calendar scopes
        scopes = oauth_provider_manager.get_tool_scopes("google_calendar", OAuthProvider.GOOGLE)
        assert len(scopes) > 0
        assert any("calendar" in scope for scope in scopes)
        
        # Test non-existent tool
        scopes = oauth_provider_manager.get_tool_scopes("nonexistent_tool", OAuthProvider.GOOGLE)
        assert len(scopes) == 0
    
    def test_provider_configuration(self):
        """Test provider configuration."""
        # Test getting provider config
        config = oauth_provider_manager.get_provider_config(OAuthProvider.GOOGLE)
        assert config is not None
        assert config.provider == OAuthProvider.GOOGLE
        assert "google.com" in config.auth_url
    
    def test_update_provider_credentials(self):
        """Test updating provider credentials."""
        oauth_provider_manager.update_provider_credentials(
            OAuthProvider.GOOGLE,
            "test_client_id",
            "test_client_secret",
            "http://localhost:8000/callback"
        )
        
        config = oauth_provider_manager.get_provider_config(OAuthProvider.GOOGLE)
        assert config.client_id == "test_client_id"
        assert config.client_secret == "test_client_secret"
        assert config.redirect_uri == "http://localhost:8000/callback"
    
    def test_is_provider_configured(self):
        """Test checking if provider is configured."""
        # Initially not configured (empty credentials)
        assert not oauth_provider_manager.is_provider_configured(OAuthProvider.GOOGLE)
        
        # Configure provider
        oauth_provider_manager.update_provider_credentials(
            OAuthProvider.GOOGLE,
            "test_client_id",
            "test_client_secret",
            "http://localhost:8000/callback"
        )
        
        # Now should be configured
        assert oauth_provider_manager.is_provider_configured(OAuthProvider.GOOGLE)


class TestOAuthCredentialModel:
    """Test OAuth credential model."""
    
    def test_generate_composite_key(self):
        """Test composite key generation."""
        from app.models.oauth import OAuthCredential
        
        key = OAuthCredential.generate_composite_key(
            "user123", "mcp456", "google_calendar", "google"
        )
        
        assert key == "user123_mcp456_google_calendar_google"
    
    def test_composite_key_with_default_provider(self):
        """Test composite key with default provider."""
        from app.models.oauth import OAuthCredential
        
        key = OAuthCredential.generate_composite_key(
            "user123", "mcp456", "google_calendar"
        )
        
        assert key == "user123_mcp456_google_calendar_google"


@patch('app.utils.redis_service.redis_service')
@patch('app.utils.secret_manager.secret_manager')
class TestOAuthServiceMocked:
    """Test OAuth service with mocked dependencies."""
    
    def test_oauth_service_initialization(self, mock_secret_manager, mock_redis_service):
        """Test OAuth service initialization."""
        # Mock the dependencies to avoid actual connections
        mock_secret_manager.health_check.return_value = True
        mock_redis_service.health_check.return_value = True
        
        # Import after mocking
        from app.services.oauth_service import OAuthService
        
        service = OAuthService()
        assert service is not None
        assert service.secret_manager is not None
        assert service.redis_service is not None


class TestOAuthSchemas:
    """Test OAuth Pydantic schemas."""
    
    def test_oauth_authorize_request(self):
        """Test OAuth authorize request schema."""
        from app.schemas.oauth import OAuthAuthorizeRequest
        
        request = OAuthAuthorizeRequest(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider=OAuthProvider.GOOGLE
        )
        
        assert request.user_id == "test_user"
        assert request.mcp_id == "test_mcp"
        assert request.tool_name == "google_calendar"
        assert request.provider == OAuthProvider.GOOGLE
    
    def test_oauth_credential_response(self):
        """Test OAuth credential response schema."""
        from app.schemas.oauth import OAuthCredentialResponse
        
        response = OAuthCredentialResponse(
            success=True,
            message="Success",
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider="google",
            access_token="test_token",
            token_type="Bearer",
            expires_in=3600,
            scope="test_scope"
        )
        
        assert response.success is True
        assert response.access_token == "test_token"
        assert response.token_type == "Bearer"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
