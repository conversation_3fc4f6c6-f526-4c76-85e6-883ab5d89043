import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { ComponentsApiResponse, ComponentDefinition } from "@/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  LogIn,
  Grip,
  Brain,
  Cloud,
  Store,
  Bell,
  MessageCircle,
  Share2,
  CloudUpload,
  Settings,
} from "lucide-react";

interface SidebarProps {
  components: ComponentsApiResponse;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

// Helper function to make a node draggable
const onDragStart = (event: React.DragEvent, nodeType: string, definition: ComponentDefinition) => {
  const nodeData = JSON.stringify({ nodeType, definition });
  event.dataTransfer.setData("application/reactflow", nodeData);
  event.dataTransfer.effectAllowed = "move";
};

// Helper function to get appropriate icon based on category
const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return LogIn;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    case "ai":
      return Brain; // AI components use Brain icon
    case "mcp":
      return Cloud; // MCP components use Cloud icon
    case "marketplace":
    case "mcp marketplace":
      return Store; // Marketplace components use Store icon
    case "notifications alerts":
      return Bell; // Notifications & Alerts components use Bell icon
    case "communication":
      return MessageCircle; // Communication components use MessageCircle icon
    case "social media":
      return Share2; // Social Media components use Share2 icon
    case "database":
      return Database; // Database components use Database icon
    case "cloud storage":
      return CloudUpload; // Cloud Storage components use CloudUpload icon
    case "devops system":
      return Settings; // DevOps System components use Settings icon
    case "file handling":
      return FileText; // File Handling components use FileText icon
    default:
      return Cog;
  }
};

// Helper function to get user-friendly category display name
const getCategoryDisplayName = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return "Input/Output";
    case "ai":
      return "AI/LLM";
    case "mcp":
      return "MCP Marketplace";
    case "mcp marketplace":
      return "MCP Marketplace";
    case "notifications alerts":
      return "Notifications & Alerts";
    case "communication":
      return "Communication";
    case "social media":
      return "Social Media";
    case "database":
      return "Database";
    case "cloud storage":
      return "Cloud Storage";
    case "devops system":
      return "DevOps System";
    case "file handling":
      return "File Handling";
    default:
      return category.charAt(0).toUpperCase() + category.slice(1);
  }
};

export const Sidebar = React.memo(function Sidebar({
  components,
  collapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  // Remove all console.log statements to prevent unnecessary work during renders

  console.log("Sidebar component categories:", Object.keys(components));

  // Check if MCP category exists and log its contents
  if (components.MCP) {
    console.log("MCP category exists with components:", Object.keys(components.MCP));
  } else {
    console.log("MCP category does not exist in components");
  }
  const categories = Object.keys(components).sort();

  const [searchTerm, setSearchTerm] = useState("");
  // Initialize with IO, AI, Data, and MCP categories expanded by default
  const [expandedCategories, setExpandedCategories] = useState<string[]>(
    categories.filter((cat) =>
      ["io", "ai", "data", "mcp", "mcp marketplace"].includes(cat.toLowerCase()),
    ),
  );

  // State to track expanded MCP server groups
  const [expandedMCPGroups, setExpandedMCPGroups] = useState<Record<string, string[]>>({});

  // Filter components based on search term
  const filteredComponents = useCallback(() => {
    if (!searchTerm.trim()) {
      return components;
    }

    const filtered: ComponentsApiResponse = {};
    const searchLower = searchTerm.toLowerCase();

    Object.entries(components).forEach(([category, categoryComponents]) => {
      const matchingComponents = Object.values(categoryComponents).filter(
        (comp) =>
          comp.display_name.toLowerCase().includes(searchLower) ||
          comp.description.toLowerCase().includes(searchLower),
      );

      if (matchingComponents.length > 0) {
        filtered[category] = Object.fromEntries(
          matchingComponents.map((comp) => [comp.name, comp]),
        );
      }
    });

    return filtered;
  }, [components, searchTerm]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Auto-expand all categories when searching
    if (value.trim()) {
      const filteredCats = Object.keys(filteredComponents());
      setExpandedCategories(filteredCats);

      // Also expand all MCP groups in the filtered categories
      const newExpandedGroups: Record<string, string[]> = {};
      filteredCats.forEach((category) => {
        const categoryComponents = filteredComponents()[category];
        if (categoryComponents) {
          // Find all MCP groups in this category
          const mcpGroups = new Set<string>();
          Object.values(categoryComponents).forEach((comp) => {
            if (comp.type === "MCP" && comp.display_name.includes(" - ")) {
              const mcpName = comp.display_name.split(" - ")[0];
              mcpGroups.add(mcpName);
            }
          });

          if (mcpGroups.size > 0) {
            newExpandedGroups[category] = Array.from(mcpGroups);
          }
        }
      });

      setExpandedMCPGroups(newExpandedGroups);
    }
  };

  // Toggle category expansion
  const handleCategoryToggle = (category: string) => {
    setExpandedCategories((prev) =>
      prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category],
    );
  };

  // Toggle MCP group expansion
  const handleMCPGroupToggle = (category: string, mcpName: string) => {
    setExpandedMCPGroups((prev) => {
      const categoryGroups = prev[category] || [];
      const newCategoryGroups = categoryGroups.includes(mcpName)
        ? categoryGroups.filter((name) => name !== mcpName)
        : [...categoryGroups, mcpName];

      return {
        ...prev,
        [category]: newCategoryGroups,
      };
    });
  };

  // Memoize the filtered data to prevent recalculation on every render
  const filteredData = useMemo(() => filteredComponents(), [filteredComponents]);

  // Memoize the visible categories to prevent recalculation on every render
  const visibleCategories = useMemo(() => {
    // Filter out the "io" category and then sort the remaining categories
    return Object.keys(filteredData)
      .filter((category) => category.toLowerCase() !== "io") // Comment out IO category
      .sort((a, b) => {
        // Priority order: AI first, then MCP Marketplace, then Data, then new MCP categories, then alphabetical
        // IO category is filtered out
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();

        if (aLower === "ai") return -1;
        if (bLower === "ai") return 1;
        if (aLower === "mcp marketplace") return -1;
        if (bLower === "mcp marketplace") return 1;
        if (aLower === "mcp") return -1;
        if (bLower === "mcp") return 1;
        if (aLower === "data") return -1;
        if (bLower === "data") return 1;

        // Group MCP categories together after the priority categories
        const mcpCategories = [
          "notifications alerts",
          "communication",
          "social media",
          "database",
          "cloud storage",
          "devops system",
          "file handling",
        ];
        const aIsMcp = mcpCategories.includes(aLower);
        const bIsMcp = mcpCategories.includes(bLower);

        if (aIsMcp && !bIsMcp) return -1;
        if (!aIsMcp && bIsMcp) return 1;
        if (aIsMcp && bIsMcp) {
          // Sort MCP categories in a specific order
          return mcpCategories.indexOf(aLower) - mcpCategories.indexOf(bLower);
        }

        return a.localeCompare(b);
      });
  }, [filteredData]);

  return (
    <aside
      className={`bg-sidebar border-brand-stroke relative flex h-full shrink-0 flex-col overflow-hidden border-r shadow-md transition-all duration-300 ${
        collapsed ? "w-16" : "w-80"
      }`}
    >
      {/* Theme-responsive overlay */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-black/5 dark:bg-black/20"></div>

      {/* Removed the border button */}

      <div
        className={`relative z-10 flex-shrink-0 bg-[#FEFEFE] dark:bg-black ${collapsed ? "p-3" : "p-5"}`}
      >
        {!collapsed && (
          <div className="relative flex items-center">
            <div className="relative flex-grow">
              <Search className="text-brand-secondary absolute top-3 left-3 h-5 w-5" />
              <Input
                placeholder="Search components..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="border-brand-stroke text-brand-primary-font placeholder:text-brand-secondary-font focus-visible:ring-brand-primary/30 dark:text-brand-white-text dark:placeholder:text-brand-secondary-font h-11 rounded-md bg-white/90 pl-10 text-sm dark:border-[#3F3F46] dark:bg-[#18181B]"
              />
            </div>
            <button
              onClick={onToggleCollapse}
              className="text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary ml-2 flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]"
              aria-label="Collapse sidebar"
              title="Collapse sidebar (Alt+S)"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15 6L9 12L15 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        )}
        {collapsed && (
          <div className="flex items-center justify-center">
            <button
              onClick={onToggleCollapse}
              className="text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]"
              aria-label="Expand sidebar"
              title="Expand sidebar (Alt+S)"
            >
              {/* Hamburger menu icon */}
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3 12H21M3 6H21M3 18H21"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      <CustomScrollArea className="custom-scrollbar relative z-10 flex-grow bg-[#FEFEFE] dark:bg-black">
        {!collapsed ? (
          <>
            <Accordion
              type="multiple"
              className="w-full space-y-3 px-5 py-4"
              value={expandedCategories}
            >
              {visibleCategories.map((category) => {
                const CategoryIcon = getCategoryIcon(category);
                return (
                  <AccordionItem
                    value={category}
                    key={category}
                    className="overflow-hidden rounded-lg bg-white shadow-md hover:bg-[#F9F9F9] dark:bg-[#1E1E1E] dark:hover:bg-[#212121]"
                  >
                    <AccordionTrigger
                      className="font-primary px-4 py-4 text-base font-semibold transition-all duration-200 hover:bg-[#F9F9F9] hover:no-underline dark:hover:bg-[#212121]"
                      onClick={() => handleCategoryToggle(category)}
                    >
                      <div className="flex w-full items-center gap-2">
                        <CategoryIcon className="text-brand-primary dark:text-brand-secondary h-6 w-6" />
                        <span className="text-brand-primary-font dark:text-brand-white-text">
                          {getCategoryDisplayName(category)}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="accordion-content-animation px-3 pb-4">
                      <div className="max-h-[300px] space-y-4 overflow-y-auto pt-3 pr-2">
                        {(() => {
                          // Group components by MCP server name
                          const groupedByMCP: Record<string, ComponentDefinition[]> = {};

                          // Get all components for this category
                          const components = Object.values(filteredData[category]).sort((a, b) =>
                            a.display_name.localeCompare(b.display_name),
                          );

                          // Group components by MCP server name (first part of display_name before " - ")
                          components.forEach((compDef) => {
                            // Check if this is an MCP component (has display_name in format "MCPName - ToolName")
                            const isMCPComponent =
                              compDef.type === "MCP" && compDef.display_name.includes(" - ");

                            if (isMCPComponent) {
                              // Extract MCP name from display_name (format: "MCPName - ToolName")
                              const mcpName = compDef.display_name.split(" - ")[0];
                              if (!groupedByMCP[mcpName]) {
                                groupedByMCP[mcpName] = [];
                              }
                              groupedByMCP[mcpName].push(compDef);
                            } else {
                              // For non-MCP components, use a special key
                              if (!groupedByMCP["__other"]) {
                                groupedByMCP["__other"] = [];
                              }
                              groupedByMCP["__other"].push(compDef);
                            }
                          });

                          // Initialize expanded MCP groups for this category if not already set
                          if (searchTerm.trim() && !expandedMCPGroups[category]) {
                            // Auto-expand all MCP groups when searching
                            const mcpNames = Object.keys(groupedByMCP).filter(
                              (name) => name !== "__other",
                            );
                            if (mcpNames.length > 0) {
                              setExpandedMCPGroups((prev) => ({
                                ...prev,
                                [category]: mcpNames,
                              }));
                            }
                          }

                          // Get the list of expanded MCP groups for this category
                          const expandedGroups = expandedMCPGroups[category] || [];

                          return (
                            <>
                              {/* Render MCP groups first */}
                              {Object.entries(groupedByMCP)
                                .filter(([mcpName]) => mcpName !== "__other")
                                .map(([mcpName, mcpComponents]) => (
                                  <div key={mcpName} className="mb-4">
                                    {/* MCP Server Name as collapsible header */}
                                    <div
                                      className="text-brand-primary-font dark:text-brand-white-text mb-2 flex cursor-pointer items-center justify-between border-b border-gray-200 pb-1 text-sm font-medium dark:border-gray-700"
                                      onClick={() => handleMCPGroupToggle(category, mcpName)}
                                    >
                                      <span>{mcpName}</span>
                                      <svg
                                        width="12"
                                        height="12"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                        className={`transition-transform duration-200 ${
                                          expandedGroups.includes(mcpName) ? "rotate-180" : ""
                                        }`}
                                      >
                                        <path
                                          d="M6 9l6 6 6-6"
                                          stroke="currentColor"
                                          strokeWidth="2"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                        />
                                      </svg>
                                    </div>

                                    {/* Components from this MCP - only show if expanded */}
                                    {expandedGroups.includes(mcpName) && (
                                      <div className="space-y-3 pl-2">
                                        {mcpComponents.map((compDef: ComponentDefinition) => (
                                          <div
                                            key={compDef.name}
                                            className="group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]"
                                            onDragStart={(event) =>
                                              onDragStart(event, compDef.name, compDef)
                                            }
                                            draggable
                                          >
                                            <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                              <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                                            </div>
                                            <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                              <span className="text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80">
                                                {/* Show only the tool name, not the full "MCPName - ToolName" */}
                                                {compDef.display_name.split(" - ")[1] ||
                                                  compDef.display_name}
                                              </span>
                                              {compDef.type === "MCPMarketplaceComponent" && (
                                                <Badge className="ml-1 bg-blue-500 text-[10px] dark:bg-blue-600">
                                                  MCP
                                                </Badge>
                                              )}
                                            </div>
                                            <div className="mb-2 pt-2"></div>
                                            <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                              {compDef.description}
                                            </p>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ))}

                              {/* Render other components */}
                              {groupedByMCP["__other"] && groupedByMCP["__other"].length > 0 && (
                                <div className="space-y-3">
                                  {groupedByMCP["__other"].map((compDef: ComponentDefinition) => (
                                    <div
                                      key={compDef.name}
                                      className="group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]"
                                      onDragStart={(event) =>
                                        onDragStart(event, compDef.name, compDef)
                                      }
                                      draggable
                                    >
                                      <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                        <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                                      </div>
                                      <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                        <span className="text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80">
                                          {compDef.display_name}
                                        </span>
                                        {compDef.type === "MCPMarketplaceComponent" && (
                                          <Badge className="ml-1 bg-blue-500 text-[10px] dark:bg-blue-600">
                                            MCP
                                          </Badge>
                                        )}
                                      </div>
                                      <div className="mb-2 pt-2"></div>
                                      <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                        {compDef.description}
                                      </p>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>

            {visibleCategories.length === 0 && (
              <div className="border-brand-stroke bg-brand-card-hover text-brand-secondary-font dark:border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font m-4 rounded-lg border p-6 text-center text-sm">
                <Search className="text-brand-primary/70 dark:text-brand-secondary/70 mx-auto mb-3 h-6 w-6" />
                No components match your search.
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center gap-3 px-1 py-6">
            {visibleCategories.map((category) => {
              const CategoryIcon = getCategoryIcon(category);
              return (
                <div
                  key={category}
                  className="group mb-1 flex flex-col items-center"
                  title={getCategoryDisplayName(category)}
                >
                  <button
                    className={`flex h-10 w-10 items-center justify-center rounded-full shadow-sm ${
                      category.toLowerCase() === "io" || category.toLowerCase() === "data"
                        ? "bg-brand-primary/10 text-brand-primary dark:bg-brand-primary/20 dark:text-brand-secondary"
                        : "bg-brand-card-hover text-brand-primary-font dark:bg-brand-card dark:text-brand-white-text"
                    } transition-all hover:scale-110 hover:shadow-md`}
                    onClick={() => {
                      if (onToggleCollapse) onToggleCollapse();
                      setTimeout(() => handleCategoryToggle(category), 300);
                    }}
                    aria-label={`Open ${getCategoryDisplayName(category)} category`}
                  >
                    <CategoryIcon className="h-5 w-5" />
                  </button>
                  <span className="text-brand-secondary-font mt-1 text-[10px] font-medium opacity-80">
                    {getCategoryDisplayName(category).substring(0, 3)}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </CustomScrollArea>
    </aside>
  );
});
