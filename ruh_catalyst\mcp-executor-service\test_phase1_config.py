#!/usr/bin/env python3
"""
Test script to verify Phase 1: MCP SDK Dependencies and Configuration.
"""

import os
import sys
import logging

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_mcp_sdk_import():
    """Test if MCP SDK can be imported."""
    try:
        import mcp
        print(f"✅ MCP SDK imported successfully - Version: {mcp.__version__ if hasattr(mcp, '__version__') else 'Unknown'}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import MCP SDK: {e}")
        return False

def test_configuration():
    """Test the new MCP SDK configuration."""
    try:
        from app.config.config import settings
        
        print("✅ Configuration loaded successfully:")
        print(f"  - USE_MCP_SDK: {settings.use_mcp_sdk}")
        print(f"  - MCP_SDK_TIMEOUT: {settings.mcp_sdk_timeout}")
        print(f"  - MCP_SDK_BUFFER_SIZE: {settings.mcp_sdk_buffer_size}")
        
        # Test environment variable detection
        env = os.getenv("ENV", "dev")
        print(f"  - Current ENV: {env}")
        print(f"  - Expected USE_MCP_SDK for {env}: {env.lower() == 'dev'}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False

def test_environment_switching():
    """Test environment-based SDK switching."""
    print("\n🧪 Testing environment-based SDK switching:")
    
    success = True
    
    # Test dev environment
    os.environ["ENV"] = "dev"
    os.environ.pop("USE_MCP_SDK", None)  # Remove explicit override
    try:
        from app.config.config import refresh_settings, settings
        refresh_settings()
        expected = True
        actual = settings.use_mcp_sdk
        print(f"  - ENV=dev -> USE_MCP_SDK: {actual} (expected: {expected})")
        if actual != expected:
            success = False
    except Exception as e:
        print(f"  - ENV=dev test failed: {e}")
        success = False
    
    # Test prod environment
    os.environ["ENV"] = "prod"
    os.environ.pop("USE_MCP_SDK", None)  # Remove explicit override
    try:
        refresh_settings()
        expected = False
        actual = settings.use_mcp_sdk
        print(f"  - ENV=prod -> USE_MCP_SDK: {actual} (expected: {expected})")
        if actual != expected:
            success = False
    except Exception as e:
        print(f"  - ENV=prod test failed: {e}")
        success = False
    
    # Test explicit override
    os.environ["USE_MCP_SDK"] = "false"
    try:
        refresh_settings()
        expected = False
        actual = settings.use_mcp_sdk
        print(f"  - USE_MCP_SDK=false -> USE_MCP_SDK: {actual} (expected: {expected})")
        if actual != expected:
            success = False
    except Exception as e:
        print(f"  - USE_MCP_SDK override test failed: {e}")
        success = False
    
    # Reset environment
    os.environ.pop("USE_MCP_SDK", None)
    os.environ["ENV"] = "dev"
    refresh_settings()
    
    return success

def test_pyproject_dependencies():
    """Test that MCP SDK is in pyproject.toml."""
    try:
        with open("pyproject.toml", "r") as f:
            content = f.read()
            if 'mcp = "^1.9.2"' in content:
                print("✅ MCP SDK dependency found in pyproject.toml")
                return True
            else:
                print("❌ MCP SDK dependency not found in pyproject.toml")
                return False
    except Exception as e:
        print(f"❌ Failed to read pyproject.toml: {e}")
        return False

def main():
    """Run all Phase 1 tests."""
    print("🚀 Testing Phase 1: MCP SDK Dependencies and Configuration")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
    
    tests = [
        ("MCP SDK Import", test_mcp_sdk_import),
        ("Configuration Loading", test_configuration),
        ("Environment Switching", test_environment_switching),
        ("PyProject Dependencies", test_pyproject_dependencies),
    ]
    
    results = []
    
    # Run tests
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Phase 1 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Phase 1 complete! MCP SDK configuration is ready.")
        print("\n🔄 Ready for Phase 2: Create SDK Abstraction Layer")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
