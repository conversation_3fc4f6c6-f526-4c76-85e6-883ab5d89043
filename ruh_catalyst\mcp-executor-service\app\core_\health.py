#!/usr/bin/env python3
"""
Health check module for MCP Executor Service.

This module provides health check functionality for monitoring service health,
dependencies, and system status.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

import aiohttp

try:
    from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
    from aiokafka.errors import KafkaError

    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False
    KafkaError = Exception

from app.config.config import settings


class HealthStatus(Enum):
    """Health status enumeration."""

    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Health check result data structure."""

    component: str
    status: str
    message: str
    duration_ms: float
    timestamp: str
    details: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()


@dataclass
class SystemHealth:
    """Overall system health data structure."""

    status: str
    timestamp: str
    uptime_seconds: float
    checks: List[HealthCheckResult]
    summary: Dict[str, int]

    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()


class HealthChecker:
    """Health checker for MCP Executor Service."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.start_time = time.time()
        self._kafka_producer = None
        self._kafka_consumer = None

    async def check_kafka_connectivity(self) -> HealthCheckResult:
        """Check Kafka broker connectivity."""
        start_time = time.time()

        if not KAFKA_AVAILABLE:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="kafka",
                status=HealthStatus.UNHEALTHY.value,
                message="Kafka client not available",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": "aiokafka not installed"},
            )

        try:
            # Test producer connection
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                request_timeout_ms=5000,
            )

            await producer.start()

            # Get cluster metadata to verify connection
            metadata = await producer.client.cluster.metadata()
            topics_count = len(metadata.topics) if metadata and metadata.topics else 0

            await producer.stop()

            duration_ms = (time.time() - start_time) * 1000

            return HealthCheckResult(
                component="kafka",
                status=HealthStatus.HEALTHY.value,
                message="Kafka connectivity successful",
                duration_ms=duration_ms,
                timestamp="",
                details={
                    "bootstrap_servers": settings.kafka_bootstrap_servers,
                    "topics_available": topics_count,
                    "consumer_topic": getattr(
                        settings, "kafka_consumer_topic", "unknown"
                    ),
                    "results_topic": getattr(
                        settings, "kafka_results_topic", "unknown"
                    ),
                },
            )

        except KafkaError as e:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="kafka",
                status=HealthStatus.UNHEALTHY.value,
                message=f"Kafka connectivity failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": str(e)},
            )
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="kafka",
                status=HealthStatus.UNHEALTHY.value,
                message=f"Kafka health check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": str(e)},
            )

    async def check_container_api_connectivity(self) -> HealthCheckResult:
        """Check Container API connectivity."""
        start_time = time.time()

        try:
            # Test connection to container API
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Try to reach the container API health endpoint
                container_api_url = f"{settings.api_base_url}/health"

                async with session.get(container_api_url) as response:
                    duration_ms = (time.time() - start_time) * 1000

                    if response.status == 200:
                        return HealthCheckResult(
                            component="container_api",
                            status=HealthStatus.HEALTHY.value,
                            message="Container API connectivity successful",
                            duration_ms=duration_ms,
                            timestamp="",
                            details={
                                "api_base_url": settings.api_base_url,
                                "response_status": response.status,
                            },
                        )
                    else:
                        return HealthCheckResult(
                            component="container_api",
                            status=HealthStatus.DEGRADED.value,
                            message=f"Container API returned status {response.status}",
                            duration_ms=duration_ms,
                            timestamp="",
                            details={
                                "api_base_url": settings.api_base_url,
                                "response_status": response.status,
                            },
                        )

        except asyncio.TimeoutError:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="container_api",
                status=HealthStatus.UNHEALTHY.value,
                message="Container API connection timeout",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": "timeout", "api_base_url": settings.api_base_url},
            )
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="container_api",
                status=HealthStatus.UNHEALTHY.value,
                message=f"Container API connectivity failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": str(e), "api_base_url": settings.api_base_url},
            )

    async def check_mcp_config_service_connectivity(self) -> HealthCheckResult:
        """Check MCP Config Service connectivity."""
        start_time = time.time()

        try:
            # Test connection to MCP config service
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Try to reach the MCP config service health endpoint
                config_service_url = f"{settings.api_base_url}/api/v1/health"

                async with session.get(config_service_url) as response:
                    duration_ms = (time.time() - start_time) * 1000

                    if response.status == 200:
                        return HealthCheckResult(
                            component="mcp_config_service",
                            status=HealthStatus.HEALTHY.value,
                            message="MCP Config Service connectivity successful",
                            duration_ms=duration_ms,
                            timestamp="",
                            details={
                                "api_base_url": settings.api_base_url,
                                "response_status": response.status,
                            },
                        )
                    else:
                        return HealthCheckResult(
                            component="mcp_config_service",
                            status=HealthStatus.DEGRADED.value,
                            message=f"MCP Config Service returned status {response.status}",
                            duration_ms=duration_ms,
                            timestamp="",
                            details={
                                "api_base_url": settings.api_base_url,
                                "response_status": response.status,
                            },
                        )

        except asyncio.TimeoutError:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="mcp_config_service",
                status=HealthStatus.UNHEALTHY.value,
                message="MCP Config Service connection timeout",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": "timeout", "api_base_url": settings.api_base_url},
            )
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="mcp_config_service",
                status=HealthStatus.UNHEALTHY.value,
                message=f"MCP Config Service connectivity failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": str(e), "api_base_url": settings.api_base_url},
            )

    def check_service_health(self) -> HealthCheckResult:
        """Check basic service health."""
        start_time = time.time()

        try:
            uptime = time.time() - self.start_time
            duration_ms = (time.time() - start_time) * 1000

            return HealthCheckResult(
                component="service",
                status=HealthStatus.HEALTHY.value,
                message="Service is running",
                duration_ms=duration_ms,
                timestamp="",
                details={
                    "uptime_seconds": uptime,
                    "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
                    "log_level": settings.log_level,
                    "max_concurrent_tasks": settings.max_concurrent_tasks,
                },
            )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="service",
                status=HealthStatus.UNHEALTHY.value,
                message=f"Service health check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp="",
                details={"error": str(e)},
            )

    async def get_system_health(self) -> SystemHealth:
        """Get overall system health."""
        checks = []

        # Run all health checks
        checks.append(self.check_service_health())
        checks.append(await self.check_kafka_connectivity())
        checks.append(await self.check_container_api_connectivity())
        checks.append(await self.check_mcp_config_service_connectivity())

        # Calculate overall status
        statuses = [check.status for check in checks]
        if all(status == HealthStatus.HEALTHY.value for status in statuses):
            overall_status = HealthStatus.HEALTHY.value
        elif any(status == HealthStatus.UNHEALTHY.value for status in statuses):
            overall_status = HealthStatus.UNHEALTHY.value
        else:
            overall_status = HealthStatus.DEGRADED.value

        # Create summary
        summary = {
            "healthy": sum(1 for s in statuses if s == HealthStatus.HEALTHY.value),
            "unhealthy": sum(1 for s in statuses if s == HealthStatus.UNHEALTHY.value),
            "degraded": sum(1 for s in statuses if s == HealthStatus.DEGRADED.value),
            "total": len(statuses),
        }

        uptime = time.time() - self.start_time

        return SystemHealth(
            status=overall_status,
            timestamp="",
            uptime_seconds=uptime,
            checks=checks,
            summary=summary,
        )


# Global health checker instance
health_checker = HealthChecker()


async def get_health_status() -> Dict[str, Any]:
    """Get system health status as dictionary."""
    system_health = await health_checker.get_system_health()
    return asdict(system_health)
