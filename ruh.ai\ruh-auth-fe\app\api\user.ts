import api from "@/services/axios";
import {
  UserInfo,
  UserProfileUpdateRequest,
  ProfileUpdateResponse,
} from "@/app/shared/interfaces";

export const userApi = {
  /**
   * Get current user information
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getCurrentUser: async (): Promise<UserInfo> => {
    try {
      const response = await api.get<UserInfo>("/users/me");
      return response.data;
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 404:
          throw new Error("User not found");
        case 500:
          throw new Error("Internal server error");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to fetch user information"
          );
      }
    }
  },

  /**
   * Update current user profile details (company, department, job role)
   * @description Requires valid JWT Bearer token in Authorization header
   */
  updateUserProfile: async (
    data: UserProfileUpdateRequest
  ): Promise<ProfileUpdateResponse> => {
    try {
      const response = await api.put<ProfileUpdateResponse>(
        "/users/profile-details",
        data
      );
      return response.data;
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 422:
          const details = error.response?.data?.detail;
          // Base validation message that will be enhanced with specific field errors
          // if they exist in the API response. The final message will be in the format:
          // "Validation failed. fieldName1: errorMsg1, fieldName2: errorMsg2"
          let validationMsg = "Validation failed.";
          if (Array.isArray(details)) {
            validationMsg +=
              " " +
              details.map((d: any) => `${d.loc?.[1]}: ${d.msg}`).join(", ");
          }
          throw new Error(validationMsg);
        case 500:
          throw new Error("Internal server error during profile update");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to update user profile"
          );
      }
    }
  },
};
