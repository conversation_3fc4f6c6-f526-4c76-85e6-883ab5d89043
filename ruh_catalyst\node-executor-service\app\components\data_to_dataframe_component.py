"""
Data to DataFrame Component - Converts data to a Pandas DataFrame.
"""
import logging
import traceback
from typing import Dict, Any, List, Optional, Union, ClassVar
from pydantic import BaseModel, Field, field_validator

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("DataToDataFrameComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class DataToDataFrameRequest(BaseModel):
    """
    Schema for data to DataFrame conversion requests.
    """
    input_data: Any = Field(..., description="The data to convert to a DataFrame")
    orientation: str = Field(
        "auto-detect",
        description="The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'"
    )

    @field_validator('orientation')
    def validate_orientation(cls, v):
        """Validate the orientation field."""
        valid_orientations = ["records", "columns", "auto-detect"]
        if v.lower() not in valid_orientations:
            raise ValueError(f"Orientation must be one of {valid_orientations}")
        return v.lower()


@register_component("DataToDataFrameComponent")
class DataToDataFrameComponent(BaseComponent):
    """
    Component for converting data to a Pandas DataFrame.

    This component takes input data (list of dictionaries or dictionary of lists)
    and converts it to a Pandas DataFrame.
    """

    def __init__(self):
        """
        Initialize the DataToDataFrameComponent.
        """
        logger.info("Initializing Data to DataFrame Component")
        super().__init__()

        # Set the request schema for automatic validation
        self.request_schema = DataToDataFrameRequest

        logger.info("Data to DataFrame Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a data to DataFrame conversion payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult: The result of the validation
        """
        logger.info("Validating Data to DataFrame payload")

        # Use the parent class validation which uses the request schema
        validation_result = await super().validate(payload)

        if not validation_result.is_valid:
            logger.error(f"Validation failed: {validation_result.error_message}")
            return validation_result

        # Additional validation beyond schema
        input_data = payload.get("input_data")

        # Check if input_data is None
        if input_data is None:
            error_message = "Input data is missing"
            logger.error(error_message)
            return ValidationResult(is_valid=False, error_message=error_message)

        # Check if input_data is a valid type for conversion
        if not (isinstance(input_data, list) or isinstance(input_data, dict)):
            error_message = f"Input data must be a list or dictionary, got {type(input_data).__name__}"
            logger.error(error_message)
            return ValidationResult(is_valid=False, error_message=error_message)

        logger.info("Payload validation successful")
        return ValidationResult(is_valid=True)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a data to DataFrame conversion request.

        Args:
            payload: The request payload containing:
                - input_data: The data to convert
                - orientation: The orientation of the data

        Returns:
            A dictionary containing the DataFrame or an error message
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing data to DataFrame request for request_id: {request_id}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        try:
            # Import pandas
            try:
                import pandas as pd
            except ImportError:
                error_msg = "Pandas library is not installed. Please install it with 'pip install pandas'."
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            # Get input data and orientation
            input_data = payload.get("input_data")
            orientation = payload.get("orientation", "auto-detect").lower()

            logger.debug(f"Input data type: {type(input_data).__name__}")
            logger.debug(f"Orientation: {orientation}")

            # Auto-detect orientation if needed
            if orientation == "auto-detect":
                if isinstance(input_data, list) and all(isinstance(item, dict) for item in input_data):
                    orientation = "records"
                    logger.debug("Auto-detected orientation: records (list of dicts)")
                elif isinstance(input_data, dict) and all(isinstance(value, list) for value in input_data.values()):
                    orientation = "columns"
                    logger.debug("Auto-detected orientation: columns (dict of lists)")
                elif isinstance(input_data, dict):
                    # Single dictionary - convert to records format (list with one dict)
                    orientation = "records"
                    input_data = [input_data]  # Wrap in list
                    logger.debug("Auto-detected orientation: records (single dict converted to list)")
                else:
                    error_msg = f"Cannot auto-detect orientation for data type {type(input_data).__name__}. Please specify the orientation explicitly or provide data as list of dicts or dict of lists."
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg
                    }

            # Convert to DataFrame based on orientation
            if orientation == "records":
                if not isinstance(input_data, list):
                    error_msg = f"Expected a list of dictionaries for 'records' orientation, got {type(input_data).__name__}"
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg
                    }

                # Check if all items are dictionaries
                if not all(isinstance(item, dict) for item in input_data):
                    error_msg = "Not all items in the list are dictionaries"
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg
                    }

                # Create DataFrame from records
                df = pd.DataFrame.from_records(input_data)

            elif orientation == "columns":
                if not isinstance(input_data, dict):
                    error_msg = f"Expected a dictionary of lists for 'columns' orientation, got {type(input_data).__name__}"
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg
                    }

                # Check if all values are lists
                if not all(isinstance(value, list) for value in input_data.values()):
                    error_msg = "Not all values in the dictionary are lists"
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg
                    }

                # Check if all lists have the same length
                list_lengths = [len(value) for value in input_data.values()]
                if len(set(list_lengths)) > 1:
                    error_msg = "Lists in the dictionary have different lengths"
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg
                    }

                # Create DataFrame from dict of lists
                df = pd.DataFrame(input_data)

            else:
                error_msg = f"Unsupported orientation: {orientation}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            # Return the DataFrame
            logger.info(f"Data converted to DataFrame successfully. Shape: {df.shape}")

            # Convert DataFrame to a serializable format (records)
            df_records = df.to_dict(orient="records")
            df_columns = {col: df[col].tolist() for col in df.columns}

            return {
                "status": "success",
                "dataframe": {
                    "records": df_records,
                    "columns": df_columns,
                    "shape": list(df.shape),
                    "column_names": list(df.columns)
                }
            }

        except Exception as e:
            error_msg = f"Error converting to DataFrame: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }
