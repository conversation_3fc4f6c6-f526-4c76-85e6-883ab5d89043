#!/usr/bin/env python3
"""
Final diagnosis of the MCP connection issue.
"""

import asyncio
import subprocess
import logging
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key, get_global_ssh_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_persistent_connection():
    """Test if the container supports persistent connections."""
    logger.info("=== Testing Persistent Connection ===")
    
    initialize_global_ssh_key(settings.ssh_key_content)
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    # Create a script that sends multiple messages with delays
    script_content = '''
import json
import time
import sys

# Initialize
init_msg = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
        "protocolVersion": "2024-11-05",
        "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
        "clientInfo": {"name": "persistent-test", "version": "1.0.0"}
    }
}

print(json.dumps(init_msg), flush=True)
time.sleep(1)

# Initialized notification
init_notif = {
    "jsonrpc": "2.0",
    "method": "notifications/initialized"
}

print(json.dumps(init_notif), flush=True)
time.sleep(1)

# List tools
tools_msg = {
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list"
}

print(json.dumps(tools_msg), flush=True)
time.sleep(2)

# List resources
resources_msg = {
    "jsonrpc": "2.0",
    "id": 3,
    "method": "resources/list"
}

print(json.dumps(resources_msg), flush=True)
time.sleep(2)
'''
    
    # Write script to a temporary file and execute it
    test_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"python3 -c '{script_content}' | timeout 15 docker exec -i {container_id} {command}"
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=20)
        logger.info(f"Persistent test - Exit code: {result.returncode}")
        
        if result.stdout:
            logger.info(f"Persistent test - Response:\n{result.stdout}")
            
            # Count responses
            lines = [line.strip() for line in result.stdout.split('\n') if line.strip()]
            json_responses = []
            for line in lines:
                try:
                    json_responses.append(json.loads(line))
                except json.JSONDecodeError:
                    pass
            
            logger.info(f"Got {len(json_responses)} JSON responses")
            for i, resp in enumerate(json_responses):
                logger.info(f"Response {i+1}: {resp.get('id', 'notification')} - {resp.get('method', resp.get('result', {}).get('serverInfo', {}).get('name', 'unknown'))}")
        
        if result.stderr:
            logger.info(f"Persistent test - Stderr:\n{result.stderr}")
            
        return len(json_responses) > 1  # Success if we got multiple responses
            
    except Exception as e:
        logger.error(f"Persistent test failed: {e}")
        return False


async def test_connection_behavior():
    """Test how the connection behaves with different timing."""
    logger.info("=== Testing Connection Behavior ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    command = "node dist/index.js"
    
    # Test 1: Quick succession
    logger.info("Test 1: Quick succession...")
    quick_script = '''
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"roots":{"listChanged":true},"sampling":{}},"clientInfo":{"name":"quick-test","version":"1.0.0"}}}'
echo '{"jsonrpc":"2.0","method":"notifications/initialized"}'
echo '{"jsonrpc":"2.0","id":2,"method":"tools/list"}'
'''
    
    test_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"bash -c '{quick_script}' | timeout 5 docker exec -i {container_id} {command}"
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
        logger.info(f"Quick test - Exit code: {result.returncode}")
        if result.stdout:
            response_count = len([line for line in result.stdout.split('\n') if line.strip().startswith('{')])
            logger.info(f"Quick test - Got {response_count} responses")
    except Exception as e:
        logger.error(f"Quick test failed: {e}")
    
    # Test 2: With delays
    logger.info("Test 2: With delays...")
    delay_script = '''
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"roots":{"listChanged":true},"sampling":{}},"clientInfo":{"name":"delay-test","version":"1.0.0"}}}'
sleep 0.5
echo '{"jsonrpc":"2.0","method":"notifications/initialized"}'
sleep 0.5
echo '{"jsonrpc":"2.0","id":2,"method":"tools/list"}'
sleep 1
'''
    
    test_cmd[7] = f"bash -c '{delay_script}' | timeout 8 docker exec -i {container_id} {command}"
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=12)
        logger.info(f"Delay test - Exit code: {result.returncode}")
        if result.stdout:
            response_count = len([line for line in result.stdout.split('\n') if line.strip().startswith('{')])
            logger.info(f"Delay test - Got {response_count} responses")
    except Exception as e:
        logger.error(f"Delay test failed: {e}")


async def main():
    """Main test function."""
    logger.info("Starting Final Diagnosis")
    
    if not settings.ssh_key_content:
        logger.error("No SSH key content found")
        return
    
    # Test persistent connection
    persistent_works = await test_persistent_connection()
    
    # Test connection behavior
    await test_connection_behavior()
    
    # Conclusion
    logger.info("\n" + "="*60)
    logger.info("DIAGNOSIS CONCLUSION:")
    logger.info("="*60)
    
    if persistent_works:
        logger.info("✅ Container DOES support persistent connections")
        logger.info("❌ Issue is likely in the MCP STDIO client implementation")
        logger.info("💡 Recommendation: Check MCP client session handling")
    else:
        logger.info("❌ Container does NOT support persistent connections")
        logger.info("💡 Recommendation: Use single-request mode or different connection type")
    
    logger.info("="*60)


if __name__ == "__main__":
    asyncio.run(main())
