#!/usr/bin/env python3
"""
Test script for the new knowledge content tools.

This script demonstrates how to use the new knowledge content retrieval tools
that were implemented as FunctionTool instances following AutoGen's tool patterns.
"""

import asyncio
import logging
import os
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage

from app.tools.knowledge_tool_loader import KnowledgeToolLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_knowledge_tools():
    """Test the knowledge content tools."""

    # Initialize the knowledge tool loader
    knowledge_loader = KnowledgeToolLoader()

    # Create knowledge content tools for a test organization
    organization_id = "test-org-123"
    knowledge_tools = knowledge_loader.create_knowledge_content_tools(
        organization_id=organization_id
    )

    logger.info(f"Created {len(knowledge_tools)} knowledge tools")

    # Print tool information
    for tool in knowledge_tools:
        logger.info(f"Tool: {tool.name}")
        logger.info(f"Description: {tool.description}")
        logger.info(f"Schema: {tool.schema}")
        print("-" * 50)

    # Test the tools directly
    cancellation_token = CancellationToken()

    # Test get_knowledge_content tool
    get_content_tool = knowledge_tools[0]  # First tool should be get_knowledge_content

    logger.info("Testing get_knowledge_content tool...")
    try:
        result = await get_content_tool.run_json(
            {
                "query": "How to setup authentication",
                "user_id": "test-user-123",
                "agent_id": "test-agent-456",
                "top_k": 3,
            },
            cancellation_token,
        )
        logger.info(f"Search result: {get_content_tool.return_value_as_string(result)}")
    except Exception as e:
        logger.error(f"Error testing get_knowledge_content: {e}")

    # Test get_batch_knowledge_content tool
    get_batch_tool = knowledge_tools[
        1
    ]  # Second tool should be get_batch_knowledge_content

    logger.info("Testing get_batch_knowledge_content tool...")
    try:
        result = await get_batch_tool.run_json(
            {
                "query_texts": [
                    "authentication setup procedures",
                    "user management best practices",
                    "security configuration guide",
                ],
                "user_id": "test-user-123",
                "agent_id": "test-agent-456",
                "top_k": 2,
            },
            cancellation_token,
        )
        logger.info(f"Batch result: {get_batch_tool.return_value_as_string(result)}")
    except Exception as e:
        logger.error(f"Error testing get_batch_knowledge_content: {e}")


async def test_agent_with_knowledge_tools():
    """Test an agent equipped with knowledge tools."""

    # Check if OpenAI API key is available
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logger.warning("OPENAI_API_KEY not found. Skipping agent test.")
        return

    logger.info("Testing agent with knowledge tools...")

    # Create model client
    model_client = OpenAIChatCompletionClient(
        model="gpt-4o-mini",
        api_key=api_key,
    )

    # Create knowledge tools
    knowledge_loader = KnowledgeToolLoader()
    organization_id = "test-org-123"
    knowledge_tools = knowledge_loader.create_knowledge_content_tools(
        organization_id=organization_id
    )

    # Create agent with knowledge tools
    agent = AssistantAgent(
        name="knowledge_assistant",
        model_client=model_client,
        tools=knowledge_tools,
        system_message=(
            "You are a helpful AI assistant with access to knowledge tools. "
            "Use the knowledge tools to search for and retrieve information "
            "when users ask questions. Always try to provide accurate and "
            "helpful responses based on the available knowledge."
        ),
        reflect_on_tool_use=True,
    )

    # Test the agent
    test_messages = [
        "Can you search for information about user authentication procedures? Use user ID 'test-user-123' and agent ID 'test-agent-456'.",
        "Please perform a batch search for these topics: authentication setup, user management, and security configuration. Use user ID 'test-user-123' and agent ID 'test-agent-456'.",
    ]

    for message_content in test_messages:
        logger.info(f"Testing message: {message_content}")

        try:
            message = TextMessage(content=message_content, source="user")
            response = await agent.on_messages([message], CancellationToken())

            logger.info(f"Agent response: {response.chat_message.content}")
            print("-" * 50)

        except Exception as e:
            logger.error(f"Error testing agent: {e}")

    # Close the model client
    await model_client.close()


async def main():
    """Main test function."""
    logger.info("Starting knowledge tools tests...")

    # Test 1: Test knowledge tools directly
    await test_knowledge_tools()

    print("=" * 80)

    # Test 2: Test agent with knowledge tools
    await test_agent_with_knowledge_tools()

    logger.info("Knowledge tools tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
