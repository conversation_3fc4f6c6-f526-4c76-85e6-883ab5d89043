"use client";

import React from "react";
import { ChakraProvider as ChakraUIProvider } from "@chakra-ui/react";

// Create a simple wrapper component that just passes children through
export function ChakraProvider({ children }: { children: React.ReactNode }) {
  // Just render the children directly without using ChakraUIProvider
  // This is a temporary solution until we properly migrate away from Chakra UI
  return <>{children}</>;
}
