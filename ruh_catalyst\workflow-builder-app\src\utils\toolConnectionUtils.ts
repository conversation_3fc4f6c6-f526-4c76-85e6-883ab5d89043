/**
 * Utility functions for managing tool connections in AgenticAI workflow components
 */

import { Edge, Node } from "reactflow";
import { WorkflowNodeData, ToolConnectionInfo, ToolConnectionState } from "@/types";

/**
 * Checks if a handle name represents a tool connection
 * @param handleName - The handle name to check (e.g., 'tools', 'input_data')
 * @returns True if the handle is a tool handle
 */
export function isToolHandle(handleName: string): boolean {
  return handleName === "tools";
}

/**
 * Extracts tool slot number from tool handle name
 * @param toolHandle - Tool handle name (e.g., 'tool_1', 'tool_2')
 * @returns Tool slot number or null if not a valid tool handle
 */
export function extractToolSlotNumber(toolHandle: string): number | null {
  const match = toolHandle.match(/^tool_(\d+)$/);
  return match ? parseInt(match[1], 10) : null;
}

/**
 * Gets all tool connections for a specific AgenticAI node
 * @param nodeId - The ID of the AgenticAI node
 * @param edges - All edges in the workflow
 * @param nodes - All nodes in the workflow
 * @returns Array of tool connection information
 */
export function getToolConnections(
  nodeId: string,
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[]
): ToolConnectionInfo[] {
  // Input validation
  if (!nodeId || !Array.isArray(edges) || !Array.isArray(nodes)) {
    console.warn("[toolConnectionUtils] Invalid input parameters for getToolConnections");
    return [];
  }

  const toolConnections: ToolConnectionInfo[] = [];

  try {
    // Find all edges targeting this node with tool handles
    const toolEdges = edges.filter(
      (edge) => edge.target === nodeId && edge.targetHandle && isToolHandle(edge.targetHandle)
    );

    for (const edge of toolEdges) {
      const sourceNode = nodes.find((node) => node.id === edge.source);
      if (sourceNode && edge.targetHandle) {
        const componentType = sourceNode.data.definition?.type === "mcp" ? "mcp" : "regular";

        toolConnections.push({
          toolSlot: edge.targetHandle,
          connectedNodeId: edge.source,
          connectedNodeType: sourceNode.data.originalType || sourceNode.data.type,
          connectedNodeLabel: sourceNode.data.label,
          componentType,
        });
      }
    }

    return toolConnections.sort((a, b) => {
      const aNum = extractToolSlotNumber(a.toolSlot) || 0;
      const bNum = extractToolSlotNumber(b.toolSlot) || 0;
      return aNum - bNum;
    });
  } catch (error) {
    console.error("[toolConnectionUtils] Error in getToolConnections:", error);
    return [];
  }
}

/**
 * Gets all tool connections grouped by handle for a specific AgenticAI node
 * Supports multiple tools per handle (new array format)
 * @param nodeId - The ID of the AgenticAI node
 * @param edges - All edges in the workflow
 * @param nodes - All nodes in the workflow
 * @returns Object mapping tool handles to arrays of connected tools
 */
export function getToolConnectionsByHandle(
  nodeId: string,
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[]
): Record<string, ToolConnectionInfo[]> {
  // Input validation
  if (!nodeId || !Array.isArray(edges) || !Array.isArray(nodes)) {
    console.warn("[toolConnectionUtils] Invalid input parameters for getToolConnectionsByHandle");
    return {};
  }

  const toolConnectionsByHandle: Record<string, ToolConnectionInfo[]> = {};

  try {
    // Find all edges targeting this node with tool handles
    const toolEdges = edges.filter(
      (edge) => edge.target === nodeId && edge.targetHandle && isToolHandle(edge.targetHandle)
    );

    for (const edge of toolEdges) {
      const sourceNode = nodes.find((node) => node.id === edge.source);
      if (sourceNode && edge.targetHandle) {
        const componentType = sourceNode.data.definition?.type === "mcp" ? "mcp" : "regular";

        const toolConnection: ToolConnectionInfo = {
          toolSlot: edge.targetHandle,
          connectedNodeId: edge.source,
          connectedNodeType: sourceNode.data.originalType || sourceNode.data.type,
          connectedNodeLabel: sourceNode.data.label,
          componentType,
        };

        // Initialize array for this handle if it doesn't exist
        if (!toolConnectionsByHandle[edge.targetHandle]) {
          toolConnectionsByHandle[edge.targetHandle] = [];
        }

        // Add the connection to the handle's array
        toolConnectionsByHandle[edge.targetHandle].push(toolConnection);
      }
    }

    // Sort connections within each handle by node label for consistency
    Object.keys(toolConnectionsByHandle).forEach(handle => {
      toolConnectionsByHandle[handle].sort((a, b) =>
        (a.connectedNodeLabel || '').localeCompare(b.connectedNodeLabel || '')
      );
    });

    return toolConnectionsByHandle;
  } catch (error) {
    console.error("[toolConnectionUtils] Error in getToolConnectionsByHandle:", error);
    return {};
  }
}

/**
 * Gets the count of tools connected to each handle
 * @param nodeId - The ID of the AgenticAI node
 * @param edges - All edges in the workflow
 * @returns Object mapping tool handles to connection counts
 */
export function getToolConnectionCounts(
  nodeId: string,
  edges: Edge[]
): Record<string, number> {
  const connectionCounts: Record<string, number> = {};

  try {
    // Find all edges targeting this node with tool handles
    const toolEdges = edges.filter(
      (edge) => edge.target === nodeId && edge.targetHandle && isToolHandle(edge.targetHandle)
    );

    // Count connections per handle
    toolEdges.forEach(edge => {
      if (edge.targetHandle) {
        connectionCounts[edge.targetHandle] = (connectionCounts[edge.targetHandle] || 0) + 1;
      }
    });

    return connectionCounts;
  } catch (error) {
    console.error("[toolConnectionUtils] Error in getToolConnectionCounts:", error);
    return {};
  }
}

/**
 * Calculates the complete tool connection state for an AgenticAI node
 * Supports both edge-based connections and config-based tool connections (for single handle approach)
 * @param nodeId - The ID of the AgenticAI node
 * @param edges - All edges in the workflow
 * @param nodes - All nodes in the workflow
 * @returns Complete tool connection state
 */
export function calculateToolConnectionState(
  nodeId: string,
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[]
): {
  connectedTools: Array<{
    nodeId: string;
    handleId: string;
    componentType: string;
    label: string;
  }>;
  toolCount: number;
  hasToolConnections: boolean;
  hasConnectedTools: boolean;
  connectedToolCount: number;
  toolConnections: Array<{
    toolSlot: string;
    connectedNodeId: string;
    connectedNodeType: string;
    connectedNodeLabel: string;
    componentType: string;
  }>;
  availableToolSlots: string[];
} {
  const connectedTools: Array<{
    nodeId: string;
    handleId: string;
    componentType: string;
    label: string;
  }> = [];

  // Find the target node to check for config-based tool connections
  const targetNode = nodes.find(n => n.id === nodeId);

  // Method 1: Check for edge-based connections (current approach)
  const toolEdges = edges.filter(
    (edge) => edge.target === nodeId && edge.targetHandle && (
      isToolHandle(edge.targetHandle) || edge.targetHandle === "tools"
    )
  );

  toolEdges.forEach((edge) => {
    const sourceNode = nodes.find((n) => n.id === edge.source);
    if (sourceNode) {
      connectedTools.push({
        nodeId: edge.source,
        handleId: edge.targetHandle!,
        componentType: sourceNode.data.originalType || "Unknown",
        label: sourceNode.data.label || `Node ${edge.source}`,
      });
    }
  });

  // Method 2: Check for config-based tool connections (for cases where tools are in config but nodes are missing)
  if (targetNode?.data.config?.tools) {
    const tools = targetNode.data.config.tools;

    // Handle new simplified tools format
    if (Array.isArray(tools)) {
      tools.forEach((toolData: any) => {
        // Check if this tool is already in connectedTools (from edges)
        const existingTool = connectedTools.find(t => t.nodeId === toolData.node_id);
        if (!existingTool && toolData.node_id) {
          // Add tool from config even if node doesn't exist in nodes array
          connectedTools.push({
            nodeId: toolData.node_id,
            handleId: "tools", // Single handle approach
            componentType: toolData.node_type || "MCP",
            label: toolData.node_label || toolData.component_definition?.display_name || "Unknown Tool",
          });
        }
      });
    }
  }

  // Get tool connections in the expected format (for backward compatibility)
  const toolConnections = connectedTools.map(tool => ({
    toolSlot: tool.handleId,
    connectedNodeId: tool.nodeId,
    connectedNodeType: tool.componentType,
    connectedNodeLabel: tool.label,
    componentType: tool.componentType.toLowerCase().includes('mcp') ? 'mcp' : 'regular',
  }));

  // For single handle approach, available tool slots is not relevant
  const availableToolSlots: string[] = [];

  const hasConnections = connectedTools.length > 0;

  return {
    connectedTools,
    toolCount: connectedTools.length,
    hasToolConnections: hasConnections,
    hasConnectedTools: hasConnections,
    connectedToolCount: connectedTools.length,
    toolConnections,
    availableToolSlots,
  };
}

/**
 * Gets the number of tool handles for a node
 * @param node - The node to check
 * @returns Number of tool handles
 */
export function getToolHandleCount(node: Node<WorkflowNodeData>): number {
  // For AgenticAI nodes, check the num_tool_handles config
  if (node.data.originalType === "AgenticAI") {
    return node.data.config?.num_tool_handles || 0;
  }
  
  // For other nodes, return 0
  return 0;
}

/**
 * Checks if a node is connected as a tool to any AgenticAI node
 * @param nodeId - The ID of the node to check
 * @param edges - All edges in the workflow
 * @returns True if the node is connected as a tool
 */
export function isNodeConnectedAsTool(nodeId: string, edges: Edge[]): boolean {
  return edges.some(
    (edge) => edge.source === nodeId && edge.targetHandle && isToolHandle(edge.targetHandle)
  );
}

/**
 * Gets all nodes that are connected as tools to AgenticAI nodes
 * @param edges - All edges in the workflow
 * @returns Set of node IDs that are connected as tools
 */
export function getNodesConnectedAsTools(edges: Edge[]): Set<string> {
  const connectedAsTools = new Set<string>();
  
  edges.forEach((edge) => {
    if (edge.targetHandle && isToolHandle(edge.targetHandle)) {
      connectedAsTools.add(edge.source);
    }
  });
  
  return connectedAsTools;
}

/**
 * Generates CSS classes for tool-related styling
 * @param hasConnectedTools - Whether the node has connected tools
 * @param connectedToolCount - Number of connected tools
 * @param isConnectedAsTool - Whether this node is connected as a tool
 * @returns Object with CSS class names
 */
export function generateToolStyleClasses(
  hasConnectedTools: boolean,
  connectedToolCount: number,
  isConnectedAsTool: boolean
) {
  return {
    agenticAIWithTools: hasConnectedTools ? "agentic-ai-has-tools" : "",
    connectedAsTool: isConnectedAsTool ? "connected-as-tool" : "",
    toolCountBadge: hasConnectedTools ? "tool-count-badge" : "",
  };
}

// getAvailableToolSlots function removed - no longer needed with single handle approach

/**
 * Calculates handle positions (re-exported from handleUtils for compatibility)
 */
export { calculateHandlePositions } from "./handleUtils";
