#!/usr/bin/env python3
"""
MCP Executor Service CLI

Command-line interface for testing MCP execution with smart routing support.

Usage:
    poetry run python -m app.cli mcp_execution \
        --tool_name git_directory_structure \
        --tool_parameters '{"repo_url": "https://github.com/modelcontextprotocol/python-sdk.git"}' \
        --mcp_id ********-f358-4595-9993-33abd9afee06 \
        --user_id 91a237fd-0225-4e02-9e9f-805eff073b07 \
        --server_script_path http://localhost:5001/mcp
"""

import argparse
import asyncio
import json
import logging
import sys
import time
import uuid
from typing import Dict, Any, Optional

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MCPExecutionCLI:
    """CLI for MCP execution testing."""

    def __init__(self):
        pass

    async def send_mcp_request(self, payload: Dict[str, Any]) -> str:
        """Send MCP execution request via Kafka."""
        request_id = payload.get("request_id", str(uuid.uuid4()))

        logger.info(f"📤 Sending MCP execution request")
        logger.info(f"   Request ID: {request_id}")
        logger.info(f"   Tool: {payload.get('tool_name')}")
        logger.info(f"   MCP ID: {payload.get('mcp_id', 'Not provided')}")
        logger.info(f"   User ID: {payload.get('user_id', 'Not provided')}")
        logger.info(
            f"   Server Script Path: {payload.get('server_script_path', 'Not provided')}"
        )

        producer = None
        try:
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await producer.start()

            # Send to the correct topic
            await producer.send(settings.kafka_consumer_topic, value=payload)

            logger.info(
                f"✅ Request sent to Kafka topic: {settings.kafka_consumer_topic}"
            )
            return request_id

        except Exception as e:
            logger.error(f"❌ Failed to send request: {e}")
            raise
        finally:
            if producer:
                await producer.stop()

    async def wait_for_response(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Wait for response from Kafka results topic."""
        consumer = None
        try:
            consumer = AIOKafkaConsumer(
                settings.kafka_results_topic,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=f"mcp-cli-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
                value_deserializer=lambda m: json.loads(m.decode("utf-8")),
            )
            await consumer.start()

            logger.info(
                f"📥 Waiting for response (no timeout - press Ctrl+C to cancel)..."
            )

            while True:
                try:
                    messages = await consumer.getmany(timeout_ms=1000)

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:
                                response = msg.value
                                if (
                                    isinstance(response, dict)
                                    and response.get("request_id") == request_id
                                ):
                                    logger.info(
                                        f"✅ Received response for request: {request_id}"
                                    )
                                    return response
                            except Exception as e:
                                logger.debug(f"Error processing message: {e}")
                                continue

                except Exception as e:
                    logger.warning(f"Error consuming messages: {e}")

                await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            logger.info("⏹️ Cancelled by user")
            return None
        finally:
            if consumer:
                await consumer.stop()

    async def execute_mcp_tool(
        self,
        tool_name: str,
        tool_parameters: Dict[str, Any],
        mcp_id: Optional[str] = None,
        user_id: Optional[str] = None,
        server_script_path: Optional[str] = None,
        retries: int = 3,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Execute MCP tool with smart routing support."""

        # Create payload
        request_id = str(uuid.uuid4())
        payload = {
            "request_id": request_id,
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "retries": retries,
        }

        # Add headers if provided
        if headers:
            payload["headers"] = headers

        # Add execution method parameters - prioritize mcp_id over server_script_path
        if mcp_id:
            payload["mcp_id"] = mcp_id
            if user_id:
                payload["user_id"] = user_id
            logger.info("🎯 Using smart routing (mcp_id provided)")
            if server_script_path:
                logger.info("ℹ️ Ignoring server_script_path since mcp_id is provided")
        elif server_script_path:
            payload["server_script_path"] = server_script_path
            logger.info("🔗 Using legacy routing (server_script_path provided)")
        else:
            raise ValueError("Either mcp_id or server_script_path must be provided")

        # Send request
        start_time = time.time()
        await self.send_mcp_request(payload)

        # Wait for response
        response = await self.wait_for_response(request_id)
        end_time = time.time()

        # Process result
        result = {
            "request_id": request_id,
            "execution_time": end_time - start_time,
            "success": False,
            "response": response,
            "error": None,
        }

        if response:
            result["success"] = response.get("mcp_status") == "success"
            result["mcp_status"] = response.get("mcp_status")
            result["result_data"] = response.get("result")
            result["error"] = response.get("error") if not result["success"] else None
        else:
            result["error"] = "No response received (cancelled by user)"

        return result

    def print_result(self, result: Dict[str, Any]):
        """Print execution result in a formatted way."""
        print("\n" + "=" * 80)
        print("📊 MCP EXECUTION RESULT")
        print("=" * 80)

        print(f"Request ID: {result['request_id']}")
        print(f"Execution Time: {result['execution_time']:.2f}s")
        print(f"Success: {'✅ YES' if result['success'] else '❌ NO'}")

        if result.get("mcp_status"):
            print(f"MCP Status: {result['mcp_status']}")

        if result.get("error"):
            print(f"Error: {result['error']}")

        if result.get("result_data"):
            print(f"\n📋 Result Data:")
            if isinstance(result["result_data"], (dict, list)):
                print(json.dumps(result["result_data"], indent=2))
            else:
                print(result["result_data"])

        print("=" * 80)


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command-line interface parser."""
    parser = argparse.ArgumentParser(
        description="MCP Executor Service CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # MCP execution command
    mcp_parser = subparsers.add_parser("mcp_execution", help="Execute MCP tool")

    # Required arguments
    mcp_parser.add_argument(
        "--tool_name", required=True, help="Name of the tool to execute"
    )
    mcp_parser.add_argument(
        "--tool_parameters", required=True, help="Tool parameters as JSON string"
    )

    # Execution method arguments (both can be provided, mcp_id takes priority)
    mcp_parser.add_argument(
        "--server_script_path", help="Server script path for legacy execution"
    )
    mcp_parser.add_argument(
        "--mcp_id",
        help="MCP ID for smart routing execution (takes priority over server_script_path)",
    )

    # Smart routing arguments
    mcp_parser.add_argument("--user_id", help="User ID (required when using --mcp_id)")

    # Optional arguments
    mcp_parser.add_argument(
        "--retries", type=int, default=3, help="Number of retries (default: 3)"
    )
    mcp_parser.add_argument(
        "--headers", help="HTTP headers as JSON string (e.g., '{\"x-google-client-id\": \"your-id\"}')"
    )
    mcp_parser.add_argument(
        "--verbose", action="store_true", help="Enable verbose logging"
    )

    return parser


async def main():
    """Main CLI entry point."""
    parser = create_cli_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    if args.command == "mcp_execution":
        # Validate arguments - prioritize mcp_id over server_script_path
        if args.mcp_id:
            if not args.user_id:
                logger.error("❌ --user_id is required when using --mcp_id")
                return 1
            if args.server_script_path:
                logger.info(
                    "ℹ️ Both mcp_id and server_script_path provided. Using smart routing (mcp_id) and ignoring server_script_path"
                )
        elif not args.server_script_path:
            logger.error("❌ Either --mcp_id or --server_script_path must be provided")
            return 1

        # Parse tool parameters
        try:
            tool_parameters = json.loads(args.tool_parameters)
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON in tool_parameters: {e}")
            return 1

        # Parse headers if provided
        headers = None
        if args.headers:
            try:
                headers = json.loads(args.headers)
                if not isinstance(headers, dict):
                    logger.error("❌ Headers must be a JSON object")
                    return 1
            except json.JSONDecodeError as e:
                logger.error(f"❌ Invalid JSON in headers: {e}")
                return 1

        # Create CLI instance
        cli = MCPExecutionCLI()

        try:
            # Execute MCP tool
            result = await cli.execute_mcp_tool(
                tool_name=args.tool_name,
                tool_parameters=tool_parameters,
                mcp_id=args.mcp_id,
                user_id=args.user_id,
                server_script_path=args.server_script_path,
                retries=args.retries,
                headers=headers,
            )

            # Print result
            cli.print_result(result)

            # Return appropriate exit code
            return 0 if result["success"] else 1

        except Exception as e:
            logger.error(f"💥 Execution failed: {e}")
            return 1

    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Cancelled by user")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
