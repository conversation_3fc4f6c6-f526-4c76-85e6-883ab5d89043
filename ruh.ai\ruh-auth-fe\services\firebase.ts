import { firebaseConfig } from "@/app/shared/firebaseConfig";
import { initializeApp } from "firebase/app";
import {
  getMessaging,
  getToken,
  isSupported,
  onMessage,
} from "firebase/messaging";

// Initialize Firebase
let app: any;
let messaging: any;

// Initialize Firebase and messaging on client side only
const initializeFirebase = async () => {
  if (typeof window === "undefined") return false;

  try {
    // Check if Firebase is already initialized
    if (!app) {
      app = initializeApp(firebaseConfig);
    }

    // Check if browser supports Firebase messaging
    const isMessagingSupported = await isSupported();
    if (!isMessagingSupported) {
      return false;
    }

    // Initialize messaging if not already initialized
    if (!messaging) {
      messaging = getMessaging(app);
    }

    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Request FCM token for push notifications
 * @returns Promise<string | null> FCM token if successful, null otherwise
 */
export const requestFCMToken = async (): Promise<string | null> => {
  try {
    // Initialize Firebase if not already initialized
    const isInitialized = await initializeFirebase();
    if (!isInitialized) {
      return null;
    }

    // Check if service worker is registered
    if (navigator.serviceWorker) {
      const registrations = await navigator.serviceWorker.getRegistrations();

      // Check if firebase-messaging-sw.js is registered
      const hasFCMServiceWorker = registrations.some(
        (reg) =>
          reg.active &&
          reg.active.scriptURL.includes("firebase-messaging-sw.js")
      );

      if (!hasFCMServiceWorker) {
        try {
          await navigator.serviceWorker.register("/firebase-messaging-sw.js");
        } catch (swError) {
          // Silent error handling
        }
      }
    }

    // Request notification permission
    const permission = await Notification.requestPermission();
    if (permission !== "granted") {
      return null;
    }

    // Get FCM token
    const token = await getToken(messaging, {
      vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
    });

    return token;
  } catch (error) {
    return null;
  }
};

/**
 * Set up a listener for foreground messages
 * @param callback Function to call when a message is received
 * @returns Unsubscribe function
 */
export const onForegroundMessage = (callback: (payload: any) => void) => {
  if (!messaging) {
    return () => {};
  }

  return onMessage(messaging, callback);
};

// Export the service as an object for consistency with other services
export const firebaseService = {
  requestFCMToken,
  onForegroundMessage,
  initializeFirebase,
};
