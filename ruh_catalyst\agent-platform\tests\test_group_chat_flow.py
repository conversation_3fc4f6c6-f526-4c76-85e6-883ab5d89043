#!/usr/bin/env python3
"""
Test script for the complete group chat flow.

This script demonstrates and tests:
1. Group validation
2. Group chat session creation
3. Group chat conversation
4. Session management
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from app.services.agent_group_service import AgentGroupService
from app.autogen_service.group_chat_processor import GroupChatProcessor
from app.autogen_service.agent_factory import AgentFactory
from app.helper.session_manager import SessionManager
from app.helper.redis_client import RedisClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class GroupChatFlowTester:
    """Test class for group chat functionality."""

    def __init__(self):
        self.logger = logger
        self.redis_client = None
        self.session_manager = None
        self.agent_factory = None
        self.group_chat_processor = None
        self.agent_group_service = None

    async def setup(self):
        """Setup test environment."""
        try:
            self.logger.info("Setting up test environment...")

            # Initialize Redis client
            self.redis_client = RedisClient()
            await self.redis_client.initialize()

            # Initialize session manager
            self.session_manager = SessionManager(
                self.redis_client,
                session_ttl=3600,
                max_messages=50,
                compression_level=6,
            )

            # Initialize services
            self.agent_factory = AgentFactory()
            self.group_chat_processor = GroupChatProcessor(
                self.session_manager, self.agent_factory
            )
            self.agent_group_service = AgentGroupService()

            self.logger.info("Test environment setup completed")

        except Exception as e:
            self.logger.error(f"Error setting up test environment: {e}")
            raise

    async def cleanup(self):
        """Cleanup test environment."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            self.logger.info("Test environment cleaned up")
        except Exception as e:
            self.logger.error(f"Error cleaning up test environment: {e}")

    async def test_group_validation(self, group_id: str) -> bool:
        """Test group validation functionality."""
        try:
            self.logger.info(f"Testing group validation for group: {group_id}")

            # Test group details fetching
            group_details = await self.agent_group_service.fetch_group_details(group_id)
            if group_details:
                self.logger.info(f"Group details: {group_details}")
            else:
                self.logger.warning(f"No group details found for: {group_id}")
                return False

            # Test group agents fetching
            agents = await self.agent_group_service.fetch_group_agents(group_id)
            self.logger.info(f"Found {len(agents)} agents in group")

            # Test group validation
            is_valid = await self.agent_group_service.validate_group_for_chat(group_id)
            self.logger.info(f"Group validation result: {is_valid}")

            # Test chat configuration
            chat_config = await self.agent_group_service.get_group_chat_config(group_id)
            if chat_config:
                self.logger.info(f"Chat config: {chat_config}")

            return is_valid

        except Exception as e:
            self.logger.error(f"Error testing group validation: {e}")
            return False

    async def test_group_chat_session_creation(
        self, group_id: str, user_id: str = "test-user-123"
    ) -> str:
        """Test group chat session creation."""
        try:
            self.logger.info(
                f"Testing group chat session creation for group: {group_id}"
            )

            session_id = await self.group_chat_processor.create_group_chat_session(
                group_id=group_id,
                user_id=user_id,
                communication_type="group_chat",
                organization_id="test-org-456",
                use_knowledge=True,
                variables={"test_var": "test_value"},
            )

            if session_id:
                self.logger.info(
                    f"Successfully created group chat session: {session_id}"
                )

                # Test getting session info
                session_info = await self.group_chat_processor.get_group_chat_info(
                    session_id
                )
                if session_info:
                    self.logger.info(f"Session info: {session_info}")

                return session_id
            else:
                self.logger.error("Failed to create group chat session")
                return None

        except Exception as e:
            self.logger.error(f"Error testing group chat session creation: {e}")
            return None

    async def test_group_chat_conversation(
        self, session_id: str, messages: list = None
    ) -> bool:
        """Test group chat conversation."""
        try:
            if not messages:
                messages = [
                    "Hello everyone! Can you help me with a Python coding task?",
                    "I need to create a function that calculates the factorial of a number.",
                    "Please provide both recursive and iterative solutions.",
                ]

            self.logger.info(
                f"Testing group chat conversation for session: {session_id}"
            )

            for i, message in enumerate(messages, 1):
                self.logger.info(f"Sending message {i}: {message}")

                try:
                    response_count = 0
                    async for (
                        response_chunk
                    ) in self.group_chat_processor.process_group_chat(
                        session_id=session_id,
                        user_message=message,
                        run_id=f"test-run-{i}",
                    ):
                        response_count += 1
                        self.logger.info(f"Response {response_count}: {response_chunk}")

                        # If this is the final response, break
                        if response_chunk.get("final", False):
                            break

                    self.logger.info(
                        f"Completed message {i} with {response_count} responses"
                    )

                    # Wait a bit between messages
                    await asyncio.sleep(2)

                except Exception as e:
                    self.logger.error(f"Error processing message {i}: {e}")
                    continue

            return True

        except Exception as e:
            self.logger.error(f"Error testing group chat conversation: {e}")
            return False

    async def test_session_cleanup(self, session_id: str) -> bool:
        """Test session cleanup."""
        try:
            self.logger.info(f"Testing session cleanup for: {session_id}")

            success = await self.group_chat_processor.end_group_chat_session(session_id)

            if success:
                self.logger.info("Session cleanup successful")

                # Verify session is gone
                session_info = await self.group_chat_processor.get_group_chat_info(
                    session_id
                )
                if not session_info:
                    self.logger.info("Session successfully removed")
                    return True
                else:
                    self.logger.warning("Session still exists after cleanup")
                    return False
            else:
                self.logger.error("Session cleanup failed")
                return False

        except Exception as e:
            self.logger.error(f"Error testing session cleanup: {e}")
            return False

    async def run_full_test(self, group_id: str) -> Dict[str, Any]:
        """Run the complete group chat flow test."""
        results = {
            "group_validation": False,
            "session_creation": False,
            "conversation": False,
            "session_cleanup": False,
            "session_id": None,
        }

        try:
            self.logger.info("=" * 80)
            self.logger.info("STARTING FULL GROUP CHAT FLOW TEST")
            self.logger.info("=" * 80)

            # Test 1: Group validation
            self.logger.info("\n1. Testing group validation...")
            results["group_validation"] = await self.test_group_validation(group_id)

            if not results["group_validation"]:
                self.logger.error("Group validation failed. Stopping test.")
                return results

            # Test 2: Session creation
            self.logger.info("\n2. Testing group chat session creation...")
            session_id = await self.test_group_chat_session_creation(group_id)
            results["session_creation"] = session_id is not None
            results["session_id"] = session_id

            if not results["session_creation"]:
                self.logger.error("Session creation failed. Stopping test.")
                return results

            # Test 3: Conversation
            self.logger.info("\n3. Testing group chat conversation...")
            results["conversation"] = await self.test_group_chat_conversation(
                session_id
            )

            # Test 4: Session cleanup
            self.logger.info("\n4. Testing session cleanup...")
            results["session_cleanup"] = await self.test_session_cleanup(session_id)

            # Summary
            self.logger.info("\n" + "=" * 80)
            self.logger.info("TEST RESULTS SUMMARY")
            self.logger.info("=" * 80)
            for test_name, result in results.items():
                if test_name != "session_id":
                    status = "✅ PASS" if result else "❌ FAIL"
                    self.logger.info(f"{test_name.replace('_', ' ').title()}: {status}")

            overall_success = all(
                results[key] for key in results if key != "session_id"
            )
            self.logger.info(
                f"\nOverall Test Result: {'✅ PASS' if overall_success else '❌ FAIL'}"
            )

            return results

        except Exception as e:
            self.logger.error(f"Error running full test: {e}")
            return results


async def main():
    """Main test function."""
    # Test configuration
    test_group_id = "test-group-123"  # Replace with actual group ID

    tester = GroupChatFlowTester()

    try:
        # Setup
        await tester.setup()

        # Run tests
        results = await tester.run_full_test(test_group_id)

        # Print final results
        print("\n" + "=" * 80)
        print("FINAL TEST RESULTS")
        print("=" * 80)
        print(f"Results: {results}")

    except Exception as e:
        logger.error(f"Test execution failed: {e}")
    finally:
        # Cleanup
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
