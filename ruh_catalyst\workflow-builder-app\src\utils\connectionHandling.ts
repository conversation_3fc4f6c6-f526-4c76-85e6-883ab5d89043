import { Edge, Node } from "reactflow";
import { InputDefinition, WorkflowNodeData } from "@/types";

/**
 * Checks if an input should be disabled based on connections
 * @param inputName The name of the input
 * @param connectedInputs Record of connected inputs
 * @returns True if the input should be disabled
 */
export function shouldDisableInput(
  inputName: string,
  connectedInputs: Record<string, boolean>
): boolean {
  // If this is a handle input, it should never be disabled
  if (inputName.endsWith("_handle")) {
    return false;
  }

  // For inputs with is_handle=true, check if they're connected
  if (connectedInputs[inputName]) {
    return true;
  }

  // Check if there's a corresponding handle input
  const handleName = `${inputName}_handle`;

  // If the handle is connected, disable the direct input
  return !!connectedInputs[handleName];
}

/**
 * Gets information about a connection
 * @param inputName The name of the input
 * @param connectedInputs Record of connected inputs
 * @param connectionSources Record of connection sources
 * @param nodes All nodes in the workflow
 * @returns Connection information
 */
export function getConnectionInfo(
  inputName: string,
  connectedInputs: Record<string, boolean>,
  connectionSources: Record<string, { nodeId: string; handleId: string }>,
  nodes: Node<WorkflowNodeData>[]
): {
  isConnected: boolean;
  sourceNodeId?: string;
  sourceNodeType?: string;
  sourceNodeLabel?: string;
} {
  const isConnected = !!connectedInputs[inputName];
  if (!isConnected) {
    return { isConnected: false };
  }

  const source = connectionSources[inputName];
  if (!source) {
    return { isConnected: true };
  }

  // Find the source node
  const sourceNode = nodes.find((node) => node.id === source.nodeId);
  if (!sourceNode) {
    return { isConnected: true, sourceNodeId: source.nodeId };
  }

  return {
    isConnected: true,
    sourceNodeId: source.nodeId,
    sourceNodeType: sourceNode.data.type,
    sourceNodeLabel: sourceNode.data.label || sourceNode.data.type,
  };
}

/**
 * Finds all connected inputs for a node
 * @param selectedNode The selected node
 * @param edges All edges in the workflow
 * @returns Record of connected inputs and their sources
 */
export function findConnectedInputs(
  selectedNode: Node<WorkflowNodeData> | null,
  edges: Edge[]
): {
  connectedInputs: Record<string, boolean>;
  connectionSources: Record<string, { nodeId: string; handleId: string }>;
} {
  if (!selectedNode) {
    return { connectedInputs: {}, connectionSources: {} };
  }

  // Find all edges that target the selected node
  const nodeInputEdges = edges.filter((edge) => edge.target === selectedNode.id);

  // Create a map of input handle names to connection status
  const connectedHandles: Record<string, boolean> = {};
  const sources: Record<string, { nodeId: string; handleId: string }> = {};

  // Mark all handles as disconnected initially
  if (selectedNode.data.definition?.inputs) {
    selectedNode.data.definition.inputs.forEach((input) => {
      if (input.is_handle) {
        connectedHandles[input.name] = false;
      }
    });

    // Also check for dynamic inputs in the config
    if (selectedNode.data.config?.inputs) {
      selectedNode.data.config.inputs.forEach((input: InputDefinition) => {
        if (input.is_handle) {
          connectedHandles[input.name] = false;
        }
      });
    }
  }

  // Mark connected handles and store connection sources
  nodeInputEdges.forEach((edge) => {
    if (edge.targetHandle) {
      connectedHandles[edge.targetHandle] = true;
      if (edge.source && edge.sourceHandle) {
        sources[edge.targetHandle] = {
          nodeId: edge.source,
          handleId: edge.sourceHandle,
        };
      }
    }
  });

  return { connectedInputs: connectedHandles, connectionSources: sources };
}
