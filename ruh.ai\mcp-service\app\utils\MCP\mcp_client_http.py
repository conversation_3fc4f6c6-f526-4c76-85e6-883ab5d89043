import asyncio
import sys
import argparse
from typing import Optional, Dict, List
from typing import Any, Dict, List, Optional
import logging

DEFAULT_REQUEST_TIMEOUT_SECONDS = 5.0
DEFAULT_MAX_RETRIES = 2
DEFAULT_RETRY_DELAY_SECONDS = 3.0

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


try:
    import mcp.types as types
    from mcp.client.session import ClientSession
    from mcp.client.sse import sse_client
    from mcp.client.streamable_http import streamablehttp_client
except ImportError as e:
    print(f"❌ Missing MCP dependencies. Install with: pip install mcp")
    sys.exit(1)

try:
    import httpx
except ImportError:
    print(f"❌ Missing httpx dependency. Install with: pip install httpx")
    sys.exit(1)


class MCPToolsLister:
    """Simple MCP client focused only on listing tools via HTTP."""

    def __init__(self, server_url, headers=None):
        if not server_url:
            raise ValueError("server_url is required")

        self.server_url = server_url
        self.headers = headers or {}

        # Determine connection type based on URL
        if "/mcp" in server_url:
            self.connection_type = "streamable_http"
        else:
            self.connection_type = "sse"

        self._session = None
        self._context = None

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def connect(self):
        """Connect to MCP server."""

        # Connect via HTTP
        if self.connection_type == "sse":
            self._context = sse_client(self.server_url, headers=self.headers)
        else:
            self._context = streamablehttp_client(self.server_url, headers=self.headers)

        result = await self._context.__aenter__()
        if isinstance(result, tuple):
            self._streams = (result[0], result[1])
        else:
            self._streams = (result, result)

        # Create session
        self._session = ClientSession(self._streams[0], self._streams[1])
        await self._session.__aenter__()
        await self._session.initialize()

    async def list_tools(self) -> List[Dict]:
        """List tools from MCP server."""

        try:
            tools_result = await self._session.list_tools()
            return tools_result
        except Exception as e:
            raise

    async def close(self):
        """Close connections and cleanup."""
        try:
            if self._session:
                await self._session.__aexit__(None, None, None)
            if self._context:
                await self._context.__aexit__(None, None, None)
        except Exception as e:
            raise


async def _get_tools_async_with_retry(
    server_url: str,
    timeout_seconds: float,
    max_retries: int,
    retry_delay_seconds: float,
) -> Optional[Any]:
    """
    Internal async function to connect to the MCP server and retrieve available tools,
    with timeout and retry mechanisms.

    Args:
        server_url: The URL of the MCP server.
        timeout_seconds: Timeout for each attempt.
        max_retries: Maximum number of retries.
        retry_delay_seconds: Delay between retries.

    Returns:
        List of available tools, or None if all attempts fail.
    """
    last_exception = None
    for attempt in range(max_retries + 1):  # +1 for the initial attempt
        try:
            logger.info(f"Attempt {attempt + 1}/{max_retries + 1} to get tools from {server_url}")

            async def _fetch_operation():
                async with MCPToolsLister(server_url) as client:
                    tools = await client.list_tools()
                    return tools

            tools_result = await asyncio.wait_for(_fetch_operation(), timeout=timeout_seconds)
            logger.info(f"Successfully retrieved tools from {server_url} on attempt {attempt + 1}")
            return tools_result

        except asyncio.TimeoutError:
            last_exception = asyncio.TimeoutError(
                f"Timeout after {timeout_seconds}s on attempt {attempt + 1} for {server_url}"
            )
            logger.warning(str(last_exception))
        except ValueError as ve:
            logger.error(f"Invalid server URL '{server_url}': {ve}. Not retrying.")

            return None
        except Exception as e:
            last_exception = e
            logger.error(
                f"Error getting tools from MCP server {server_url} on attempt {attempt + 1}: {e}"
            )

        if attempt < max_retries:
            logger.info(f"Retrying in {retry_delay_seconds} seconds...")
            await asyncio.sleep(retry_delay_seconds)
        else:
            logger.error(
                f"Failed to get tools from {server_url} after {max_retries + 1} attempts. "
                f"Last error: {last_exception}"
            )
            return None

    return None


async def get_mcp_tools(
    server_url: str,
    timeout_seconds: float = DEFAULT_REQUEST_TIMEOUT_SECONDS,
    max_retries: int = DEFAULT_MAX_RETRIES,
    retry_delay_seconds: float = DEFAULT_RETRY_DELAY_SECONDS,
) -> Optional[Any]:
    """
    Connect to the MCP server and return the list of available tools.
    Implements timeout and retry mechanisms.

    Args:
        server_url: The URL of the MCP server (e.g., "http://localhost:8080/sse").
        timeout_seconds: Timeout for each connection/retrieval attempt.
        max_retries: Maximum number of retries.
        retry_delay_seconds: Delay between retries.

    Returns:
        List of available tools, or None if retrieval fails after all retries.
    """
    try:
        return await _get_tools_async_with_retry(
            server_url, timeout_seconds, max_retries, retry_delay_seconds
        )

    except Exception as e:
        logger.error(f"Critical error in get_mcp_tools for {server_url}: {e}", exc_info=True)
        return None


def tools_to_json_response(tools_result: Any) -> Optional[dict]:
    """
    Convert MCP tool result into structured JSON with meta and nextCursor fields.

    Args:
        tools_result: The raw result returned from `get_mcp_tools(server_url)`,
                      typically an object with `meta`, `nextCursor`, and `tools` attributes.
                      Can be None if tool retrieval failed.

    Returns:
        A dictionary that matches the desired JSON structure, or None if input is None.
    """
    if tools_result is None:
        return None

    # If `tools_result` is a class with attributes
    return {
        "meta": getattr(tools_result, "meta", None),
        "nextCursor": getattr(tools_result, "nextCursor", None),
        "tools": [
            {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema,  # Assuming already dict-like
                "annotations": tool.annotations,
            }
            for tool in getattr(tools_result, "tools", [])
        ],
    }


# tools = get_mcp_tools(
#     server_url="https://google-calendar-mcp-dev-624209391722.us-central1.run.app/mcp"
# )
# tools = tools_to_json_response(tools)
# print(tools)
