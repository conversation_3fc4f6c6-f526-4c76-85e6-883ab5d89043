# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM node:lts-alpine

# Create app directory in container
WORKDIR /app

# Copy package.json and package-lock.json if available
COPY package*.json ./

# Install dependencies (ignoring scripts to skip any prepare hooks)
RUN npm install --ignore-scripts

# Copy the rest of the application code
COPY . .

# Expose port if needed (not required for MCP using stdio)

EXPOSE 8081

# Command to run the application
CMD ["npm","run","start"]
