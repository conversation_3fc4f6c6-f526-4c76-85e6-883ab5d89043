# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import authentication_pb2 as authentication__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in authentication_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AuthenticationServiceStub(object):
    """Authentication Service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.InitiateOAuth = channel.unary_unary(
                '/authentication.AuthenticationService/InitiateOAuth',
                request_serializer=authentication__pb2.OAuthAuthorizeRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthAuthorizeResponse.FromString,
                _registered_method=True)
        self.HandleOAuthCallback = channel.unary_unary(
                '/authentication.AuthenticationService/HandleOAuthCallback',
                request_serializer=authentication__pb2.OAuthCallbackRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthCallbackResponse.FromString,
                _registered_method=True)
        self.RefreshOAuthTokens = channel.unary_unary(
                '/authentication.AuthenticationService/RefreshOAuthTokens',
                request_serializer=authentication__pb2.OAuthRefreshRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthRefreshResponse.FromString,
                _registered_method=True)
        self.GetOAuthCredentials = channel.unary_unary(
                '/authentication.AuthenticationService/GetOAuthCredentials',
                request_serializer=authentication__pb2.OAuthCredentialRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthCredentialResponse.FromString,
                _registered_method=True)
        self.GetServerOAuthCredentials = channel.unary_unary(
                '/authentication.AuthenticationService/GetServerOAuthCredentials',
                request_serializer=authentication__pb2.ServerOAuthCredentialRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthCredentialResponse.FromString,
                _registered_method=True)
        self.DeleteOAuthCredentials = channel.unary_unary(
                '/authentication.AuthenticationService/DeleteOAuthCredentials',
                request_serializer=authentication__pb2.DeleteOAuthCredentialRequest.SerializeToString,
                response_deserializer=authentication__pb2.DeleteOAuthCredentialResponse.FromString,
                _registered_method=True)
        self.ListOAuthProviders = channel.unary_unary(
                '/authentication.AuthenticationService/ListOAuthProviders',
                request_serializer=authentication__pb2.OAuthProvidersListRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthProvidersListResponse.FromString,
                _registered_method=True)
        self.GetToolScopes = channel.unary_unary(
                '/authentication.AuthenticationService/GetToolScopes',
                request_serializer=authentication__pb2.OAuthToolScopesRequest.SerializeToString,
                response_deserializer=authentication__pb2.OAuthToolScopesResponse.FromString,
                _registered_method=True)
        self.HealthCheck = channel.unary_unary(
                '/authentication.AuthenticationService/HealthCheck',
                request_serializer=authentication__pb2.HealthCheckRequest.SerializeToString,
                response_deserializer=authentication__pb2.HealthCheckResponse.FromString,
                _registered_method=True)


class AuthenticationServiceServicer(object):
    """Authentication Service
    """

    def InitiateOAuth(self, request, context):
        """OAuth Flow Methods
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HandleOAuthCallback(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RefreshOAuthTokens(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOAuthCredentials(self, request, context):
        """Credential Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetServerOAuthCredentials(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteOAuthCredentials(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListOAuthProviders(self, request, context):
        """Provider Information
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetToolScopes(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HealthCheck(self, request, context):
        """Health Check
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AuthenticationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'InitiateOAuth': grpc.unary_unary_rpc_method_handler(
                    servicer.InitiateOAuth,
                    request_deserializer=authentication__pb2.OAuthAuthorizeRequest.FromString,
                    response_serializer=authentication__pb2.OAuthAuthorizeResponse.SerializeToString,
            ),
            'HandleOAuthCallback': grpc.unary_unary_rpc_method_handler(
                    servicer.HandleOAuthCallback,
                    request_deserializer=authentication__pb2.OAuthCallbackRequest.FromString,
                    response_serializer=authentication__pb2.OAuthCallbackResponse.SerializeToString,
            ),
            'RefreshOAuthTokens': grpc.unary_unary_rpc_method_handler(
                    servicer.RefreshOAuthTokens,
                    request_deserializer=authentication__pb2.OAuthRefreshRequest.FromString,
                    response_serializer=authentication__pb2.OAuthRefreshResponse.SerializeToString,
            ),
            'GetOAuthCredentials': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOAuthCredentials,
                    request_deserializer=authentication__pb2.OAuthCredentialRequest.FromString,
                    response_serializer=authentication__pb2.OAuthCredentialResponse.SerializeToString,
            ),
            'GetServerOAuthCredentials': grpc.unary_unary_rpc_method_handler(
                    servicer.GetServerOAuthCredentials,
                    request_deserializer=authentication__pb2.ServerOAuthCredentialRequest.FromString,
                    response_serializer=authentication__pb2.OAuthCredentialResponse.SerializeToString,
            ),
            'DeleteOAuthCredentials': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteOAuthCredentials,
                    request_deserializer=authentication__pb2.DeleteOAuthCredentialRequest.FromString,
                    response_serializer=authentication__pb2.DeleteOAuthCredentialResponse.SerializeToString,
            ),
            'ListOAuthProviders': grpc.unary_unary_rpc_method_handler(
                    servicer.ListOAuthProviders,
                    request_deserializer=authentication__pb2.OAuthProvidersListRequest.FromString,
                    response_serializer=authentication__pb2.OAuthProvidersListResponse.SerializeToString,
            ),
            'GetToolScopes': grpc.unary_unary_rpc_method_handler(
                    servicer.GetToolScopes,
                    request_deserializer=authentication__pb2.OAuthToolScopesRequest.FromString,
                    response_serializer=authentication__pb2.OAuthToolScopesResponse.SerializeToString,
            ),
            'HealthCheck': grpc.unary_unary_rpc_method_handler(
                    servicer.HealthCheck,
                    request_deserializer=authentication__pb2.HealthCheckRequest.FromString,
                    response_serializer=authentication__pb2.HealthCheckResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'authentication.AuthenticationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('authentication.AuthenticationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AuthenticationService(object):
    """Authentication Service
    """

    @staticmethod
    def InitiateOAuth(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/InitiateOAuth',
            authentication__pb2.OAuthAuthorizeRequest.SerializeToString,
            authentication__pb2.OAuthAuthorizeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HandleOAuthCallback(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/HandleOAuthCallback',
            authentication__pb2.OAuthCallbackRequest.SerializeToString,
            authentication__pb2.OAuthCallbackResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RefreshOAuthTokens(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/RefreshOAuthTokens',
            authentication__pb2.OAuthRefreshRequest.SerializeToString,
            authentication__pb2.OAuthRefreshResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOAuthCredentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/GetOAuthCredentials',
            authentication__pb2.OAuthCredentialRequest.SerializeToString,
            authentication__pb2.OAuthCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetServerOAuthCredentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/GetServerOAuthCredentials',
            authentication__pb2.ServerOAuthCredentialRequest.SerializeToString,
            authentication__pb2.OAuthCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteOAuthCredentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/DeleteOAuthCredentials',
            authentication__pb2.DeleteOAuthCredentialRequest.SerializeToString,
            authentication__pb2.DeleteOAuthCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListOAuthProviders(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/ListOAuthProviders',
            authentication__pb2.OAuthProvidersListRequest.SerializeToString,
            authentication__pb2.OAuthProvidersListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetToolScopes(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/GetToolScopes',
            authentication__pb2.OAuthToolScopesRequest.SerializeToString,
            authentication__pb2.OAuthToolScopesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HealthCheck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/authentication.AuthenticationService/HealthCheck',
            authentication__pb2.HealthCheckRequest.SerializeToString,
            authentication__pb2.HealthCheckResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
