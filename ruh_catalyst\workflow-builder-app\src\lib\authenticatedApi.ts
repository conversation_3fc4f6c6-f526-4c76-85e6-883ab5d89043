/**
 * Authenticated API client for workflow-builder-poc
 * This module provides authenticated versions of the API functions.
 */

import api from "@/utils/axios";

// Note: Using centralized axios instance from @/utils/axios

// Credential types (copied from api.ts)
export interface Credential {
  id: string;
  name: string;
  type: string;
}

export interface CredentialCreate {
  name: string;
  type: string;
  value: string;
}

export interface CredentialListResponse {
  credentials: Credential[];
}

export interface CredentialDeleteResponse {
  success: boolean;
  message?: string;
}

// Authenticated API functions
export const authenticatedApi = {
  /**
   * Fetch all credentials
   */
  fetchCredentials: async (): Promise<CredentialListResponse> => {
    try {
      const response = await api.get("/api/credentials");
      return response.data;
    } catch (error) {
      console.error("Error fetching credentials:", error);
      throw error;
    }
  },

  /**
   * Create a new credential
   * @param credential The credential to create
   */
  createCredential: async (credential: CredentialCreate): Promise<Credential> => {
    try {
      const response = await api.post("/api/credentials", credential);
      return response.data;
    } catch (error) {
      console.error("Error creating credential:", error);
      throw error;
    }
  },

  /**
   * Delete a credential
   * @param credentialId The ID of the credential to delete
   */
  deleteCredential: async (credentialId: string): Promise<CredentialDeleteResponse> => {
    try {
      const response = await api.delete(`/api/credentials/${credentialId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting credential ${credentialId}:`, error);
      throw error;
    }
  },

  /**
   * Save a workflow
   * @param workflow The workflow to save
   */
  saveWorkflow: async (workflow: any): Promise<any> => {
    try {
      const response = await api.post("/api/save_workflow", workflow);
      return response.data;
    } catch (error) {
      console.error("Error saving workflow:", error);
      throw error;
    }
  },

  /**
   * Execute a workflow
   * @param workflow The workflow to execute
   */
  executeWorkflow: async (workflow: any): Promise<any> => {
    try {
      const response = await api.post("/api/execute", workflow);
      return response.data;
    } catch (error) {
      console.error("Error executing workflow:", error);
      throw error;
    }
  },
};

export default authenticatedApi;
