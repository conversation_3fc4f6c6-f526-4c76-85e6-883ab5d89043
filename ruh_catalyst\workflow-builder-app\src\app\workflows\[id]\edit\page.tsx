// This needs to be a server component for Next.js to properly type the params
// We'll create a client component for the actual content

import React from "react";
import { Metadata } from "next";
import WorkflowEditorClient from "./WorkflowEditorClient";

// Define the page props type according to Next.js 15 requirements
type WorkflowEditorPageProps = {
  params: Promise<{
    id: string;
  }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

// Generate metadata for the page
export async function generateMetadata({ params }: WorkflowEditorPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  return {
    title: `Edit Workflow: ${resolvedParams.id}`,
    description: "Edit workflow in the visual editor",
  };
}

// Simple server component that renders the client component
export default async function WorkflowEditorPage({ params }: WorkflowEditorPageProps) {
  // Access the ID directly from params - using await since params is a Promise in Next.js 15
  const resolvedParams = await params;
  const { id } = resolvedParams;

  return <WorkflowEditorClient id={id} />;
}
