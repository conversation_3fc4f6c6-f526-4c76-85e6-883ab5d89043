#!/usr/bin/env python3
"""
Test script to debug MCP protocol communication issues.
"""

import asyncio
import subprocess
import logging
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import get_global_ssh_manager, initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_mcp_protocol_manually(container_id: str, command: str):
    """Test MCP protocol communication manually."""
    logger.info(f"=== Testing MCP Protocol: {container_id} ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    # Test if we can send MCP initialization message
    logger.info("Testing MCP initialization handshake...")
    
    # Create MCP initialization message
    init_message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {
                    "listChanged": True
                },
                "sampling": {}
            },
            "clientInfo": {
                "name": "mcp-executor-test",
                "version": "1.0.0"
            }
        }
    }
    
    init_json = json.dumps(init_message)
    logger.info(f"Sending MCP init message: {init_json}")
    
    # Use echo to send the message and see what happens
    test_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"echo '{init_json}' | timeout 5 docker exec -i {container_id} {command}"
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=15)
        logger.info(f"MCP test exit code: {result.returncode}")
        if result.stdout:
            logger.info(f"MCP test stdout:\n{result.stdout}")
        if result.stderr:
            logger.info(f"MCP test stderr:\n{result.stderr}")
            
        # Check if we got any JSON response
        if result.stdout:
            try:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip().startswith('{'):
                        response = json.loads(line.strip())
                        logger.info(f"Got MCP response: {response}")
                        return True
            except json.JSONDecodeError:
                logger.warning("Response is not valid JSON")
                
    except subprocess.TimeoutExpired:
        logger.info("MCP test timed out (might be normal for interactive servers)")
    except Exception as e:
        logger.error(f"Error testing MCP protocol: {e}")
    
    return False


async def test_container_logs(container_id: str):
    """Check container logs for any error messages."""
    logger.info(f"=== Checking Container Logs: {container_id} ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    logs_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker logs --tail 20 {container_id}"
    ]
    
    try:
        result = subprocess.run(logs_cmd, capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            logger.info(f"Container logs:\n{result.stdout}")
            if result.stderr:
                logger.info(f"Container stderr:\n{result.stderr}")
        else:
            logger.error(f"Failed to get logs: {result.stderr}")
    except Exception as e:
        logger.error(f"Error getting container logs: {e}")


async def test_container_processes(container_id: str):
    """Check what processes are running in the container."""
    logger.info(f"=== Checking Container Processes: {container_id} ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    ps_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=10",
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec {container_id} ps aux"
    ]
    
    try:
        result = subprocess.run(ps_cmd, capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            logger.info(f"Container processes:\n{result.stdout}")
        else:
            logger.error(f"Failed to get processes: {result.stderr}")
    except Exception as e:
        logger.error(f"Error getting container processes: {e}")


async def main():
    """Main test function."""
    logger.info("Starting MCP Protocol Debug Tests")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        logger.info("Initializing SSH key...")
        initialize_global_ssh_key(settings.ssh_key_content)
    else:
        logger.error("No SSH key content found in settings")
        return
    
    # Test containers with their detected commands
    test_cases = [
        ("ff5d4995-d431-4566-a843-59fee0521b15_91a237fd-0225-4e02-9e9f-805eff073b07", "uv run weather.py"),
        ("35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07", "node dist/index.js"),
    ]
    
    for container_id, command in test_cases:
        logger.info(f"\n{'='*80}")
        logger.info(f"Testing container: {container_id}")
        logger.info(f"Command: {command}")
        logger.info(f"{'='*80}")
        
        # Check container logs first
        await test_container_logs(container_id)
        
        # Check running processes
        await test_container_processes(container_id)
        
        # Test MCP protocol
        await test_mcp_protocol_manually(container_id, command)
    
    logger.info("MCP Protocol debug tests completed")


if __name__ == "__main__":
    asyncio.run(main())
