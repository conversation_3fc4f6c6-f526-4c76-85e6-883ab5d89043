#!/usr/bin/env python3
"""
Test script to verify the fix for transition-specific parameter application issue.
This script tests that transition-specific values are NOT applied to wrong transitions.
"""

import json


def _parse_json_if_needed(value, field_name, transition, workflow):
    """
    Parse JSON string if the field is expected to be an object type.
    Simplified version for testing.
    """
    # Only process string values that look like JSON
    if not isinstance(value, str) or not (
        value.strip().startswith("{") or value.strip().startswith("[")
    ):
        return value

    # For testing, just try to parse JSON
    try:
        return json.loads(value)
    except (json.JSONDecodeError, ValueError):
        return value


def _has_handle_mapping_for_field(field_name, transition, workflow):
    """
    Check if a field has a corresponding handle mapping defined in the workflow.
    Simplified version for testing.
    """
    return False  # For testing, assume no handle mappings


def initialize_workflow_with_params(workflow, params):
    """
    Simplified version of the initialize_workflow_with_params function for testing.
    """
    user_dependent_fields = params["payload"]["user_dependent_fields"]
    user_payload_template = params["payload"]["user_payload_template"]
    global_context_values = user_payload_template.get("global_context_defs", {})

    # Create a mapping of transition IDs to transitions for quick lookup
    transitions_by_id = {
        transition.get("id"): transition
        for transition in workflow.get("transitions", [])
    }

    # Process tool parameters
    for transition in workflow.get("transitions", []):
        transition_id = transition.get("id")
        # Handle tool parameters
        for tool in transition.get("node_info", {}).get("tools_to_use", []):
            for item in tool.get("tool_params", {}).get("items", []):
                field_name = item.get("field_name")
                field_value = item.get("field_value")

                # Check if this field has a value with a specific transition ID
                field_updated = False

                # Check for field name match
                if field_name in user_payload_template:
                    payload_value = user_payload_template[field_name]

                    # Check if the payload value is a dictionary with transition_id
                    if (
                        isinstance(payload_value, dict)
                        and "value" in payload_value
                        and "transition_id" in payload_value
                    ):
                        # If this is the specified transition, update the field
                        if payload_value["transition_id"] == transition_id:
                            extracted_value = payload_value["value"]

                            # Check if this is a JSON string that should be parsed for object fields
                            parsed_value = _parse_json_if_needed(
                                extracted_value, field_name, transition, workflow
                            )

                            item["field_value"] = parsed_value
                            field_updated = True
                        # If transition_id doesn't match, don't apply this value to current transition
                        # The value is intended for a different transition, so skip it
                    else:
                        # For simple values (not transition-specific), update as before
                        # Check if this is a JSON string that should be parsed for object fields
                        parsed_value = _parse_json_if_needed(
                            payload_value, field_name, transition, workflow
                        )

                        item["field_value"] = parsed_value
                        field_updated = True

                # Handle user-dependent fields that weren't updated by transition ID
                if not field_updated and field_name in user_dependent_fields:
                    # Try to find a value for this field that might be in a different format
                    if field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]

                        # If it's a dict with transition_id, only apply if transition doesn't exist
                        # (fallback case) or if it's a simple value (not transition-specific)
                        if (
                            isinstance(payload_value, dict)
                            and "value" in payload_value
                            and "transition_id" in payload_value
                        ):
                            # Check if the specified transition exists
                            target_transition_id = payload_value.get("transition_id")
                            if target_transition_id not in transitions_by_id:
                                # If transition doesn't exist, use the value as fallback
                                item["field_value"] = payload_value["value"]
                                field_updated = True
                            # If transition exists but doesn't match current transition, don't apply
                        else:
                            # For simple values (not transition-specific), apply to all matching fields
                            parsed_value = _parse_json_if_needed(
                                payload_value, field_name, transition, workflow
                            )
                            item["field_value"] = parsed_value
                            field_updated = True

                    # If still not updated and it's a required field, raise error
                    # Only raise error if we've checked all transitions and none have been updated
                    # We'll track this separately and check at the end of processing all transitions
                    if not field_updated and field_name not in user_payload_template:
                        raise ValueError(
                            f"Missing value for required user-dependent field: '{field_name}'"
                        )

                # Handle fields with None values that weren't updated yet
                elif not field_updated and field_value is None:
                    if field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]

                        # If it's a dict with transition_id, only apply if transition doesn't exist
                        # (fallback case) or if it's a simple value (not transition-specific)
                        if (
                            isinstance(payload_value, dict)
                            and "value" in payload_value
                            and "transition_id" in payload_value
                        ):
                            # Check if the specified transition exists
                            target_transition_id = payload_value.get("transition_id")
                            if target_transition_id not in transitions_by_id:
                                # If transition doesn't exist, use the value as fallback
                                extracted_value = payload_value["value"]
                                parsed_value = _parse_json_if_needed(
                                    extracted_value, field_name, transition, workflow
                                )
                                item["field_value"] = parsed_value
                            # If transition exists but doesn't match current transition, don't apply
                        else:
                            # For simple values (not transition-specific), apply to all matching fields
                            parsed_value = _parse_json_if_needed(
                                payload_value, field_name, transition, workflow
                            )
                            item["field_value"] = parsed_value

    return workflow


def test_transition_specific_field_not_applied_to_wrong_transition():
    """
    Test that transition-specific fields are NOT applied to transitions with different IDs.
    """
    print("Testing transition-specific field application fix...")

    workflow = {
        "transitions": [
            {
                "id": "transition-1",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [{"field_name": "topic", "field_value": None}]
                            }
                        }
                    ]
                },
            },
            {
                "id": "transition-2",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [{"field_name": "topic", "field_value": None}]
                            }
                        }
                    ]
                },
            },
        ]
    }

    params = {
        "payload": {
            "user_dependent_fields": ["topic"],
            "user_payload_template": {
                "topic": {
                    "value": "test topic for transition 2",
                    "transition_id": "transition-2",
                }
            },
        }
    }

    result = initialize_workflow_with_params(workflow, params)

    # The field in transition-1 should remain None
    transition1_topic = result["transitions"][0]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]

    # The field in transition-2 should be updated
    transition2_topic = result["transitions"][1]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]

    print(f"Transition 1 topic value: {transition1_topic}")
    print(f"Transition 2 topic value: {transition2_topic}")

    # Verify the fix
    assert (
        transition1_topic is None
    ), f"Expected transition 1 topic to be None, got: {transition1_topic}"
    assert (
        transition2_topic == "test topic for transition 2"
    ), f"Expected transition 2 topic to be 'test topic for transition 2', got: {transition2_topic}"

    print(
        "✅ Test PASSED: Transition-specific values are correctly applied only to the intended transition!"
    )
    return True


def test_user_specific_payload():
    """
    Test with the user's specific payload to ensure it works correctly.
    """
    print("\nTesting with user's specific payload...")

    # Create a workflow that matches the user's scenario
    workflow = {
        "transitions": [
            {
                "id": "ApiRequestNode-1748853965235",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [
                                    {"field_name": "url", "field_value": None},
                                    {"field_name": "body", "field_value": None},
                                ]
                            }
                        }
                    ]
                },
            },
            {
                "id": "ApiRequestNode-1748854246219",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [
                                    {"field_name": "url", "field_value": None},
                                    {"field_name": "body", "field_value": None},
                                ]
                            }
                        }
                    ]
                },
            },
            {
                "id": "SomeOtherNode-123456789",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [
                                    {"field_name": "url", "field_value": None},
                                    {"field_name": "body", "field_value": None},
                                ]
                            }
                        }
                    ]
                },
            },
        ]
    }

    # User's actual payload
    params = {
        "payload": {
            "user_dependent_fields": ["url", "body"],
            "user_payload_template": {
                "url": {
                    "value": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
                    "transition_id": "ApiRequestNode-1748853965235",
                },
                "body": {
                    "value": {
                        "title": "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai",
                        "summary": "Delve into the transformative impact of AI agent workflows in enhancing business efficiency.",
                        "category": "AI Innovation",
                    },
                    "transition_id": "ApiRequestNode-1748854246219",
                },
            },
        }
    }

    result = initialize_workflow_with_params(workflow, params)

    # Check results for each transition
    transition1_url = result["transitions"][0]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition1_body = result["transitions"][0]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][1]["field_value"]

    transition2_url = result["transitions"][1]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition2_body = result["transitions"][1]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][1]["field_value"]

    transition3_url = result["transitions"][2]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition3_body = result["transitions"][2]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][1]["field_value"]

    print(
        f"Transition 1 (ApiRequestNode-1748853965235) - URL: {transition1_url}, Body: {transition1_body}"
    )
    print(
        f"Transition 2 (ApiRequestNode-1748854246219) - URL: {transition2_url}, Body: {transition2_body}"
    )
    print(
        f"Transition 3 (SomeOtherNode-123456789) - URL: {transition3_url}, Body: {transition3_body}"
    )

    # Verify the fix
    # Transition 1 should have URL but not body
    assert (
        transition1_url
        == "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login"
    ), f"Expected transition 1 to have URL, got: {transition1_url}"
    assert (
        transition1_body is None
    ), f"Expected transition 1 body to be None, got: {transition1_body}"

    # Transition 2 should have body but not URL
    assert (
        transition2_url is None
    ), f"Expected transition 2 URL to be None, got: {transition2_url}"
    assert (
        transition2_body is not None
    ), f"Expected transition 2 to have body, got: {transition2_body}"
    assert (
        transition2_body["title"]
        == "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai"
    ), f"Expected transition 2 body title to match, got: {transition2_body}"

    # Transition 3 should have neither
    assert (
        transition3_url is None
    ), f"Expected transition 3 URL to be None, got: {transition3_url}"
    assert (
        transition3_body is None
    ), f"Expected transition 3 body to be None, got: {transition3_body}"

    print(
        "✅ User payload test PASSED: Transition-specific values are correctly applied only to their intended transitions!"
    )
    return True


if __name__ == "__main__":
    try:
        print("=" * 60)
        print("TESTING TRANSITION-SPECIFIC PARAMETER APPLICATION FIX")
        print("=" * 60)

        # Test the main fix
        test_transition_specific_field_not_applied_to_wrong_transition()

        # Test with user's specific payload
        test_user_specific_payload()

        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
        print("=" * 60)

    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        print("=" * 60)
        import traceback

        traceback.print_exc()
