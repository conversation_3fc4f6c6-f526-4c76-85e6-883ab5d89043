/**
 * Next.js middleware for route protection
 * Matches the implementation in ruh-app-fe for consistency.
 */

import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { workflowsRoute, homeRoute, loginRoute, publicRoutes } from "@/shared/routes";

export function middleware(request: NextRequest) {
  // TEMPORARY: Uncomment this line to bypass all auth checks for testing
  // return NextResponse.next();

  const path = request.nextUrl.pathname;

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(
    (route) =>
      // For exact matches (like login, signup)
      path === route ||
      // For routes with parameters (like reset-password/[token])
      (route.startsWith("/") && path.startsWith(route)),
  );

  // Check if the user has an auth token
  const hasAuthTokens = request.cookies.has("accessToken") || request.cookies.has("refreshToken");

  // If user is logged in and trying to access auth/public pages, redirect to workflows page
  if (isPublicRoute && hasAuthTokens) {
    return NextResponse.redirect(new URL(workflowsRoute, request.url));
  }
  // If user is not logged in and trying to access any non-public route, redirect to login
  if (!isPublicRoute && !hasAuthTokens) {
    // Add the current path as a callback URL for post-login redirection
    const loginUrl = new URL(loginRoute, request.url);
    loginUrl.searchParams.set("callbackUrl", path);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

// Configure middleware to run on all routes except api, _next/static, etc.
export const config = {
  matcher: ["/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"],
};
