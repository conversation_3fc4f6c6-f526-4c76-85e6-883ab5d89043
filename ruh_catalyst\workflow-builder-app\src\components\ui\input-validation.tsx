import React from "react";
import { AlertCircle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface InputValidationProps {
  isValid?: boolean;
  message?: string;
  showSuccess?: boolean;
  className?: string;
}

/**
 * A component for displaying input validation errors or success messages
 */
export function InputValidation({
  isValid = true,
  message,
  showSuccess = false,
  className,
}: InputValidationProps) {
  // If there's no message or the input is valid and we don't want to show success messages, don't render anything
  if (!message || (isValid && !showSuccess)) {
    return null;
  }

  return (
    <div
      className={cn(
        "mt-1 flex items-center gap-1.5 text-xs",
        isValid ? "text-success" : "text-destructive",
        className,
      )}
    >
      {isValid ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
      <span>{message}</span>
    </div>
  );
}

interface ValidationRulesProps {
  rules: {
    name: string;
    isValid: boolean;
    message?: string;
  }[];
  className?: string;
}

/**
 * A component for displaying a list of validation rules
 */
export function ValidationRules({ rules, className }: ValidationRulesProps) {
  if (!rules || rules.length === 0) {
    return null;
  }

  return (
    <div className={cn("mt-2 space-y-1", className)}>
      {rules.map((rule) => (
        <div
          key={rule.name}
          className={cn(
            "flex items-center gap-1.5 text-xs",
            rule.isValid ? "text-success" : "text-muted-foreground",
          )}
        >
          {rule.isValid ? (
            <CheckCircle className="h-3 w-3" />
          ) : (
            <div className="border-muted-foreground/50 h-3 w-3 rounded-full border" />
          )}
          <span>{rule.message || rule.name}</span>
        </div>
      ))}
    </div>
  );
}

interface ValidatedInputWrapperProps {
  children: React.ReactNode;
  isValid?: boolean;
  message?: string;
  showSuccess?: boolean;
  validationRules?: {
    name: string;
    isValid: boolean;
    message?: string;
  }[];
  showRules?: boolean;
  className?: string;
}

/**
 * A wrapper component for inputs that need validation
 */
export function ValidatedInputWrapper({
  children,
  isValid = true,
  message,
  showSuccess = false,
  validationRules,
  showRules = false,
  className,
}: ValidatedInputWrapperProps) {
  return (
    <div className={cn("space-y-1", className)}>
      {children}
      <InputValidation isValid={isValid} message={message} showSuccess={showSuccess} />
      {showRules && validationRules && <ValidationRules rules={validationRules} />}
    </div>
  );
}
