import { describe, it, expect } from 'vitest';
import { isStartNode, findStartNode } from '../utils';
import { isFieldRequired } from '../fieldValidation';
import { Node } from 'reactflow';
import { WorkflowNodeData, InputDefinition } from '@/types';

// Mock the isMCPMarketplaceComponent function since it's internal to fieldValidation.ts
// We'll test it separately
jest.mock('../fieldValidation', () => {
  const original = jest.requireActual('../fieldValidation');
  return {
    ...original,
    // Expose the internal function for testing
    isMCPMarketplaceComponent: (node: Node<WorkflowNodeData>) => {
      if (!node || !node.data) return false;
    
      // Check various indicators that this is an MCP component
      return (
        node.data.type === "mcp" ||
        node.data.originalType === "MCPMarketplaceComponent" ||
        node.data.type === "MCPMarketplaceComponent" ||
        (node.data.definition && node.data.definition.type === "MCPMarketplaceComponent") ||
        (node.data.definition && node.data.definition.mcp_info) ||
        (node.data.definition && node.data.definition.path &&
         (node.data.definition.path.includes("mcp_marketplace") ||
          node.data.definition.path.includes("components.mcp")))
      );
    }
  };
});

// Import the mocked function
import { isMCPMarketplaceComponent } from '../fieldValidation';

describe('isStartNode', () => {
  it('should identify a node as a StartNode by originalType', () => {
    const node = {
      id: 'node1',
      data: {
        originalType: 'StartNode',
        type: 'someType'
      }
    } as Node<WorkflowNodeData>;
    
    expect(isStartNode(node)).toBe(true);
  });

  it('should identify a node as a StartNode by definition name', () => {
    const node = {
      id: 'node1',
      data: {
        type: 'someType',
        definition: {
          name: 'StartNode'
        }
      }
    } as Node<WorkflowNodeData>;
    
    expect(isStartNode(node)).toBe(true);
  });

  it('should return false for non-StartNode nodes', () => {
    const node = {
      id: 'node1',
      data: {
        type: 'someType',
        originalType: 'someOtherType',
        definition: {
          name: 'someOtherName'
        }
      }
    } as Node<WorkflowNodeData>;
    
    expect(isStartNode(node)).toBe(false);
  });

  it('should return false for nodes without data', () => {
    const node = {
      id: 'node1'
    } as Node<WorkflowNodeData>;
    
    expect(isStartNode(node)).toBe(false);
  });
});

describe('findStartNode', () => {
  it('should find a StartNode in an array of nodes', () => {
    const nodes = [
      {
        id: 'node1',
        data: {
          type: 'someType'
        }
      },
      {
        id: 'node2',
        data: {
          originalType: 'StartNode'
        }
      },
      {
        id: 'node3',
        data: {
          type: 'someOtherType'
        }
      }
    ] as Node<WorkflowNodeData>[];
    
    const startNode = findStartNode(nodes);
    expect(startNode).toBeDefined();
    expect(startNode?.id).toBe('node2');
  });

  it('should return undefined if no StartNode is found', () => {
    const nodes = [
      {
        id: 'node1',
        data: {
          type: 'someType'
        }
      },
      {
        id: 'node3',
        data: {
          type: 'someOtherType'
        }
      }
    ] as Node<WorkflowNodeData>[];
    
    const startNode = findStartNode(nodes);
    expect(startNode).toBeUndefined();
  });
});

describe('isMCPMarketplaceComponent', () => {
  it('should identify a node as an MCP component by type', () => {
    const node = {
      id: 'node1',
      data: {
        type: 'mcp'
      }
    } as Node<WorkflowNodeData>;
    
    expect(isMCPMarketplaceComponent(node)).toBe(true);
  });

  it('should identify a node as an MCP component by originalType', () => {
    const node = {
      id: 'node1',
      data: {
        originalType: 'MCPMarketplaceComponent'
      }
    } as Node<WorkflowNodeData>;
    
    expect(isMCPMarketplaceComponent(node)).toBe(true);
  });

  it('should identify a node as an MCP component by definition type', () => {
    const node = {
      id: 'node1',
      data: {
        definition: {
          type: 'MCPMarketplaceComponent'
        }
      }
    } as Node<WorkflowNodeData>;
    
    expect(isMCPMarketplaceComponent(node)).toBe(true);
  });

  it('should identify a node as an MCP component by mcp_info', () => {
    const node = {
      id: 'node1',
      data: {
        definition: {
          mcp_info: {}
        }
      }
    } as Node<WorkflowNodeData>;
    
    expect(isMCPMarketplaceComponent(node)).toBe(true);
  });

  it('should identify a node as an MCP component by path', () => {
    const node = {
      id: 'node1',
      data: {
        definition: {
          path: 'some/path/mcp_marketplace/component'
        }
      }
    } as Node<WorkflowNodeData>;
    
    expect(isMCPMarketplaceComponent(node)).toBe(true);
  });

  it('should return false for non-MCP components', () => {
    const node = {
      id: 'node1',
      data: {
        type: 'someType',
        originalType: 'someOtherType',
        definition: {
          type: 'someOtherType',
          path: 'some/other/path'
        }
      }
    } as Node<WorkflowNodeData>;
    
    expect(isMCPMarketplaceComponent(node)).toBe(false);
  });
});

describe('isFieldRequired', () => {
  // Create a mock node for testing
  const createMockNode = (isMCP: boolean): Node<WorkflowNodeData> => ({
    id: 'node1',
    data: {
      type: isMCP ? 'mcp' : 'standard',
      label: isMCP ? 'MCP Component' : 'Standard Component',
      definition: {
        inputs: []
      }
    }
  } as Node<WorkflowNodeData>);

  // Create a mock input definition for testing
  const createMockInput = (name: string, required?: boolean, isHandle?: boolean): InputDefinition => ({
    name,
    display_name: name,
    input_type: 'string',
    required,
    is_handle: isHandle
  });

  it('should return true for explicitly required fields', () => {
    const node = createMockNode(false);
    const input = createMockInput('field1', true);
    
    expect(isFieldRequired(node, input)).toBe(true);
  });

  it('should return false for explicitly optional fields', () => {
    const node = createMockNode(false);
    const input = createMockInput('field1', false);
    
    expect(isFieldRequired(node, input)).toBe(false);
  });

  it('should return false for handle inputs', () => {
    const node = createMockNode(false);
    const input = createMockInput('field1', true, true);
    
    expect(isFieldRequired(node, input)).toBe(false);
  });

  it('should return false for inputs with names ending with _handle', () => {
    const node = createMockNode(false);
    const input = createMockInput('field1_handle', true);
    
    expect(isFieldRequired(node, input)).toBe(false);
  });

  it('should handle MCP components differently - treat fields as required by default', () => {
    const node = createMockNode(true);
    const input = createMockInput('field1');
    
    expect(isFieldRequired(node, input)).toBe(true);
  });

  it('should handle MCP components - respect explicitly optional fields', () => {
    const node = createMockNode(true);
    const input = createMockInput('field1', false);
    
    expect(isFieldRequired(node, input)).toBe(false);
  });

  it('should handle MCP components - common optional fields', () => {
    const node = createMockNode(true);
    const input = createMockInput('api_key');
    
    expect(isFieldRequired(node, input)).toBe(false);
  });
});
