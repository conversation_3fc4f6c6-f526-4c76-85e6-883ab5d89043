"""
Test suite for ConditionalRoutingHandler - handles routing decisions from conditional components.

This module tests the conditional routing handler functionality:
- Detection of conditional component execution results
- Extraction of routing decisions from component results
- Processing routing decisions to determine next transitions
- Error handling for malformed or missing routing decisions

Following TDD methodology - Phase 2 Cycle 1: Conditional Component Detection and Result Processing
"""

import pytest
import sys
import os
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any, List

# Add the app directory to the Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core_.conditional_routing_handler import ConditionalRoutingHandler


class TestConditionalRoutingHandler:
    """Test suite for conditional routing handler functionality."""
    
    @pytest.fixture
    def mock_logger(self):
        """Fixture providing a mock logger for testing."""
        logger = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.debug = Mock()
        return logger
    
    @pytest.fixture
    def routing_handler(self, mock_logger):
        """Fixture providing ConditionalRouting<PERSON>andler instance for testing."""
        return ConditionalRoutingHandler(logger=mock_logger)
    
    def test_detects_conditional_component_result_with_routing_decision(self, routing_handler):
        """
        Test detection of conditional component execution results.
        
        Expected to FAIL initially until ConditionalRoutingHandler is implemented.
        """
        component_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-success",
                "matched_condition": 1,
                "condition_result": True,
                "execution_time_ms": 5.2
            },
            "metadata": {
                "total_conditions": 3,
                "evaluation_order": 1
            }
        }
        
        is_conditional = routing_handler.is_conditional_component_result(component_result)
        assert is_conditional is True, \
            "Should detect conditional component result by presence of routing_decision"
    
    def test_does_not_detect_regular_component_result(self, routing_handler):
        """
        Test that regular component results are not detected as conditional.
        
        Expected to FAIL initially until ConditionalRoutingHandler is implemented.
        """
        regular_result = {
            "status": "success",
            "result": "Some regular component output",
            "execution_time": 10.5
        }
        
        is_conditional = routing_handler.is_conditional_component_result(regular_result)
        assert is_conditional is False, \
            "Should not detect regular component result as conditional"
    
    def test_detects_conditional_component_result_with_error_status(self, routing_handler):
        """
        Test detection of conditional component results even with error status.
        
        Expected to FAIL initially until ConditionalRoutingHandler is implemented.
        """
        error_result = {
            "status": "error",
            "error": "Validation failed",
            "routing_decision": {
                "target_transition": "transition-default",
                "matched_condition": None,
                "condition_result": False,
                "execution_time_ms": 2.1
            }
        }
        
        is_conditional = routing_handler.is_conditional_component_result(error_result)
        assert is_conditional is True, \
            "Should detect conditional component result even with error status"
    
    @pytest.mark.asyncio
    async def test_extracts_routing_decision_from_successful_result(self, routing_handler):
        """
        Test extraction of routing decision from successful component result.
        
        Expected to FAIL initially until routing decision handling is implemented.
        """
        component_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-premium",
                "matched_condition": 2,
                "condition_result": True,
                "execution_time_ms": 3.7
            },
            "metadata": {
                "total_conditions": 5,
                "evaluation_order": 2
            }
        }
        
        mock_transition = {
            "id": "transition-conditional-1",
            "execution_type": "Component"
        }
        
        next_transitions = await routing_handler.handle_conditional_result(
            component_result, mock_transition
        )
        
        assert next_transitions == ["transition-premium"], \
            "Should extract target transition from routing decision"
    
    @pytest.mark.asyncio
    async def test_extracts_routing_decision_from_error_result_with_default(self, routing_handler):
        """
        Test extraction of routing decision from error result with default transition.
        
        Expected to FAIL initially until error handling is implemented.
        """
        error_result = {
            "status": "error",
            "error": "Component execution failed",
            "routing_decision": {
                "target_transition": "transition-error-recovery",
                "matched_condition": None,
                "condition_result": False,
                "execution_time_ms": 1.5
            }
        }
        
        mock_transition = {
            "id": "transition-conditional-error",
            "execution_type": "Component"
        }
        
        next_transitions = await routing_handler.handle_conditional_result(
            error_result, mock_transition
        )
        
        assert next_transitions == ["transition-error-recovery"], \
            "Should extract default transition from error result"
    
    @pytest.mark.asyncio
    async def test_handles_missing_routing_decision_gracefully(self, routing_handler):
        """
        Test graceful handling of results missing routing decision.
        
        Expected to FAIL initially until error handling is implemented.
        """
        malformed_result = {
            "status": "success",
            "result": "Some output but no routing decision"
        }
        
        mock_transition = {
            "id": "transition-malformed",
            "execution_type": "Component"
        }
        
        next_transitions = await routing_handler.handle_conditional_result(
            malformed_result, mock_transition
        )
        
        assert next_transitions == [], \
            "Should return empty list when routing decision is missing"
    
    @pytest.mark.asyncio
    async def test_handles_missing_target_transition_gracefully(self, routing_handler):
        """
        Test graceful handling of routing decision missing target transition.
        
        Expected to FAIL initially until error handling is implemented.
        """
        incomplete_result = {
            "status": "success",
            "routing_decision": {
                "matched_condition": 1,
                "condition_result": True,
                "execution_time_ms": 4.2
                # Missing target_transition
            }
        }
        
        mock_transition = {
            "id": "transition-incomplete",
            "execution_type": "Component"
        }
        
        next_transitions = await routing_handler.handle_conditional_result(
            incomplete_result, mock_transition
        )
        
        assert next_transitions == [], \
            "Should return empty list when target_transition is missing"
    
    @pytest.mark.asyncio
    async def test_logs_routing_decision_processing(self, routing_handler, mock_logger):
        """
        Test that routing decision processing is properly logged.
        
        Expected to FAIL initially until logging is implemented.
        """
        component_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-logged",
                "matched_condition": 3,
                "condition_result": True,
                "execution_time_ms": 6.1
            }
        }
        
        mock_transition = {
            "id": "transition-logging-test",
            "execution_type": "Component"
        }
        
        next_transitions = await routing_handler.handle_conditional_result(
            component_result, mock_transition
        )
        
        # Verify logging calls
        mock_logger.info.assert_called()
        assert next_transitions == ["transition-logged"]
    
    @pytest.mark.asyncio
    async def test_handles_null_routing_decision_gracefully(self, routing_handler):
        """
        Test graceful handling of null routing decision.
        
        Expected to FAIL initially until null handling is implemented.
        """
        null_result = {
            "status": "success",
            "routing_decision": None
        }
        
        mock_transition = {
            "id": "transition-null",
            "execution_type": "Component"
        }
        
        next_transitions = await routing_handler.handle_conditional_result(
            null_result, mock_transition
        )
        
        assert next_transitions == [], \
            "Should return empty list when routing_decision is null"


class TestConditionalRoutingHandlerPerformance:
    """Test suite for conditional routing handler performance."""
    
    @pytest.fixture
    def mock_logger(self):
        """Fixture providing a mock logger for testing."""
        return Mock()
    
    @pytest.fixture
    def routing_handler(self, mock_logger):
        """Fixture providing ConditionalRoutingHandler instance for testing."""
        return ConditionalRoutingHandler(logger=mock_logger)
    
    @pytest.mark.asyncio
    async def test_routing_decision_processing_performance_under_10ms(self, routing_handler):
        """
        Test that routing decision processing completes within 10ms.
        
        Performance requirement: Routing decision processing should complete in <10ms.
        Expected to FAIL initially until optimized implementation is complete.
        """
        import time
        
        component_result = {
            "status": "success",
            "routing_decision": {
                "target_transition": "transition-performance-test",
                "matched_condition": 1,
                "condition_result": True,
                "execution_time_ms": 2.3
            },
            "metadata": {
                "total_conditions": 10,
                "evaluation_order": 1
            }
        }
        
        mock_transition = {
            "id": "transition-performance",
            "execution_type": "Component"
        }
        
        # Test multiple iterations for performance consistency
        execution_times = []
        
        for i in range(10):
            start_time = time.time()
            next_transitions = await routing_handler.handle_conditional_result(
                component_result, mock_transition
            )
            end_time = time.time()
            
            execution_time_ms = (end_time - start_time) * 1000
            execution_times.append(execution_time_ms)
            
            assert next_transitions == ["transition-performance-test"]
        
        avg_execution_time = sum(execution_times) / len(execution_times)
        max_execution_time = max(execution_times)
        
        assert avg_execution_time < 10.0, \
            f"Average routing processing time {avg_execution_time:.2f}ms should be <10ms"
        
        assert max_execution_time < 10.0, \
            f"Maximum routing processing time {max_execution_time:.2f}ms should be <10ms"


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
