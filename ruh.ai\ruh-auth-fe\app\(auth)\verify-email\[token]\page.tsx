"use client";

import { useEffect, use } from "react";
import { useMutation } from "@tanstack/react-query";
import { authApi } from "@/app/api/auth";
import Link from "next/link";
import { loginRoute } from "@/app/shared/routes";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, LoaderIcon, XCircle } from "lucide-react";
import { Logo } from "@/app/shared/Logo";

export default function VerifyEmailPage({
  params,
}: {
  params: Promise<{ token: string }>;
}) {
  const resolvedParams = use(params);
  const { token } = resolvedParams;

  const {
    mutate: verifyEmail,
    isPending,
    isSuccess,
    isError,
    error,
    data,
  } = useMutation({
    mutationFn: authApi.verifyEmailOtp,
  });

  // Automatically trigger the verification when the component mounts
  useEffect(() => {
    if (token) {
      verifyEmail(token);
    }
  }, [token, verifyEmail]);

  return (
    <div className="min-h-screen flex items-center justify-center ">
      {/* Left Side - Verification Status */}
      <div className="min-w-[400px] sm:min-w-[500px] border-2 border-brand-stroke  p-8 md:p-12 lg:p-16 flex flex-col items-center justify-center gap-10 bg-brand-overlay border-r border-gray-200/90 dark:border-brand-card rounded-xl">
        <div>
          <Logo />
        </div>

        <div className="max-w-md w-full flex flex-col justify-center gap-10">
          <div className="flex flex-col justify-center gap-16">
            <div className="flex flex-col items-center justify-center  rounded-lg bg-card">
              {isPending && (
                <div className="flex flex-col items-center gap-10">
                  <LoaderIcon className="w-12 h-12 animate-spin text-brand-primary" />
                  <p className="text-brand-primary-font font-medium font-primary">
                    Verifying your email address...
                  </p>
                </div>
              )}

              {isSuccess && (
                <div className="flex flex-col items-center gap-10">
                  <h1 className="font-primary font-bold text-xl text-brand-primary-font">
                    YOUR ACCOUNT IS ACTIVATED!
                  </h1>
                  <Button
                    asChild
                    variant="default"
                    className="bg-brand-primary hover:bg-brand-primary/90 text-white w-full"
                  >
                    <Link href={loginRoute}>Enter</Link>
                  </Button>
                </div>
              )}

              {isError && (
                <div className="flex flex-col items-center gap-10">
                  <h1 className="font-primary font-bold text-lg text-brand-secondary-font text-center">
                    Something went wrong. Please request a new verification link
                    or contact support.
                  </h1>
                  <Button
                    asChild
                    variant="default"
                    className="bg-brand-primary hover:bg-brand-primary/90 text-white w-full"
                  >
                    <Link href={loginRoute}>Back to Login</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
