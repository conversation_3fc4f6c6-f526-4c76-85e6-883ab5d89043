diff --git a/frontend/src/components/layout/InspectorPanel.tsx b/frontend/src/components/layout/InspectorPanel.tsx
index 1234567..abcdef0 100644
--- a/frontend/src/components/layout/InspectorPanel.tsx
+++ b/frontend/src/components/layout/InspectorPanel.tsx
@@ -334,11 +334,11 @@ export function InspectorPanel({
       // For inputs with is_handle=true, always show them unless they're connected
       if (inputDef.is_handle) {
         // Check if this input is connected
-        const isInputDirectlyConnected = isInputConnected(inputDef.name);
+        // const isInputDirectlyConnected = isInputConnected(inputDef.name);
 
         // If the input is connected, show it as a handle
         // If not connected, show it as a regular input
-        return true;
+        return true; // Always show inputs with is_handle=true
       }
 
       // For regular inputs, check if there's a corresponding handle
@@ -1484,7 +1484,7 @@ export function InspectorPanel({
       // Handle input type for connection handles
       case 'handle':
         // Special handling for MCP Marketplace components
-        if (isMCPMarketplaceComponent(selectedNode) && inputDef.is_handle && inputDef.input_type !== 'handle') {
+        if ((isMCPMarketplaceComponent(selectedNode) || inputDef.is_handle) && inputDef.input_type !== 'handle') {
           // For MCP Marketplace components, render the input based on its original type
           // but still check if it's connected
           const isHandleConnected = isInputConnected(inputDef.name);
@@ -1533,7 +1533,7 @@ export function InspectorPanel({
         }
 
         // Standard handling for regular handle inputs
-        // If the input is marked as a handle but doesn't have the correct input_type
+        // If the input is marked as a handle but doesn't have the correct input_type (legacy pattern)
         if (inputDef.is_handle && inputDef.input_type !== 'handle') {
           inputDef.input_type = 'handle';
         }
@@ -1793,7 +1793,7 @@ export function InspectorPanel({
                                 const isConnected = isInputConnected(handleName);
                                 return (
                                   <div key={inputDef.name} className="pb-2">
-                                    <Label htmlFor={`config-${selectedNode?.id}-${inputDef.name}`} className="text-xs font-medium flex items-center justify-between">
+                                    <Label htmlFor={`config-${selectedNode?.id}-${inputDef.name}`} className="text-xs font-medium flex items-center justify-between" title={inputDef.is_handle ? "This input can be connected to other nodes" : ""}>
                                       <span>{inputDef.display_name}</span>
                                       {inputDef.required && <Badge variant="destructive" className="text-[9px] h-4 px-1">Required</Badge>}
                                     </Label>
@@ -1801,13 +1801,7 @@ export function InspectorPanel({
                                       <p className="text-xs text-muted-foreground mb-1.5 mt-0.5">{inputDef.info}</p>
                                     )}
-                                    {isConnected ? (
-                                      <>
-                                        {renderInputComponent(inputDef, true)}
-                                        <p className="text-xs text-blue-500 mt-1">Connected to handle</p>
-                                      </>
-                                    ) : (
-                                      renderInputComponent(inputDef, false)
-                                    )}
+                                    {renderInputComponent(inputDef, isConnected)}
                                   </div>
                                 );
                               })}
