# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: authentication.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'authentication.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x61uthentication.proto\x12\x0e\x61uthentication\"\x92\x01\n\x15OAuthAuthorizeRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\ttool_name\x18\x02 \x01(\t\x12/\n\x08provider\x18\x03 \x01(\x0e\x32\x1d.authentication.OAuthProvider\x12\x0e\n\x06scopes\x18\x04 \x03(\t\x12\x14\n\x0credirect_uri\x18\x05 \x01(\t\"d\n\x16OAuthAuthorizeResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x19\n\x11\x61uthorization_url\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\t\"B\n\x14OAuthCallbackRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\r\n\x05state\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"\x85\x01\n\x15OAuthCallbackResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x11\n\ttool_name\x18\x04 \x01(\t\x12\x10\n\x08provider\x18\x05 \x01(\t\x12\x14\n\x0credirect_url\x18\x06 \x01(\t\"m\n\x16OAuthCredentialRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\ttool_name\x18\x02 \x01(\t\x12/\n\x08provider\x18\x03 \x01(\x0e\x32\x1d.authentication.OAuthProvider\"\xd5\x01\n\x17OAuthCredentialResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x11\n\ttool_name\x18\x04 \x01(\t\x12\x10\n\x08provider\x18\x05 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x06 \x01(\t\x12\x15\n\rrefresh_token\x18\x07 \x01(\t\x12\x12\n\ntoken_type\x18\x08 \x01(\t\x12\x12\n\nexpires_in\x18\t \x01(\x05\x12\r\n\x05scope\x18\n \x01(\t\"\x8c\x01\n\x1cServerOAuthCredentialRequest\x12\x17\n\x0fserver_auth_key\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x11\n\ttool_name\x18\x03 \x01(\t\x12/\n\x08provider\x18\x04 \x01(\x0e\x32\x1d.authentication.OAuthProvider\"\x1b\n\x19OAuthProvidersListRequest\"\x98\x01\n\x11OAuthProviderInfo\x12/\n\x08provider\x18\x01 \x01(\x0e\x32\x1d.authentication.OAuthProvider\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x03 \x01(\t\x12\x17\n\x0fsupported_tools\x18\x04 \x03(\t\x12\x15\n\ris_configured\x18\x05 \x01(\x08\"t\n\x1aOAuthProvidersListResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x34\n\tproviders\x18\x03 \x03(\x0b\x32!.authentication.OAuthProviderInfo\"\\\n\x16OAuthToolScopesRequest\x12\x11\n\ttool_name\x18\x01 \x01(\t\x12/\n\x08provider\x18\x02 \x01(\x0e\x32\x1d.authentication.OAuthProvider\"\xa4\x01\n\x17OAuthToolScopesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\ttool_name\x18\x03 \x01(\t\x12/\n\x08provider\x18\x04 \x01(\x0e\x32\x1d.authentication.OAuthProvider\x12\x0e\n\x06scopes\x18\x05 \x03(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\"s\n\x1c\x44\x65leteOAuthCredentialRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\ttool_name\x18\x02 \x01(\t\x12/\n\x08provider\x18\x03 \x01(\x0e\x32\x1d.authentication.OAuthProvider\"A\n\x1d\x44\x65leteOAuthCredentialResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"j\n\x13OAuthRefreshRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\ttool_name\x18\x02 \x01(\t\x12/\n\x08provider\x18\x03 \x01(\x0e\x32\x1d.authentication.OAuthProvider\"\x85\x01\n\x14OAuthRefreshResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x03 \x01(\t\x12\x12\n\ntoken_type\x18\x04 \x01(\t\x12\x12\n\nexpires_in\x18\x05 \x01(\x05\x12\r\n\x05scope\x18\x06 \x01(\t\"\x14\n\x12HealthCheckRequest\"\xc9\x01\n\x13HealthCheckResponse\x12\x0f\n\x07healthy\x18\x01 \x01(\x08\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12K\n\x0c\x64\x65pendencies\x18\x04 \x03(\x0b\x32\x35.authentication.HealthCheckResponse.DependenciesEntry\x1a\x33\n\x11\x44\x65pendenciesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01*\x9e\x01\n\rOAuthProvider\x12\x1e\n\x1aOAUTH_PROVIDER_UNSPECIFIED\x10\x00\x12\x19\n\x15OAUTH_PROVIDER_GOOGLE\x10\x01\x12\x1c\n\x18OAUTH_PROVIDER_MICROSOFT\x10\x02\x12\x19\n\x15OAUTH_PROVIDER_GITHUB\x10\x03\x12\x19\n\x15OAUTH_PROVIDER_CUSTOM\x10\x04\x32\xb6\x07\n\x15\x41uthenticationService\x12^\n\rInitiateOAuth\x12%.authentication.OAuthAuthorizeRequest\x1a&.authentication.OAuthAuthorizeResponse\x12\x62\n\x13HandleOAuthCallback\x12$.authentication.OAuthCallbackRequest\x1a%.authentication.OAuthCallbackResponse\x12_\n\x12RefreshOAuthTokens\x12#.authentication.OAuthRefreshRequest\x1a$.authentication.OAuthRefreshResponse\x12\x66\n\x13GetOAuthCredentials\x12&.authentication.OAuthCredentialRequest\x1a\'.authentication.OAuthCredentialResponse\x12r\n\x19GetServerOAuthCredentials\x12,.authentication.ServerOAuthCredentialRequest\x1a\'.authentication.OAuthCredentialResponse\x12u\n\x16\x44\x65leteOAuthCredentials\x12,.authentication.DeleteOAuthCredentialRequest\x1a-.authentication.DeleteOAuthCredentialResponse\x12k\n\x12ListOAuthProviders\x12).authentication.OAuthProvidersListRequest\x1a*.authentication.OAuthProvidersListResponse\x12`\n\rGetToolScopes\x12&.authentication.OAuthToolScopesRequest\x1a\'.authentication.OAuthToolScopesResponse\x12V\n\x0bHealthCheck\x12\".authentication.HealthCheckRequest\x1a#.authentication.HealthCheckResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'authentication_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_HEALTHCHECKRESPONSE_DEPENDENCIESENTRY']._loaded_options = None
  _globals['_HEALTHCHECKRESPONSE_DEPENDENCIESENTRY']._serialized_options = b'8\001'
  _globals['_OAUTHPROVIDER']._serialized_start=2183
  _globals['_OAUTHPROVIDER']._serialized_end=2341
  _globals['_OAUTHAUTHORIZEREQUEST']._serialized_start=41
  _globals['_OAUTHAUTHORIZEREQUEST']._serialized_end=187
  _globals['_OAUTHAUTHORIZERESPONSE']._serialized_start=189
  _globals['_OAUTHAUTHORIZERESPONSE']._serialized_end=289
  _globals['_OAUTHCALLBACKREQUEST']._serialized_start=291
  _globals['_OAUTHCALLBACKREQUEST']._serialized_end=357
  _globals['_OAUTHCALLBACKRESPONSE']._serialized_start=360
  _globals['_OAUTHCALLBACKRESPONSE']._serialized_end=493
  _globals['_OAUTHCREDENTIALREQUEST']._serialized_start=495
  _globals['_OAUTHCREDENTIALREQUEST']._serialized_end=604
  _globals['_OAUTHCREDENTIALRESPONSE']._serialized_start=607
  _globals['_OAUTHCREDENTIALRESPONSE']._serialized_end=820
  _globals['_SERVEROAUTHCREDENTIALREQUEST']._serialized_start=823
  _globals['_SERVEROAUTHCREDENTIALREQUEST']._serialized_end=963
  _globals['_OAUTHPROVIDERSLISTREQUEST']._serialized_start=965
  _globals['_OAUTHPROVIDERSLISTREQUEST']._serialized_end=992
  _globals['_OAUTHPROVIDERINFO']._serialized_start=995
  _globals['_OAUTHPROVIDERINFO']._serialized_end=1147
  _globals['_OAUTHPROVIDERSLISTRESPONSE']._serialized_start=1149
  _globals['_OAUTHPROVIDERSLISTRESPONSE']._serialized_end=1265
  _globals['_OAUTHTOOLSCOPESREQUEST']._serialized_start=1267
  _globals['_OAUTHTOOLSCOPESREQUEST']._serialized_end=1359
  _globals['_OAUTHTOOLSCOPESRESPONSE']._serialized_start=1362
  _globals['_OAUTHTOOLSCOPESRESPONSE']._serialized_end=1526
  _globals['_DELETEOAUTHCREDENTIALREQUEST']._serialized_start=1528
  _globals['_DELETEOAUTHCREDENTIALREQUEST']._serialized_end=1643
  _globals['_DELETEOAUTHCREDENTIALRESPONSE']._serialized_start=1645
  _globals['_DELETEOAUTHCREDENTIALRESPONSE']._serialized_end=1710
  _globals['_OAUTHREFRESHREQUEST']._serialized_start=1712
  _globals['_OAUTHREFRESHREQUEST']._serialized_end=1818
  _globals['_OAUTHREFRESHRESPONSE']._serialized_start=1821
  _globals['_OAUTHREFRESHRESPONSE']._serialized_end=1954
  _globals['_HEALTHCHECKREQUEST']._serialized_start=1956
  _globals['_HEALTHCHECKREQUEST']._serialized_end=1976
  _globals['_HEALTHCHECKRESPONSE']._serialized_start=1979
  _globals['_HEALTHCHECKRESPONSE']._serialized_end=2180
  _globals['_HEALTHCHECKRESPONSE_DEPENDENCIESENTRY']._serialized_start=2129
  _globals['_HEALTHCHECKRESPONSE_DEPENDENCIESENTRY']._serialized_end=2180
  _globals['_AUTHENTICATIONSERVICE']._serialized_start=2344
  _globals['_AUTHENTICATIONSERVICE']._serialized_end=3294
# @@protoc_insertion_point(module_scope)
