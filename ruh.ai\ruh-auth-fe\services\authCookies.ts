"use server";

import { cookies } from "next/headers";

export const getAccessToken = async () => {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get("accessToken");
  if (accessToken) {
    return accessToken.value;
  } else {
    return "";
  }
};

export const getRefreshToken = async () => {
  const cookieStore = await cookies();
  const refreshToken = cookieStore.get("refreshToken");
  return refreshToken?.value || null;
};

export const checkAccessToken = async () => {
  const cookieStore = await cookies();
  const tokenCookie = cookieStore.get("accessToken");
  return Boolean(tokenCookie?.value);
};

export const setAuthCookies = async (
  accessToken: string,
  refreshToken: string | null,
  accessTokenAge: number,
  refreshTokenAge: number | null,
  isDomainWide: boolean = false
) => {
  const cookieStore = await cookies();
  cookieStore.set("accessToken", accessToken, {
    path: "/",
    domain: isDomainWide ? process.env.NEXT_PUBLIC_COOKIES_DOMAIN : undefined,
    httpOnly: false,
    sameSite: isDomainWide ? "none" : "strict",
    secure: true,
    maxAge: accessTokenAge,
  });

  if (refreshToken && refreshTokenAge) {
    cookieStore.set("refreshToken", refreshToken, {
      path: "/",
      domain: isDomainWide ? process.env.NEXT_PUBLIC_COOKIES_DOMAIN : undefined,
      httpOnly: true,
      sameSite: isDomainWide ? "none" : "strict",
      secure: true,
      maxAge: refreshTokenAge,
    });
  }
};

export const clearAuthCookies = async (isDomainWide: boolean = false) => {
  "use server";

  const cookieStore = await cookies();
  cookieStore.set("accessToken", "", {
    path: "/",
    domain: isDomainWide ? process.env.NEXT_PUBLIC_COOKIES_DOMAIN : undefined,
    httpOnly: true,
    secure: true,
    sameSite: isDomainWide ? "none" : "strict",
    maxAge: 0,
    expires: new Date(0),
  });

  cookieStore.set("refreshToken", "", {
    path: "/",
    domain: isDomainWide ? process.env.NEXT_PUBLIC_COOKIES_DOMAIN : undefined,
    httpOnly: true,
    secure: true,
    sameSite: isDomainWide ? "none" : "strict",
    maxAge: 0,
    expires: new Date(0),
  });
};

export const setRefreshingTokenCookie = async (
  isDomainWide: boolean = false
) => {
  const cookieStore = await cookies();
  cookieStore.set("refreshingToken", "true", {
    path: "/",
    domain: isDomainWide ? process.env.NEXT_PUBLIC_COOKIES_DOMAIN : undefined,
    httpOnly: false, // Must be false to be accessible from JavaScript
    sameSite: isDomainWide ? "none" : "strict",
    maxAge: 60, // 1 minute should be enough to handle the refresh
  });
};

export const clearRefreshingTokenCookie = async (
  isDomainWide: boolean = false
) => {
  const cookieStore = await cookies();
  cookieStore.set("refreshingToken", "", {
    path: "/",
    domain: isDomainWide ? process.env.NEXT_PUBLIC_COOKIES_DOMAIN : undefined,
    sameSite: isDomainWide ? "none" : "strict",
    maxAge: 0,
  });
};
