"""
Test script for UpdateDeploymentStatus with new proto structure
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import grpc
from unittest.mock import Mock
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.services.mcp_service import MCPConfigService
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig
from app.utils.constants.constants import DeploymentStatus
import uuid

def test_update_deployment_status_new_structure():
    """Test UpdateDeploymentStatus with new individual field structure"""
    
    # Create a test MCP first
    db = SessionLocal()
    try:
        test_mcp = McpConfig(
            id=str(uuid.uuid4()),
            name="Test MCP for Deployment Update",
            description="Test MCP for deployment status update",
            owner_id="test-user-123",
            owner_type="user",
            department="engineering",
            deployment_status=DeploymentStatus.PENDING.value
        )
        db.add(test_mcp)
        db.commit()
        db.refresh(test_mcp)
        
        print(f"Created test MCP with ID: {test_mcp.id}")
        
        # Test the UpdateDeploymentStatus method with new structure
        service = MCPConfigService()
        context = Mock(spec=grpc.ServicerContext)
        
        # Test Case 1: Update with SSE URL and completed status
        request = mcp_pb2.UpdateDeploymentStatusRequest(
            id=test_mcp.id,
            deployment_status=DeploymentStatus.COMPLETED.value,
            type="sse",
            url="http://localhost:3001/sse",
            image_name="test-mcp-image:latest"
        )
        
        response = service.UpdateDeploymentStatus(request, context)
        
        print(f"Response success: {response.success}")
        print(f"Response message: {response.message}")
        
        # Verify the MCP was updated
        db.refresh(test_mcp)
        print(f"Updated deployment status: {test_mcp.deployment_status}")
        print(f"Updated config: {test_mcp.config}")
        print(f"Updated image_name: {test_mcp.image_name}")
        
        # Test Case 2: Update with HTTP URL
        request2 = mcp_pb2.UpdateDeploymentStatusRequest(
            id=test_mcp.id,
            deployment_status=DeploymentStatus.COMPLETED.value,
            type="http",
            url="http://localhost:3002/api",
            image_name="test-mcp-http:v1.0"
        )
        
        response2 = service.UpdateDeploymentStatus(request2, context)
        print(f"\nSecond update - Response success: {response2.success}")
        print(f"Second update - Response message: {response2.message}")
        
        # Test Case 3: Update with pending status and error message
        request3 = mcp_pb2.UpdateDeploymentStatusRequest(
            id=test_mcp.id,
            deployment_status=DeploymentStatus.PENDING.value,
            error_message="Deployment failed due to network timeout"
        )
        
        response3 = service.UpdateDeploymentStatus(request3, context)
        print(f"\nError update - Response success: {response3.success}")
        print(f"Error update - Response message: {response3.message}")
        
        # Verify final state
        db.refresh(test_mcp)
        print(f"\nFinal deployment status: {test_mcp.deployment_status}")
        print(f"Final config: {test_mcp.config}")
        print(f"Final image_name: {test_mcp.image_name}")
        
    finally:
        # Cleanup
        if test_mcp:
            db.delete(test_mcp)
            db.commit()
        db.close()

if __name__ == "__main__":
    test_update_deployment_status_new_structure()