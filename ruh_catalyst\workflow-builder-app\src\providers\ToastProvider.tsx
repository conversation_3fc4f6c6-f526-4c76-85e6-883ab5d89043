"use client";

import { Toaster } from "sonner";
import { useTheme } from "next-themes";

export function ToastProvider() {
  const { theme } = useTheme();

  return (
    <Toaster
      position="top-right"
      toastOptions={{
        style: {
          background: theme === "dark" ? "#222222" : "#F9F7F7",
          color: theme === "dark" ? "#f0f0f0" : "#112D4E",
          border:
            theme === "dark"
              ? "1px solid rgba(29, 205, 159, 0.1)"
              : "1px solid rgba(63, 114, 175, 0.1)",
        },
      }}
    />
  );
}
