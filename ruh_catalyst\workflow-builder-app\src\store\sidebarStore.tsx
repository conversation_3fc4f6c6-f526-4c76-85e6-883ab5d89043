"use client";

import { create } from "zustand";
import { createContext, useContext, useRef } from "react";

interface SidebarState {
  isCollapsed: boolean;
  toggleCollapsed: () => void;
  setCollapsed: (collapsed: boolean) => void;
}

// Create the store
const createSidebarStore = (initialState: Partial<SidebarState> = {}) => {
  // Check if localStorage is available (for SSR compatibility)
  const getInitialCollapsedState = () => {
    if (typeof window !== "undefined") {
      const savedState = localStorage.getItem("sidebar-collapsed");
      return savedState ? JSON.parse(savedState) : false;
    }
    return false;
  };

  return create<SidebarState>((set) => ({
    isCollapsed: getInitialCollapsedState(),
    toggleCollapsed: () =>
      set((state) => {
        const newCollapsed = !state.isCollapsed;
        if (typeof window !== "undefined") {
          localStorage.setItem("sidebar-collapsed", JSON.stringify(newCollapsed));
        }
        return { isCollapsed: newCollapsed };
      }),
    setCollapsed: (collapsed) =>
      set(() => {
        if (typeof window !== "undefined") {
          localStorage.setItem("sidebar-collapsed", JSON.stringify(collapsed));
        }
        return { isCollapsed: collapsed };
      }),
    ...initialState,
  }));
};

// Create context for the store
type SidebarStoreType = ReturnType<typeof createSidebarStore>;
const SidebarStoreContext = createContext<SidebarStoreType | null>(null);

// Provider component
export const SidebarStoreProvider = ({ children }: { children: React.ReactNode }) => {
  const storeRef = useRef<SidebarStoreType | null>(null);
  if (!storeRef.current) {
    storeRef.current = createSidebarStore();
  }
  return (
    <SidebarStoreContext.Provider value={storeRef.current}>{children}</SidebarStoreContext.Provider>
  );
};

// Hook to use the store
export const useSidebarStore = <T,>(selector: (state: SidebarState) => T): T => {
  const store = useContext(SidebarStoreContext);
  if (!store) {
    throw new Error("useSidebarStore must be used within SidebarStoreProvider");
  }

  // Use the store with the selector
  return store(selector);
};
