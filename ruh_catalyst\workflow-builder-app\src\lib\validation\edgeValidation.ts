import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ValidationError, ValidationErrorCode, ValidationResult } from "./types";
import { createValidationError } from "./errors";

/**
 * Validates a single edge
 * 
 * @param edge The edge to validate
 * @param nodes The array of nodes to check against
 * @param index The index of the edge in the array
 * @returns An array of validation errors
 */
export function validateEdge(
  edge: Edge,
  nodes: Node<WorkflowNodeData>[],
  index: number
): ValidationError[] {
  const errors: ValidationError[] = [];

  // Check for required properties
  if (!edge.id) {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_MISSING_ID,
        `Edge at index ${index} is missing an ID`
      )
    );
  }

  if (!edge.source) {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_MISSING_SOURCE,
        `Edge ${edge.id || `at index ${index}`} is missing a source`,
        "error",
        undefined,
        edge.id
      )
    );
  }

  if (!edge.target) {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_MISSING_TARGET,
        `Edge ${edge.id || `at index ${index}`} is missing a target`,
        "error",
        undefined,
        edge.id
      )
    );
  }

  // Skip further validation if any required properties are missing
  if (!edge.source || !edge.target) {
    return errors;
  }

  // Check if source and target nodes exist
  const sourceNode = nodes.find(node => node.id === edge.source);
  if (!sourceNode) {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_SOURCE_NOT_FOUND,
        `Edge ${edge.id || `at index ${index}`} has a non-existent source node: ${edge.source}`,
        "error",
        undefined,
        edge.id
      )
    );
  }

  const targetNode = nodes.find(node => node.id === edge.target);
  if (!targetNode) {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_TARGET_NOT_FOUND,
        `Edge ${edge.id || `at index ${index}`} has a non-existent target node: ${edge.target}`,
        "error",
        undefined,
        edge.id
      )
    );
  }

  // Check for self-referencing edges
  if (edge.source === edge.target) {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_SELF_REFERENCE,
        `Edge ${edge.id || `at index ${index}`} is self-referencing: ${edge.source} -> ${edge.target}`,
        "error",
        undefined,
        edge.id
      )
    );
  }

  return errors;
}

/**
 * Validates that all edges have unique IDs
 * 
 * @param edges The array of edges to validate
 * @returns An array of validation errors
 */
export function validateEdgeUniqueness(edges: Edge[]): ValidationError[] {
  const errors: ValidationError[] = [];
  const edgeIds = new Set<string>();
  const duplicateIds = new Set<string>();

  // Find duplicate IDs
  edges.forEach(edge => {
    if (edge.id) {
      if (edgeIds.has(edge.id)) {
        duplicateIds.add(edge.id);
      } else {
        edgeIds.add(edge.id);
      }
    }
  });

  // Create errors for duplicate IDs
  duplicateIds.forEach(id => {
    errors.push(
      createValidationError(
        ValidationErrorCode.EDGE_DUPLICATE_ID,
        `Duplicate edge ID: ${id}`,
        "error",
        undefined,
        id
      )
    );
  });

  return errors;
}

/**
 * Validates all edges in a workflow
 * 
 * @param edges The array of edges to validate
 * @param nodes The array of nodes to check against
 * @returns A validation result
 */
export function validateEdges(
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[]
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  // Check if edges is an array
  if (!Array.isArray(edges)) {
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_MISSING_EDGES,
        'Workflow must contain an "edges" array'
      )
    );
    return { isValid: false, errors, warnings, infos };
  }

  // Validate each edge
  edges.forEach((edge, index) => {
    const edgeErrors = validateEdge(edge, nodes, index);
    errors.push(...edgeErrors);
  });

  // Validate edge uniqueness
  const uniquenessErrors = validateEdgeUniqueness(edges);
  errors.push(...uniquenessErrors);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    infos,
  };
}
