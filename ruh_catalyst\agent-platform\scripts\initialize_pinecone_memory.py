#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to initialize Pinecone memory and add sample knowledge for agents.

This script:
1. Creates the Pinecone index if it doesn't exist
2. Adds sample knowledge to demonstrate the memory system
3. Tests basic memory operations
"""

import asyncio
import logging
import os
import sys
from typing import List, Dict, Any

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from app.memory import memory_manager
from app.shared.config.base import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class PineconeInitializer:
    """Initialize Pinecone memory with sample knowledge."""

    def __init__(self):
        self.settings = get_settings()
        self.logger = logger

    def get_sample_knowledge(self) -> List[Dict[str, Any]]:
        """Get sample knowledge items for different domains."""
        return [
            # Programming Knowledge
            {
                "content": "Python is a high-level, interpreted programming language known for its simplicity and readability. It supports multiple programming paradigms including procedural, object-oriented, and functional programming.",
                "metadata": {
                    "topic": "programming",
                    "language": "python",
                    "category": "general",
                    "difficulty": "beginner",
                },
                "source": "knowledge_base",
            },
            {
                "content": "FastAPI is a modern, fast (high-performance) web framework for building APIs with Python 3.7+ based on standard Python type hints. It provides automatic API documentation, data validation, and serialization.",
                "metadata": {
                    "topic": "programming",
                    "framework": "fastapi",
                    "category": "web_development",
                    "difficulty": "intermediate",
                },
                "source": "documentation",
            },
            {
                "content": "AutoGen is a framework that enables the creation of LLM applications using multiple agents that can converse with each other to solve tasks. It supports both single-agent and multi-agent conversations.",
                "metadata": {
                    "topic": "ai",
                    "framework": "autogen",
                    "category": "multi_agent",
                    "difficulty": "advanced",
                },
                "source": "documentation",
            },
            # AI/ML Knowledge
            {
                "content": "Machine Learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed. It uses algorithms to analyze data, identify patterns, and make predictions.",
                "metadata": {
                    "topic": "ai",
                    "subtopic": "machine_learning",
                    "category": "general",
                    "difficulty": "beginner",
                },
                "source": "knowledge_base",
            },
            {
                "content": "Vector databases store and retrieve data based on vector embeddings, enabling semantic search and similarity matching. They are essential for RAG (Retrieval Augmented Generation) applications and AI-powered search.",
                "metadata": {
                    "topic": "ai",
                    "subtopic": "vector_databases",
                    "category": "infrastructure",
                    "difficulty": "intermediate",
                },
                "source": "knowledge_base",
            },
            {
                "content": "Pinecone is a fully managed vector database service that provides fast, accurate similarity search at scale. It supports real-time updates, metadata filtering, and hybrid search capabilities.",
                "metadata": {
                    "topic": "ai",
                    "product": "pinecone",
                    "category": "database",
                    "difficulty": "intermediate",
                },
                "source": "product_documentation",
            },
            # Software Development
            {
                "content": "RESTful APIs follow REST (Representational State Transfer) architectural principles, using HTTP methods (GET, POST, PUT, DELETE) to perform operations on resources identified by URLs.",
                "metadata": {
                    "topic": "programming",
                    "subtopic": "api_design",
                    "category": "web_development",
                    "difficulty": "intermediate",
                },
                "source": "best_practices",
            },
            {
                "content": "Docker is a containerization platform that packages applications and their dependencies into lightweight, portable containers. It ensures consistency across different environments and simplifies deployment.",
                "metadata": {
                    "topic": "devops",
                    "tool": "docker",
                    "category": "containerization",
                    "difficulty": "intermediate",
                },
                "source": "documentation",
            },
            {
                "content": "Redis is an in-memory data structure store used as a database, cache, and message broker. It supports various data structures like strings, hashes, lists, sets, and sorted sets.",
                "metadata": {
                    "topic": "database",
                    "product": "redis",
                    "category": "caching",
                    "difficulty": "intermediate",
                },
                "source": "documentation",
            },
            # Business and Communication
            {
                "content": "Agile methodology is an iterative approach to software development that emphasizes collaboration, customer feedback, and rapid delivery of working software through short development cycles called sprints.",
                "metadata": {
                    "topic": "methodology",
                    "approach": "agile",
                    "category": "project_management",
                    "difficulty": "beginner",
                },
                "source": "methodology_guide",
            },
        ]

    async def initialize_sample_agents(self) -> List[str]:
        """Initialize sample agents with different specializations."""
        agents = [
            {
                "agent_id": "python-expert",
                "specialization": "Python programming and web development",
                "knowledge_filter": [
                    "programming",
                    "python",
                    "fastapi",
                    "web_development",
                ],
            },
            {
                "agent_id": "ai-specialist",
                "specialization": "AI, ML, and vector databases",
                "knowledge_filter": [
                    "ai",
                    "machine_learning",
                    "vector_databases",
                    "pinecone",
                ],
            },
            {
                "agent_id": "devops-engineer",
                "specialization": "DevOps, containerization, and infrastructure",
                "knowledge_filter": ["devops", "docker", "database", "redis"],
            },
            {
                "agent_id": "project-manager",
                "specialization": "Project management and methodologies",
                "knowledge_filter": ["methodology", "agile", "project_management"],
            },
        ]

        sample_knowledge = self.get_sample_knowledge()
        created_agents = []

        for agent_info in agents:
            agent_id = agent_info["agent_id"]
            knowledge_filter = agent_info["knowledge_filter"]

            # Filter knowledge relevant to this agent
            relevant_knowledge = [
                item
                for item in sample_knowledge
                if any(
                    filter_term in str(item["metadata"]).lower()
                    for filter_term in knowledge_filter
                )
            ]

            if relevant_knowledge:
                added_count = await memory_manager.add_agent_knowledge(
                    agent_id=agent_id,
                    knowledge_items=relevant_knowledge,
                    user_id="system",
                    knowledge_type="domain_expertise",
                )

                self.logger.info(
                    f"✅ Initialized agent '{agent_id}' with {added_count} knowledge items"
                )
                created_agents.append(agent_id)

        return created_agents

    async def test_memory_operations(self, agent_ids: List[str]) -> bool:
        """Test basic memory operations with the initialized agents."""
        try:
            self.logger.info("Testing memory operations...")

            # Test queries for different agents
            test_queries = [
                ("python-expert", "How to build web APIs with Python?"),
                ("ai-specialist", "What are vector databases used for?"),
                ("devops-engineer", "How does Docker containerization work?"),
                ("project-manager", "What is Agile methodology?"),
            ]

            all_tests_passed = True

            for agent_id, query in test_queries:
                if agent_id in agent_ids:
                    results = await memory_manager.query_agent_memory(
                        agent_id=agent_id, query=query, user_id="system", k=3
                    )

                    if results:
                        self.logger.info(
                            f"✅ Agent '{agent_id}' retrieved {len(results)} relevant memories for: '{query}'"
                        )

                        # Log top result
                        top_result = results[0]
                        score = top_result.metadata.get("score", 0)
                        self.logger.info(
                            f"   Top result (score: {score:.3f}): {top_result.content[:100]}..."
                        )
                    else:
                        self.logger.warning(
                            f"⚠️ No results for agent '{agent_id}' query: '{query}'"
                        )
                        all_tests_passed = False

            return all_tests_passed

        except Exception as e:
            self.logger.error(f"Error testing memory operations: {e}")
            return False

    async def get_memory_statistics(self, agent_ids: List[str]) -> Dict[str, Any]:
        """Get memory statistics for all initialized agents."""
        stats = {}

        for agent_id in agent_ids:
            try:
                agent_stats = await memory_manager.get_agent_memory_stats(
                    agent_id=agent_id, user_id="system"
                )
                stats[agent_id] = agent_stats
            except Exception as e:
                self.logger.error(f"Error getting stats for agent {agent_id}: {e}")
                stats[agent_id] = {"error": str(e)}

        return stats

    async def run_initialization(self) -> Dict[str, Any]:
        """Run the complete initialization process."""
        results = {
            "agents_created": [],
            "memory_test_passed": False,
            "statistics": {},
            "success": False,
        }

        try:
            self.logger.info("=" * 60)
            self.logger.info("INITIALIZING PINECONE MEMORY SYSTEM")
            self.logger.info("=" * 60)

            # Check configuration
            if not self.settings.pinecone.api_key:
                self.logger.error("❌ Pinecone API key not configured")
                return results

            self.logger.info(
                f"Using Pinecone index: {self.settings.pinecone.index_name}"
            )
            self.logger.info(f"Environment: {self.settings.pinecone.environment}")

            # Initialize sample agents
            self.logger.info("\n1. Initializing sample agents with domain knowledge...")
            agent_ids = await self.initialize_sample_agents()
            results["agents_created"] = agent_ids

            if not agent_ids:
                self.logger.error("❌ No agents were created")
                return results

            # Wait for indexing
            self.logger.info("\n2. Waiting for Pinecone indexing...")
            await asyncio.sleep(5)

            # Test memory operations
            self.logger.info("\n3. Testing memory operations...")
            test_passed = await self.test_memory_operations(agent_ids)
            results["memory_test_passed"] = test_passed

            # Get statistics
            self.logger.info("\n4. Gathering memory statistics...")
            stats = await self.get_memory_statistics(agent_ids)
            results["statistics"] = stats

            # Summary
            results["success"] = len(agent_ids) > 0 and test_passed

            self.logger.info("\n" + "=" * 60)
            self.logger.info("INITIALIZATION SUMMARY")
            self.logger.info("=" * 60)
            self.logger.info(f"Agents created: {len(agent_ids)}")
            self.logger.info(
                f"Memory tests: {'✅ PASSED' if test_passed else '❌ FAILED'}"
            )
            self.logger.info(
                f"Overall status: {'✅ SUCCESS' if results['success'] else '❌ FAILED'}"
            )

            # Display statistics
            if stats:
                self.logger.info("\nMemory Statistics:")
                for agent_id, agent_stats in stats.items():
                    if "error" not in agent_stats:
                        vectors = agent_stats.get("namespace_vectors", 0)
                        self.logger.info(f"  {agent_id}: {vectors} vectors")

            return results

        except Exception as e:
            self.logger.error(f"Error during initialization: {e}")
            results["success"] = False
            return results


async def main():
    """Main initialization function."""
    initializer = PineconeInitializer()

    try:
        results = await initializer.run_initialization()

        if results["success"]:
            print("\n🎉 Pinecone memory system initialized successfully!")
            print(f"Created {len(results['agents_created'])} specialized agents:")
            for agent_id in results["agents_created"]:
                print(f"  - {agent_id}")
            print("\nYou can now use these agents with persistent Pinecone memory.")
        else:
            print("\n❌ Initialization failed. Check the logs for details.")

        return results["success"]

    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
