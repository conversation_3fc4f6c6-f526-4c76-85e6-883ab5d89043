/**
 * Shared interfaces for authentication and API responses
 */

// ==========================================
// Authentication Interfaces
// ==========================================

/**
 * Response from login API
 */
export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  accessTokenAge?: number;
  refreshTokenAge?: number;
  success: boolean;
  message: string;
}

/**
 * Response from signup API
 */
export interface SignupResponse {
  success: boolean;
  message: string;
}

/**
 * Combined login result with redirect path
 */
export interface LoginResult {
  loginData: LoginResponse;
  redirectPath: string;
}

/**
 * Response from token refresh API
 */
export interface TokenResponse {
  success: boolean;
  access_token: string;
  token_type: string;
  tokenExpireAt: string;
}

/**
 * Confirmation screen props interface
 */
export interface ConfirmationScreenProps {
  title: string;
  message: string;
  email: string;
  onBackToLogin: () => void;
}

// ==========================================
// User Interfaces
// ==========================================

/**
 * User information returned from API
 */
export interface UserInfo {
  email: string;
  fullName: string;
  company?: string;
  department?: string;
  jobRole?: string;
}

/**
 * Request payload for updating user profile
 */
export interface UserProfileUpdateRequest {
  company?: string | null;
  department?: string | null;
  job_role?: string | null;
}

/**
 * Response from profile update API
 */
export interface ProfileUpdateResponse {
  message: string;
}

/**
 * Data structure for user updates in the frontend
 */
export interface UserUpdateData {
  fullName?: string;
  company?: string;
  department?: string;
  jobRole?: string;
}
