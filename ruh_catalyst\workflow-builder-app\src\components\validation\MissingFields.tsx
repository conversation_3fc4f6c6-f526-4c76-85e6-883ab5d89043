import React from "react";
import { AlertCircle } from "lucide-react";
import { MissingField } from "@/lib/validation/types";
import { cn } from "@/lib/utils";

interface MissingFieldsProps {
  missingFields: MissingField[];
  className?: string;
  onNavigateToNode?: (nodeId: string) => void;
}

/**
 * Component for displaying missing required fields
 */
export function MissingFields({
  missingFields,
  className,
  onNavigateToNode,
}: MissingFieldsProps) {
  if (missingFields.length === 0) {
    return null;
  }

  // Group missing fields by node
  const fieldsByNode = missingFields.reduce<Record<string, MissingField[]>>(
    (acc, field) => {
      if (!acc[field.nodeId]) {
        acc[field.nodeId] = [];
      }
      acc[field.nodeId].push(field);
      return acc;
    },
    {}
  );

  return (
    <div className={cn("space-y-4", className)}>
      <h3 className="text-sm font-medium text-destructive flex items-center gap-1">
        <AlertCircle className="h-4 w-4" />
        Missing Required Fields
      </h3>

      <div className="space-y-3">
        {Object.entries(fieldsByNode).map(([nodeId, fields]) => (
          <div key={nodeId} className="space-y-2">
            <h4 className="text-xs font-medium flex items-center justify-between">
              <span>{fields[0].nodeName || nodeId}</span>
              {onNavigateToNode && (
                <button
                  type="button"
                  className="text-primary text-xs hover:underline"
                  onClick={() => onNavigateToNode(nodeId)}
                >
                  Navigate to node
                </button>
              )}
            </h4>
            <ul className="list-disc pl-5 text-xs space-y-1">
              {fields.map((field, index) => (
                <li key={`${nodeId}-${field.name}-${index}`}>
                  <span className="font-medium">{field.displayName}</span>
                  {field.info && (
                    <span className="text-muted-foreground ml-1">
                      ({field.info})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}

interface MissingFieldsBadgeProps {
  count: number;
  className?: string;
}

/**
 * Component for displaying a badge with the count of missing fields
 */
export function MissingFieldsBadge({
  count,
  className,
}: MissingFieldsBadgeProps) {
  if (count === 0) return null;

  return (
    <div
      className={cn(
        "flex items-center gap-1 rounded-full bg-destructive/10 px-2 py-0.5 text-xs font-medium text-destructive",
        className
      )}
    >
      <AlertCircle className="h-3 w-3" />
      <span>{count} missing fields</span>
    </div>
  );
}
