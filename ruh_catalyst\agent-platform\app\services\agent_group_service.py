"""
Agent Group Service for managing agent groups and their configurations.
"""

import logging
from typing import Dict, List, Optional, Any
from ..helper.api_call import HttpRequestHelper, AuthType
from ..shared.config.base import get_settings
from ..schemas.api import AgentConfig


logger = logging.getLogger(__name__)


class AgentGroupService:
    """Service for managing agent groups and fetching group details."""

    def __init__(self):
        self.settings = get_settings()
        self.http_request_service = HttpRequestHelper(
            self.settings.gateway.api_url,
            auth_token=self.settings.gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Agent-Platform-Auth-Key",
        )
        self.logger = logger

    async def fetch_group_details(self, group_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch agent group details including group configuration and member agents.

        Args:
            group_id: The ID of the agent group

        Returns:
            Dictionary containing group details and member agents
        """
        try:
            self.logger.info(f"Fetching group details for group_id: {group_id}")

            response = self.http_request_service.get(
                endpoint=f"agent-groups/{group_id}"
            )

            if not response or "group" not in response:
                self.logger.warning(f"No group found for group_id: {group_id}")
                return None

            group_data = response["group"]
            self.logger.info(f"Successfully fetched group details for: {group_id}")

            return group_data

        except Exception as e:
            self.logger.error(f"Error fetching group details for {group_id}: {e}")
            return None

    async def fetch_group_agents(self, group_id: str) -> List[Dict[str, Any]]:
        """
        Fetch all agents that belong to a specific group.

        Args:
            group_id: The ID of the agent group

        Returns:
            List of agent configurations
        """
        try:
            self.logger.info(f"Fetching agents for group_id: {group_id}")

            response = self.http_request_service.get(
                endpoint=f"agent-groups/{group_id}/agents"
            )

            if not response or "agents" not in response:
                self.logger.warning(f"No agents found for group_id: {group_id}")
                return []

            agents = response["agents"]
            self.logger.info(f"Found {len(agents)} agents for group: {group_id}")

            return agents

        except Exception as e:
            self.logger.error(f"Error fetching group agents for {group_id}: {e}")
            return []

    async def get_group_chat_config(self, group_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the chat configuration for a group (team type, termination conditions, etc.).

        Args:
            group_id: The ID of the agent group

        Returns:
            Dictionary containing chat configuration
        """
        try:
            group_details = await self.fetch_group_details(group_id)

            if not group_details:
                return None

            # Extract chat configuration from group details
            chat_config = {
                "team_type": group_details.get("team_type", "round_robin"),
                "max_messages": group_details.get("max_messages", 10),
                "termination_keywords": group_details.get(
                    "termination_keywords", ["TERMINATE"]
                ),
                "selector_model": group_details.get("selector_model"),
                "group_description": group_details.get("description", ""),
                "group_name": group_details.get("name", f"Group-{group_id}"),
            }

            self.logger.info(f"Group chat config for {group_id}: {chat_config}")
            return chat_config

        except Exception as e:
            self.logger.error(f"Error getting group chat config for {group_id}: {e}")
            return None

    async def validate_group_for_chat(self, group_id: str) -> bool:
        """
        Validate if a group is ready for chat (has agents, proper configuration, etc.).

        Args:
            group_id: The ID of the agent group

        Returns:
            True if group is valid for chat, False otherwise
        """
        try:
            # Check if group exists
            group_details = await self.fetch_group_details(group_id)
            if not group_details:
                self.logger.warning(f"Group {group_id} not found")
                return False

            # Check if group has agents
            agents = await self.fetch_group_agents(group_id)
            if len(agents) < 1:
                self.logger.warning(f"Group {group_id} has no agents")
                return False

            # Check if group is active
            if not group_details.get("is_active", True):
                self.logger.warning(f"Group {group_id} is not active")
                return False

            self.logger.info(
                f"Group {group_id} is valid for chat with {len(agents)} agents"
            )
            return True

        except Exception as e:
            self.logger.error(f"Error validating group {group_id}: {e}")
            return False

    async def get_group_agent_configs(self, group_id: str) -> List[AgentConfig]:
        """
        Get properly formatted AgentConfig objects for all agents in a group.

        Args:
            group_id: The ID of the agent group

        Returns:
            List of AgentConfig objects
        """
        try:
            agents_data = await self.fetch_group_agents(group_id)
            agent_configs = []

            for agent_data in agents_data:
                try:
                    # Convert agent data to AgentConfig format
                    agent_config = AgentConfig(
                        id=agent_data.get("id"),
                        name=agent_data.get("name"),
                        description=agent_data.get("description", ""),
                        system_message=agent_data.get(
                            "system_message", "You are a helpful AI assistant."
                        ),
                        model_provider=agent_data.get("model_provider", "openai"),
                        model_name=agent_data.get("model_name", "gpt-4o-mini"),
                        model_api_key=agent_data.get("model_api_key"),
                        workflow_ids=agent_data.get("workflow_ids", []),
                        mcp_server_ids=agent_data.get("mcp_server_ids", []),
                        agent_category=agent_data.get("agent_category"),
                        visibility=agent_data.get("visibility", "private"),
                        tags=agent_data.get("tags", {}),
                    )
                    agent_configs.append(agent_config)

                except Exception as e:
                    self.logger.error(f"Error converting agent data to config: {e}")
                    continue

            self.logger.info(
                f"Successfully converted {len(agent_configs)} agent configs for group {group_id}"
            )
            return agent_configs

        except Exception as e:
            self.logger.error(f"Error getting agent configs for group {group_id}: {e}")
            return []
