#!/usr/bin/env python3
"""
Test script to verify the actual initialize_workflow_with_params function works correctly.
"""

import sys
import os
import json

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))


def test_actual_function():
    """
    Test the actual initialize_workflow_with_params function.
    """
    try:
        from services.initialize_workflow import initialize_workflow_with_params

        print("✅ Successfully imported initialize_workflow_with_params")
    except ImportError as e:
        print(f"❌ Failed to import: {e}")
        print("Trying alternative import...")
        try:
            # Try importing directly from the file
            import importlib.util

            spec = importlib.util.spec_from_file_location(
                "initialize_workflow", "app/services/initialize_workflow.py"
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            initialize_workflow_with_params = module.initialize_workflow_with_params
            print("✅ Successfully imported via alternative method")
        except Exception as e2:
            print(f"❌ Alternative import also failed: {e2}")
            return False

    print("Testing actual initialize_workflow_with_params function...")

    # Create a workflow that matches the user's scenario
    workflow = {
        "transitions": [
            {
                "id": "ApiRequestNode-1748853965235",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [
                                    {"field_name": "url", "field_value": None},
                                    {"field_name": "body", "field_value": None},
                                ]
                            }
                        }
                    ]
                },
            },
            {
                "id": "ApiRequestNode-1748854246219",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [
                                    {"field_name": "url", "field_value": None},
                                    {"field_name": "body", "field_value": None},
                                ]
                            }
                        }
                    ]
                },
            },
            {
                "id": "SomeOtherNode-123456789",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_params": {
                                "items": [
                                    {"field_name": "url", "field_value": None},
                                    {"field_name": "body", "field_value": None},
                                ]
                            }
                        }
                    ]
                },
            },
        ]
    }

    # User's actual payload
    params = {
        "payload": {
            "user_dependent_fields": ["url", "body"],
            "user_payload_template": {
                "url": {
                    "value": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
                    "transition_id": "ApiRequestNode-1748853965235",
                },
                "body": {
                    "value": {
                        "title": "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai",
                        "summary": "Delve into the transformative impact of AI agent workflows in enhancing business efficiency.",
                        "category": "AI Innovation",
                    },
                    "transition_id": "ApiRequestNode-1748854246219",
                },
            },
        }
    }

    print("Original workflow:")
    for i, transition in enumerate(workflow["transitions"]):
        url_value = transition["node_info"]["tools_to_use"][0]["tool_params"]["items"][
            0
        ]["field_value"]
        body_value = transition["node_info"]["tools_to_use"][0]["tool_params"]["items"][
            1
        ]["field_value"]
        print(
            f"  Transition {i+1} ({transition['id']}) - URL: {url_value}, Body: {body_value}"
        )

    try:
        result = initialize_workflow_with_params(workflow, params)
        print("\n✅ Function executed successfully!")
    except Exception as e:
        print(f"\n❌ Function failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False

    print("\nAfter initialization:")

    # Check results for each transition
    transition1_url = result["transitions"][0]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition1_body = result["transitions"][0]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][1]["field_value"]

    transition2_url = result["transitions"][1]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition2_body = result["transitions"][1]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][1]["field_value"]

    transition3_url = result["transitions"][2]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][0]["field_value"]
    transition3_body = result["transitions"][2]["node_info"]["tools_to_use"][0][
        "tool_params"
    ]["items"][1]["field_value"]

    print(
        f"  Transition 1 (ApiRequestNode-1748853965235) - URL: {transition1_url}, Body: {transition1_body}"
    )
    print(
        f"  Transition 2 (ApiRequestNode-1748854246219) - URL: {transition2_url}, Body: {transition2_body}"
    )
    print(
        f"  Transition 3 (SomeOtherNode-123456789) - URL: {transition3_url}, Body: {transition3_body}"
    )

    # Verify the results
    success = True

    # Transition 1 should have URL but not body
    if (
        transition1_url
        != "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login"
    ):
        print(f"❌ FAIL: Expected transition 1 to have URL, got: {transition1_url}")
        success = False
    else:
        print("✅ PASS: Transition 1 has correct URL")

    if transition1_body is not None:
        print(
            f"❌ FAIL: Expected transition 1 body to be None, got: {transition1_body}"
        )
        success = False
    else:
        print("✅ PASS: Transition 1 body is None")

    # Transition 2 should have body but not URL
    if transition2_url is not None:
        print(f"❌ FAIL: Expected transition 2 URL to be None, got: {transition2_url}")
        success = False
    else:
        print("✅ PASS: Transition 2 URL is None")

    if transition2_body is None:
        print(f"❌ FAIL: Expected transition 2 to have body, got: {transition2_body}")
        success = False
    elif (
        transition2_body.get("title")
        != "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai"
    ):
        print(
            f"❌ FAIL: Expected transition 2 body title to match, got: {transition2_body}"
        )
        success = False
    else:
        print("✅ PASS: Transition 2 has correct body")

    # Transition 3 should have neither
    if transition3_url is not None:
        print(f"❌ FAIL: Expected transition 3 URL to be None, got: {transition3_url}")
        success = False
    else:
        print("✅ PASS: Transition 3 URL is None")

    if transition3_body is not None:
        print(
            f"❌ FAIL: Expected transition 3 body to be None, got: {transition3_body}"
        )
        success = False
    else:
        print("✅ PASS: Transition 3 body is None")

    if success:
        print("\n🎉 ALL TESTS PASSED! The actual function is working correctly.")
    else:
        print("\n❌ SOME TESTS FAILED! The issue still exists in the actual function.")

    return success


if __name__ == "__main__":
    test_actual_function()
