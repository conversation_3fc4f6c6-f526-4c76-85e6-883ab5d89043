import { No<PERSON>, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ValidationError, ValidationErrorCode, ValidationResult } from "./types";
import { createValidationError } from "./errors";
import { findStartNode, getConnectedNodes, detectCycles } from "./utils";

/**
 * Validates that the workflow has a StartNode with enhanced logging and fallback detection
 *
 * @param nodes The array of nodes to validate
 * @returns A validation result with the StartNode ID if found
 */
export function validateStartNode(
  nodes: Node<WorkflowNodeData>[]
): ValidationResult & { startNodeId?: string } {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`[${timestamp}] [validateStartNode] Starting StartNode validation with ${nodes.length} nodes`);

  // Check if nodes array is valid
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    console.warn(`[${timestamp}] [validateStartNode] Nodes array is empty or invalid`);
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_EMPTY,
        "Workflow is empty or not properly initialized"
      )
    );
    return { isValid: false, errors, warnings, infos };
  }

  // Find the StartNode using our enhanced detection
  const startNode = findStartNode(nodes);

  if (!startNode) {
    console.warn(`[${timestamp}] [validateStartNode] No StartNode found using standard detection methods`);

    // Try to find a node that might be a Start node based on additional heuristics
    const potentialStartNodes = nodes.filter(node => {
      // Check for any node with "start" in its label or type (case insensitive)
      const label = (node.data?.label || "").toLowerCase();
      const type = (node.data?.type || "").toLowerCase();
      const originalType = (node.data?.originalType || "").toLowerCase();

      return label.includes("start") || type.includes("start") || originalType.includes("start");
    });

    if (potentialStartNodes.length > 0) {
      const fallbackNode = potentialStartNodes[0];
      console.log(`[${timestamp}] [validateStartNode] Found potential StartNode using fallback detection: ${fallbackNode.id}`);

      // Add a warning but don't fail validation
      warnings.push(
        createValidationError(
          ValidationErrorCode.WORKFLOW_USING_FALLBACK_START_NODE,
          `Using ${fallbackNode.data?.label || fallbackNode.id} as a fallback Start node`,
          "warning"
        )
      );

      return {
        isValid: true,
        errors,
        warnings,
        infos,
        startNodeId: fallbackNode.id,
      };
    }

    // No StartNode found, even with fallback detection
    console.error(`[${timestamp}] [validateStartNode] No StartNode found, validation failed`);
    errors.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_MISSING_START_NODE,
        "Workflow must have a Start node"
      )
    );
    return { isValid: false, errors, warnings, infos };
  }

  console.log(`[${timestamp}] [validateStartNode] StartNode validation successful, found node: ${startNode.id}`);
  return {
    isValid: true,
    errors,
    warnings,
    infos,
    startNodeId: startNode.id,
  };
}

/**
 * Validates that all nodes are connected to the StartNode
 *
 * Instead of failing validation, it now adds warnings for disconnected nodes
 * and allows the workflow to be saved or executed with only the connected nodes.
 *
 * @param nodes The array of nodes to validate
 * @param edges The array of edges to validate
 * @param startNodeId The ID of the StartNode
 * @returns A validation result with the set of connected nodes
 */
export function validateConnectivity(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  startNodeId: string
): ValidationResult & { connectedNodes?: Set<string> } {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  // Import the tool-aware connection function
  const { getConnectedNodesWithToolConnections } = require('./toolConnectionFlow');

  // Get all nodes connected to the StartNode including tool connections
  const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, startNodeId);

  // Find disconnected nodes
  const disconnectedNodes = nodes.filter(node => !connectedNodes.has(node.id));

  if (disconnectedNodes.length > 0) {
    const disconnectedNodeIds = disconnectedNodes.map(node => node.id).join(", ");
    const disconnectedNodeLabels = disconnectedNodes
      .map(node => node.data?.label || node.id)
      .join(", ");

    warnings.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_DISCONNECTED_NODES,
        `The following nodes are not connected to the Start node and will not be saved or executed: ${disconnectedNodeLabels}`,
        "warning"
      )
    );

    // Add info about what will happen with these nodes
    infos.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_USING_FALLBACK_START_NODE,
        `Only nodes connected to the Start node will be included in the workflow execution and saving.`,
        "info"
      )
    );
  }

  // Always return isValid=true since disconnected nodes are now just warnings
  return {
    isValid: true,
    errors,
    warnings,
    infos,
    connectedNodes,
  };
}

/**
 * Detects cycles in the workflow
 *
 * @param nodes The array of nodes to validate
 * @param edges The array of edges to validate
 * @returns A validation result with warnings if cycles are detected
 */
export function detectCyclesInWorkflow(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const infos: ValidationError[] = [];

  // Detect cycles
  const nodesInCycle = detectCycles(nodes, edges);

  if (nodesInCycle.length > 0) {
    warnings.push(
      createValidationError(
        ValidationErrorCode.WORKFLOW_CYCLE_DETECTED,
        `Workflow contains cycles involving the following nodes: ${nodesInCycle.join(", ")}`,
        "warning"
      )
    );
  }

  return {
    isValid: nodesInCycle.length === 0,
    errors,
    warnings,
    infos,
  };
}
