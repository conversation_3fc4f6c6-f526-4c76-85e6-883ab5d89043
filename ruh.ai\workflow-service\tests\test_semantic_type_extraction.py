#!/usr/bin/env python3
"""
Test semantic type extraction functionality in workflow schema converter.

This test verifies that semantic types are correctly extracted from schema 1 
and added to the format field in schema 2 for the orchestration engine.
"""

import pytest
from app.services.workflow_builder.workflow_schema_converter import (
    extract_semantic_type_from_field,
    convert_node_to_transition_node,
)


class TestSemanticTypeExtraction:
    """Test cases for semantic type extraction functionality."""

    def test_extract_semantic_type_from_existing_format(self):
        """Test extraction when format is already specified in original schema."""
        field_props = {"type": "string", "format": "email", "description": "User email"}
        result = extract_semantic_type_from_field("user_email", field_props)
        assert result == "email"

    def test_extract_semantic_type_email_patterns(self):
        """Test extraction of email semantic type from field names."""
        test_cases = [
            ("user_email", {}),
            ("email_address", {}),
            ("contact_mail", {}),
            ("e_mail", {}),
        ]
        
        for field_name, field_props in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == "email", f"Failed for field: {field_name}"

    def test_extract_semantic_type_url_patterns(self):
        """Test extraction of URL semantic type from field names."""
        test_cases = [
            ("website_url", {}),
            ("profile_link", {}),
            ("redirect_href", {}),
            ("api_uri", {}),
            ("website", {}),
        ]
        
        for field_name, field_props in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == "url", f"Failed for field: {field_name}"

    def test_extract_semantic_type_datetime_patterns(self):
        """Test extraction of datetime semantic type from field names."""
        test_cases = [
            ("created_at", {}),
            ("updated_at", {}),
            ("modified_at", {}),
            ("timestamp", {}),
            ("datetime", {}),
            ("created_on", {}),
            ("date_field", {}),
            ("time_field", {}),
        ]
        
        for field_name, field_props in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == "datetime", f"Failed for field: {field_name}"

    def test_extract_semantic_type_currency_patterns(self):
        """Test extraction of currency semantic type from field names."""
        test_cases = [
            ("price", {}),
            ("cost", {}),
            ("amount", {}),
            ("currency", {}),
            ("money", {}),
            ("fee", {}),
        ]
        
        for field_name, field_props in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == "currency", f"Failed for field: {field_name}"

    def test_extract_semantic_type_media_patterns(self):
        """Test extraction of media semantic types from field names."""
        test_cases = [
            ("profile_image", {}, "image"),
            ("user_avatar", {}, "image"),
            ("photo", {}, "image"),
            ("voice_audio", {}, "audio"),
            ("background_music", {}, "audio"),
            ("intro_video", {}, "video"),
            ("movie_clip", {}, "video"),
        ]
        
        for field_name, field_props, expected in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == expected, f"Failed for field: {field_name}, expected: {expected}, got: {result}"

    def test_extract_semantic_type_other_patterns(self):
        """Test extraction of other semantic types from field names."""
        test_cases = [
            ("file_path", {}, "file_path"),
            ("user_id", {}, "identifier"),
            ("uuid", {}, "identifier"),
            ("status", {}, "status"),
            ("state", {}, "status"),
            ("background_color", {}, "color"),
            ("percentage", {}, "percentage"),
            ("completion_rate", {}, "percentage"),
        ]
        
        for field_name, field_props, expected in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == expected, f"Failed for field: {field_name}, expected: {expected}, got: {result}"

    def test_extract_semantic_type_default_fallback(self):
        """Test default fallback to 'string' for unknown patterns."""
        test_cases = [
            ("unknown_field", {}),
            ("random_data", {}),
            ("some_value", {}),
            ("description", {}),
        ]
        
        for field_name, field_props in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == "string", f"Failed for field: {field_name}"

    def test_extract_semantic_type_case_insensitive(self):
        """Test that extraction is case insensitive."""
        test_cases = [
            ("USER_EMAIL", {}, "email"),
            ("Website_URL", {}, "url"),
            ("CREATED_AT", {}, "datetime"),
            ("Price", {}, "currency"),
        ]
        
        for field_name, field_props, expected in test_cases:
            result = extract_semantic_type_from_field(field_name, field_props)
            assert result == expected, f"Failed for field: {field_name}"

    def test_convert_node_with_semantic_types(self):
        """Test that node conversion includes semantic types in output schema."""
        # Create a test node with mcp_info.output_schema
        node = {
            "id": "test_node",
            "data": {
                "type": "mcp",
                "definition": {
                    "name": "test_tool",
                    "mcp_info": {
                        "tool_name": "test_tool",
                        "server_id": "test_server",
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "user_email": {
                                    "type": "string",
                                    "description": "User email address"
                                },
                                "website_url": {
                                    "type": "string", 
                                    "description": "Website URL"
                                },
                                "created_at": {
                                    "type": "string",
                                    "description": "Creation timestamp"
                                },
                                "description": {
                                    "type": "string",
                                    "description": "General description"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # Convert the node
        result = convert_node_to_transition_node(node, [])
        
        # Verify the output schema has semantic types
        output_schema = result["server_tools"][0]["output_schema"]
        predefined_fields = output_schema["predefined_fields"]
        
        # Check that semantic types are correctly added to format field
        field_map = {field["field_name"]: field for field in predefined_fields}
        
        assert field_map["user_email"]["data_type"]["format"] == "email"
        assert field_map["website_url"]["data_type"]["format"] == "url"
        assert field_map["created_at"]["data_type"]["format"] == "datetime"
        assert field_map["description"]["data_type"]["format"] == "string"  # default


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
