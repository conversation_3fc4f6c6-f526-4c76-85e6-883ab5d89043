import pytest
from unittest.mock import AsyncMock, MagicMock
from app.services.mcp_service import MCPServiceClient
from app.schemas.mcp import DeploymentStatus, UrlTypeEnum


class TestMCPDeploymentUpdate:
    """Test cases for MCP deployment status update functionality."""

    @pytest.fixture
    def mcp_service(self):
        """Create MCPServiceClient instance for testing."""
        return MCPServiceClient()

    @pytest.fixture
    def mock_grpc_stub(self, mcp_service):
        """Mock the gRPC stub."""
        mock_stub = MagicMock()
        mcp_service.stub = mock_stub
        return mock_stub

    @pytest.mark.asyncio
    async def test_update_deployment_status_success(self, mcp_service, mock_grpc_stub):
        """Test successful deployment status update."""
        # Arrange
        mcp_id = "test-mcp-123"
        deployment_status = DeploymentStatus.COMPLETED
        url_type = UrlTypeEnum.SSE
        image_name = "test-image:latest"
        url = "https://test-url.com"
        
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Deployment status updated successfully"
        mock_grpc_stub.UpdateDeploymentStatus.return_value = mock_response

        # Act
        result = await mcp_service.update_deployment_status(
            mcp_id=mcp_id,
            deployment_status=deployment_status,
            type=url_type,
            image_name=image_name,
            error_message=None,
            url=url
        )

        # Assert
        assert result.success is True
        assert result.message == "Deployment status updated successfully"
        mock_grpc_stub.UpdateDeploymentStatus.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_deployment_status_with_error(self, mcp_service, mock_grpc_stub):
        """Test deployment status update with error message."""
        # Arrange
        mcp_id = "test-mcp-456"
        deployment_status = DeploymentStatus.PENDING
        error_message = "Deployment failed due to network issues"
        
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Deployment status updated with error"
        mock_grpc_stub.UpdateDeploymentStatus.return_value = mock_response

        # Act
        result = await mcp_service.update_deployment_status(
            mcp_id=mcp_id,
            deployment_status=deployment_status,
            type=None,
            image_name=None,
            error_message=error_message,
            url=None
        )

        # Assert
        assert result.success is True
        mock_grpc_stub.UpdateDeploymentStatus.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_deployment_status_minimal_params(self, mcp_service, mock_grpc_stub):
        """Test deployment status update with minimal required parameters."""
        # Arrange
        mcp_id = "test-mcp-789"
        deployment_status = DeploymentStatus.COMPLETED
        
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Deployment status updated"
        mock_grpc_stub.UpdateDeploymentStatus.return_value = mock_response

        # Act
        result = await mcp_service.update_deployment_status(
            mcp_id=mcp_id,
            deployment_status=deployment_status,
            type=None,
            image_name=None,
            error_message=None,
            url=None
        )

        # Assert
        assert result.success is True
        mock_grpc_stub.UpdateDeploymentStatus.assert_called_once()