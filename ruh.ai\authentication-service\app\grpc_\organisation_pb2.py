# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: organisation.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'organisation.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12organisation.proto\x12\x0corganisation\"\xa9\x01\n\x19\x43reateOrganisationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bwebsite_url\x18\x02 \x01(\t\x12\x10\n\x08industry\x18\x03 \x01(\t\x12\x12\n\ncreated_by\x18\x04 \x01(\t\x12\x12\n\nadmin_name\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64min_email\x18\x06 \x01(\t\x12\x11\n\x04logo\x18\x07 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_logo\"\xbe\x01\n\x19UpdateOrganisationRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x11\n\x04name\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0bwebsite_url\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x15\n\x08industry\x18\x05 \x01(\tH\x02\x88\x01\x01\x12\x11\n\x04logo\x18\x06 \x01(\tH\x03\x88\x01\x01\x42\x07\n\x05_nameB\x0e\n\x0c_website_urlB\x0b\n\t_industryB\x07\n\x05_logo\"$\n\x16GetOrganisationRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\xac\x01\n\x11OrganisationModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0bwebsite_url\x18\x03 \x01(\t\x12\x10\n\x08industry\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\t\x12\x12\n\ncreated_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\t\x12\x11\n\x04logo\x18\x08 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_logo\"\x8e\x01\n\nDepartment\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x14\n\x0cmember_count\x18\x04 \x01(\x05\x12\x17\n\nvisibility\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\x13\n\x0b\x61gent_count\x18\x06 \x01(\x05\x42\r\n\x0b_visibility\"\x83\x01\n\x1bOrganisationAndDeptResponse\x12\x35\n\x0corganisation\x18\x01 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12-\n\x0b\x64\x65partments\x18\x02 \x03(\x0b\x32\x18.organisation.Department\"y\n\x14OrganisationResponse\x12?\n\x0corganisation\x18\x01 \x01(\x0b\x32).organisation.OrganisationAndDeptResponse\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"\x83\x01\n\x18ListOrganisationsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x13\n\x0bsearch_term\x18\x03 \x01(\t\x12\x1a\n\x12\x66ilter_by_industry\x18\x04 \x01(\t\x12\x15\n\rfilter_by_tag\x18\x05 \x01(\t\"\x89\x01\n\x19ListOrganisationsResponse\x12\x36\n\rorganisations\x18\x01 \x03(\x0b\x32\x1f.organisation.OrganisationModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xaf\x01\n\x17\x43reateDepartmentRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x1c\n\x14parent_department_id\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\t\x12\x17\n\nvisibility\x18\x06 \x01(\tH\x00\x88\x01\x01\x42\r\n\x0b_visibility\"\"\n\x14GetDepartmentRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x8f\x01\n\x16ListDepartmentsRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x13\n\x0bsearch_term\x18\x04 \x01(\t\x12\x15\n\rdepartment_id\x18\x05 \x01(\t\x12\x0f\n\x07user_id\x18\x06 \x01(\t\"\x86\x02\n\x0f\x44\x65partmentModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x1c\n\x14parent_department_id\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\x12\x12\n\ncreated_at\x18\x07 \x01(\t\x12\x12\n\nupdated_at\x18\x08 \x01(\t\x12\x14\n\x0cmember_count\x18\t \x01(\x03\x12\x13\n\x0b\x61gent_count\x18\n \x01(\x03\x12\x17\n\nvisibility\x18\x0b \x01(\tH\x00\x88\x01\x01\x42\r\n\x0b_visibility\"X\n\x12\x44\x65partmentResponse\x12\x31\n\ndepartment\x18\x01 \x01(\x0b\x32\x1d.organisation.DepartmentModel\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x83\x01\n\x17ListDepartmentsResponse\x12\x32\n\x0b\x64\x65partments\x18\x01 \x03(\x0b\x32\x1d.organisation.DepartmentModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\x85\x01\n\x11InviteUserRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0c\n\x04role\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x12\n\npermission\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\"\x1e\n\x10GetInviteRequest\x12\n\n\x02id\x18\x01 \x01(\t\"^\n\x12ListInvitesRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x0e\n\x06status\x18\x04 \x01(\t\"\xd7\x01\n\x0bInviteModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x17\n\x0forganisation_id\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x0c\n\x04role\x18\x05 \x01(\t\x12\x12\n\npermission\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x12\n\ncreated_at\x18\t \x01(\t\x12\x12\n\nupdated_at\x18\n \x01(\t\x12\x12\n\nexpires_at\x18\x0b \x01(\t\"]\n\x0eInviteResponse\x12)\n\x06invite\x18\x01 \x01(\x0b\x32\x19.organisation.InviteModel\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"w\n\x13ListInvitesResponse\x12*\n\x07invites\x18\x01 \x03(\x0b\x32\x19.organisation.InviteModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\x81\x01\n\x19\x41\x63\x63\x65ptInviteByLinkRequest\x12\x14\n\x0cinvite_token\x18\x01 \x01(\t\x12\x1a\n\x12\x63urrent_user_email\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x11\n\tuser_name\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63\x63\x65pt\x18\x05 \x01(\x08\"l\n\x1cGrantDepartmentAccessRequest\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x02 \x03(\t\x12\x12\n\nfolder_ids\x18\x03 \x03(\t\x12\x0f\n\x07user_id\x18\x04 \x01(\t\"A\n\x1dGrantDepartmentAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"S\n\x14\x44\x65partmentAccessData\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x02 \x03(\t\x12\x12\n\nfolder_ids\x18\x03 \x03(\t\"q\n!BatchGrantDepartmentAccessRequest\x12;\n\x0f\x64\x65partment_data\x18\x01 \x03(\x0b\x32\".organisation.DepartmentAccessData\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"e\n\"BatchGrantDepartmentAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1d\n\x15\x66\x61iled_department_ids\x18\x03 \x03(\t\"-\n\x1aListTopLevelFoldersRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"\"\n\x06\x46older\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"f\n\x1bListTopLevelFoldersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x14.organisation.Folder\"j\n\x11\x44\x65partmentFolders\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x17\n\x0f\x64\x65partment_name\x18\x02 \x01(\t\x12%\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x14.organisation.Folder\"O\n\x1cListDepartmentFoldersRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x16\n\x0e\x64\x65partment_ids\x18\x02 \x03(\t\"~\n\x1dListDepartmentFoldersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12;\n\x12\x64\x65partment_folders\x18\x03 \x03(\x0b\x32\x1f.organisation.DepartmentFolders\"\x90\x01\n\x0bSourceModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12&\n\x04type\x18\x03 \x01(\x0e\x32\x18.organisation.SourceType\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x12\n\ncreated_at\x18\x05 \x01(\t\x12\x12\n\nupdated_at\x18\x06 \x01(\t\"\x90\x01\n\x10\x41\x64\x64SourceRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12&\n\x04type\x18\x02 \x01(\x0e\x32\x18.organisation.SourceType\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x1b\n\x13service_account_key\x18\x04 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x05 \x03(\t\"$\n\x08\x46ileInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x8e\x01\n\x11\x41\x64\x64SourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x06source\x18\x03 \x01(\x0b\x32\x19.organisation.SourceModel\x12,\n\x0csynced_files\x18\x04 \x03(\x0b\x32\x16.organisation.FileInfo\"-\n\x12ListSourcesRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"}\n\x13ListSourcesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x07sources\x18\x03 \x03(\x0b\x32\x19.organisation.SourceModel\x12\x18\n\x10isInitialMapping\x18\x04 \x01(\x08\"9\n\x13\x44\x65leteSourceRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"8\n\x14\x44\x65leteSourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"a\n\x1eUpdateSourceCredentialsRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x1b\n\x13service_account_key\x18\x03 \x01(\t\"n\n\x1fUpdateSourceCredentialsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x06source\x18\x03 \x01(\x0b\x32\x19.organisation.SourceModel\"C\n\x15ValidateSourceRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\"l\n\x16ValidateSourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x30\n\x12\x61\x63\x63\x65ssible_folders\x18\x03 \x03(\x0b\x32\x14.organisation.Folder\".\n\x1bGetUserOrganisationsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"o\n\x10UserOrganisation\x12\x35\n\x0corganisation\x18\x01 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12\x12\n\nis_primary\x18\x02 \x01(\x08\x12\x10\n\x08is_admin\x18\x03 \x01(\x08\"\xe1\x01\n\rPendingInvite\x12\x11\n\tinvite_id\x18\x01 \x01(\t\x12\x35\n\x0corganisation\x18\x02 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12\x12\n\ndepartment\x18\x03 \x01(\t\x12\x0c\n\x04role\x18\x04 \x01(\t\x12\x12\n\npermission\x18\x05 \x01(\t\x12\x12\n\ninviter_id\x18\x06 \x01(\t\x12\x14\n\x0cinviter_name\x18\x07 \x01(\t\x12\x12\n\ncreated_at\x18\x08 \x01(\t\x12\x12\n\nexpires_at\x18\t \x01(\t\"\xd6\x01\n\x19UserOrganisationsResponse\x12\x35\n\rorganisations\x18\x01 \x03(\x0b\x32\x1e.organisation.UserOrganisation\x12\x34\n\x0fpending_invites\x18\x02 \x03(\x0b\x32\x1b.organisation.PendingInvite\x12\x16\n\x0epersonal_space\x18\x03 \x01(\x08\x12\x12\n\nhas_joined\x18\x04 \x01(\x08\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0f\n\x07success\x18\x06 \x01(\x08\"S\n\x19ListInviterInvitesRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x17\n\x0forganisation_id\x18\x03 \x01(\t\"\x85\x02\n\rInviterInvite\x12\x12\n\ninvitee_id\x18\x01 \x01(\t\x12\x14\n\x0cinvitee_name\x18\x02 \x01(\t\x12\x15\n\rinvitee_email\x18\x03 \x01(\t\x12\x17\n\x0forganisation_id\x18\x04 \x01(\t\x12\x19\n\x11organisation_name\x18\x05 \x01(\t\x12\x12\n\ndepartment\x18\x06 \x01(\t\x12\x0c\n\x04role\x18\x07 \x01(\t\x12\x12\n\npermission\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\x11\n\tjoined_at\x18\n \x01(\t\x12\x12\n\ncreated_at\x18\x0b \x01(\t\x12\x12\n\nexpires_at\x18\x0c \x01(\t\"l\n\x1aListInviterInvitesResponse\x12,\n\x07invites\x18\x01 \x03(\x0b\x32\x1b.organisation.InviterInvite\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"[\n\x0e\x44\x65partmentUser\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x0c\n\x04role\x18\x04 \x01(\t\x12\x12\n\npermission\x18\x05 \x01(\t\"l\n\x19GetDepartmentUsersRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x15\n\rdepartment_id\x18\x02 \x01(\t\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xd3\x01\n\x1aGetDepartmentUsersResponse\x12+\n\x05users\x18\x01 \x03(\x0b\x32\x1c.organisation.DepartmentUser\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0f\n\x07success\x18\x06 \x01(\x08\x12\x17\n\x0f\x64\x65partment_name\x18\x07 \x01(\t\x12\x17\n\x0f\x64\x65partment_desc\x18\x08 \x01(\t\"\xb6\x01\n\x1c\x41\x64\x64MemberToDepartmentRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x15\n\rdepartment_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x11\n\tmember_id\x18\x04 \x01(\t\x12\x11\n\x04role\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\x17\n\npermission\x18\x06 \x01(\tH\x01\x88\x01\x01\x42\x07\n\x05_roleB\r\n\x0b_permission\"A\n\x1d\x41\x64\x64MemberToDepartmentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t*%\n\nVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*)\n\nSourceType\x12\x10\n\x0cGOOGLE_DRIVE\x10\x00\x12\t\n\x05SLACK\x10\x01*D\n\x0cInviteStatus\x12\x0b\n\x07PENDING\x10\x00\x12\x0c\n\x08\x41\x43\x43\x45PTED\x10\x01\x12\x0c\n\x08\x44\x45\x43LINED\x10\x02\x12\x0b\n\x07\x45XPIRED\x10\x03\x32\x96\x10\n\x13OrganisationService\x12\x63\n\x12\x63reateOrganisation\x12\'.organisation.CreateOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12]\n\x0fgetOrganisation\x12$.organisation.GetOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12\x63\n\x12updateOrganisation\x12\'.organisation.UpdateOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12]\n\x10\x63reateDepartment\x12%.organisation.CreateDepartmentRequest\x1a .organisation.DepartmentResponse\"\x00\x12`\n\x0flistDepartments\x12$.organisation.ListDepartmentsRequest\x1a%.organisation.ListDepartmentsResponse\"\x00\x12M\n\ninviteUser\x12\x1f.organisation.InviteUserRequest\x1a\x1c.organisation.InviteResponse\"\x00\x12]\n\x12\x61\x63\x63\x65ptInviteByLink\x12\'.organisation.AcceptInviteByLinkRequest\x1a\x1c.organisation.InviteResponse\"\x00\x12l\n\x14getUserOrganisations\x12).organisation.GetUserOrganisationsRequest\x1a\'.organisation.UserOrganisationsResponse\"\x00\x12r\n\x15grantDepartmentAccess\x12*.organisation.GrantDepartmentAccessRequest\x1a+.organisation.GrantDepartmentAccessResponse\"\x00\x12\x81\x01\n\x1a\x62\x61tchGrantDepartmentAccess\x12/.organisation.BatchGrantDepartmentAccessRequest\x1a\x30.organisation.BatchGrantDepartmentAccessResponse\"\x00\x12l\n\x13listTopLevelFolders\x12(.organisation.ListTopLevelFoldersRequest\x1a).organisation.ListTopLevelFoldersResponse\"\x00\x12N\n\taddSource\x12\x1e.organisation.AddSourceRequest\x1a\x1f.organisation.AddSourceResponse\"\x00\x12T\n\x0blistSources\x12 .organisation.ListSourcesRequest\x1a!.organisation.ListSourcesResponse\"\x00\x12W\n\x0c\x64\x65leteSource\x12!.organisation.DeleteSourceRequest\x1a\".organisation.DeleteSourceResponse\"\x00\x12x\n\x17updateSourceCredentials\x12,.organisation.UpdateSourceCredentialsRequest\x1a-.organisation.UpdateSourceCredentialsResponse\"\x00\x12]\n\x0evalidateSource\x12#.organisation.ValidateSourceRequest\x1a$.organisation.ValidateSourceResponse\"\x00\x12h\n\x11getInviterInvites\x12\'.organisation.ListInviterInvitesRequest\x1a(.organisation.ListInviterInvitesResponse\"\x00\x12i\n\x12getDepartmentUsers\x12\'.organisation.GetDepartmentUsersRequest\x1a(.organisation.GetDepartmentUsersResponse\"\x00\x12r\n\x15listDepartmentFolders\x12*.organisation.ListDepartmentFoldersRequest\x1a+.organisation.ListDepartmentFoldersResponse\"\x00\x12r\n\x15\x61\x64\x64MemberToDepartment\x12*.organisation.AddMemberToDepartmentRequest\x1a+.organisation.AddMemberToDepartmentResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'organisation_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_VISIBILITY']._serialized_start=6845
  _globals['_VISIBILITY']._serialized_end=6882
  _globals['_SOURCETYPE']._serialized_start=6884
  _globals['_SOURCETYPE']._serialized_end=6925
  _globals['_INVITESTATUS']._serialized_start=6927
  _globals['_INVITESTATUS']._serialized_end=6995
  _globals['_CREATEORGANISATIONREQUEST']._serialized_start=37
  _globals['_CREATEORGANISATIONREQUEST']._serialized_end=206
  _globals['_UPDATEORGANISATIONREQUEST']._serialized_start=209
  _globals['_UPDATEORGANISATIONREQUEST']._serialized_end=399
  _globals['_GETORGANISATIONREQUEST']._serialized_start=401
  _globals['_GETORGANISATIONREQUEST']._serialized_end=437
  _globals['_ORGANISATIONMODEL']._serialized_start=440
  _globals['_ORGANISATIONMODEL']._serialized_end=612
  _globals['_DEPARTMENT']._serialized_start=615
  _globals['_DEPARTMENT']._serialized_end=757
  _globals['_ORGANISATIONANDDEPTRESPONSE']._serialized_start=760
  _globals['_ORGANISATIONANDDEPTRESPONSE']._serialized_end=891
  _globals['_ORGANISATIONRESPONSE']._serialized_start=893
  _globals['_ORGANISATIONRESPONSE']._serialized_end=1014
  _globals['_LISTORGANISATIONSREQUEST']._serialized_start=1017
  _globals['_LISTORGANISATIONSREQUEST']._serialized_end=1148
  _globals['_LISTORGANISATIONSRESPONSE']._serialized_start=1151
  _globals['_LISTORGANISATIONSRESPONSE']._serialized_end=1288
  _globals['_CREATEDEPARTMENTREQUEST']._serialized_start=1291
  _globals['_CREATEDEPARTMENTREQUEST']._serialized_end=1466
  _globals['_GETDEPARTMENTREQUEST']._serialized_start=1468
  _globals['_GETDEPARTMENTREQUEST']._serialized_end=1502
  _globals['_LISTDEPARTMENTSREQUEST']._serialized_start=1505
  _globals['_LISTDEPARTMENTSREQUEST']._serialized_end=1648
  _globals['_DEPARTMENTMODEL']._serialized_start=1651
  _globals['_DEPARTMENTMODEL']._serialized_end=1913
  _globals['_DEPARTMENTRESPONSE']._serialized_start=1915
  _globals['_DEPARTMENTRESPONSE']._serialized_end=2003
  _globals['_LISTDEPARTMENTSRESPONSE']._serialized_start=2006
  _globals['_LISTDEPARTMENTSRESPONSE']._serialized_end=2137
  _globals['_INVITEUSERREQUEST']._serialized_start=2140
  _globals['_INVITEUSERREQUEST']._serialized_end=2273
  _globals['_GETINVITEREQUEST']._serialized_start=2275
  _globals['_GETINVITEREQUEST']._serialized_end=2305
  _globals['_LISTINVITESREQUEST']._serialized_start=2307
  _globals['_LISTINVITESREQUEST']._serialized_end=2401
  _globals['_INVITEMODEL']._serialized_start=2404
  _globals['_INVITEMODEL']._serialized_end=2619
  _globals['_INVITERESPONSE']._serialized_start=2621
  _globals['_INVITERESPONSE']._serialized_end=2714
  _globals['_LISTINVITESRESPONSE']._serialized_start=2716
  _globals['_LISTINVITESRESPONSE']._serialized_end=2835
  _globals['_ACCEPTINVITEBYLINKREQUEST']._serialized_start=2838
  _globals['_ACCEPTINVITEBYLINKREQUEST']._serialized_end=2967
  _globals['_GRANTDEPARTMENTACCESSREQUEST']._serialized_start=2969
  _globals['_GRANTDEPARTMENTACCESSREQUEST']._serialized_end=3077
  _globals['_GRANTDEPARTMENTACCESSRESPONSE']._serialized_start=3079
  _globals['_GRANTDEPARTMENTACCESSRESPONSE']._serialized_end=3144
  _globals['_DEPARTMENTACCESSDATA']._serialized_start=3146
  _globals['_DEPARTMENTACCESSDATA']._serialized_end=3229
  _globals['_BATCHGRANTDEPARTMENTACCESSREQUEST']._serialized_start=3231
  _globals['_BATCHGRANTDEPARTMENTACCESSREQUEST']._serialized_end=3344
  _globals['_BATCHGRANTDEPARTMENTACCESSRESPONSE']._serialized_start=3346
  _globals['_BATCHGRANTDEPARTMENTACCESSRESPONSE']._serialized_end=3447
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_start=3449
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_end=3494
  _globals['_FOLDER']._serialized_start=3496
  _globals['_FOLDER']._serialized_end=3530
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_start=3532
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_end=3634
  _globals['_DEPARTMENTFOLDERS']._serialized_start=3636
  _globals['_DEPARTMENTFOLDERS']._serialized_end=3742
  _globals['_LISTDEPARTMENTFOLDERSREQUEST']._serialized_start=3744
  _globals['_LISTDEPARTMENTFOLDERSREQUEST']._serialized_end=3823
  _globals['_LISTDEPARTMENTFOLDERSRESPONSE']._serialized_start=3825
  _globals['_LISTDEPARTMENTFOLDERSRESPONSE']._serialized_end=3951
  _globals['_SOURCEMODEL']._serialized_start=3954
  _globals['_SOURCEMODEL']._serialized_end=4098
  _globals['_ADDSOURCEREQUEST']._serialized_start=4101
  _globals['_ADDSOURCEREQUEST']._serialized_end=4245
  _globals['_FILEINFO']._serialized_start=4247
  _globals['_FILEINFO']._serialized_end=4283
  _globals['_ADDSOURCERESPONSE']._serialized_start=4286
  _globals['_ADDSOURCERESPONSE']._serialized_end=4428
  _globals['_LISTSOURCESREQUEST']._serialized_start=4430
  _globals['_LISTSOURCESREQUEST']._serialized_end=4475
  _globals['_LISTSOURCESRESPONSE']._serialized_start=4477
  _globals['_LISTSOURCESRESPONSE']._serialized_end=4602
  _globals['_DELETESOURCEREQUEST']._serialized_start=4604
  _globals['_DELETESOURCEREQUEST']._serialized_end=4661
  _globals['_DELETESOURCERESPONSE']._serialized_start=4663
  _globals['_DELETESOURCERESPONSE']._serialized_end=4719
  _globals['_UPDATESOURCECREDENTIALSREQUEST']._serialized_start=4721
  _globals['_UPDATESOURCECREDENTIALSREQUEST']._serialized_end=4818
  _globals['_UPDATESOURCECREDENTIALSRESPONSE']._serialized_start=4820
  _globals['_UPDATESOURCECREDENTIALSRESPONSE']._serialized_end=4930
  _globals['_VALIDATESOURCEREQUEST']._serialized_start=4932
  _globals['_VALIDATESOURCEREQUEST']._serialized_end=4999
  _globals['_VALIDATESOURCERESPONSE']._serialized_start=5001
  _globals['_VALIDATESOURCERESPONSE']._serialized_end=5109
  _globals['_GETUSERORGANISATIONSREQUEST']._serialized_start=5111
  _globals['_GETUSERORGANISATIONSREQUEST']._serialized_end=5157
  _globals['_USERORGANISATION']._serialized_start=5159
  _globals['_USERORGANISATION']._serialized_end=5270
  _globals['_PENDINGINVITE']._serialized_start=5273
  _globals['_PENDINGINVITE']._serialized_end=5498
  _globals['_USERORGANISATIONSRESPONSE']._serialized_start=5501
  _globals['_USERORGANISATIONSRESPONSE']._serialized_end=5715
  _globals['_LISTINVITERINVITESREQUEST']._serialized_start=5717
  _globals['_LISTINVITERINVITESREQUEST']._serialized_end=5800
  _globals['_INVITERINVITE']._serialized_start=5803
  _globals['_INVITERINVITE']._serialized_end=6064
  _globals['_LISTINVITERINVITESRESPONSE']._serialized_start=6066
  _globals['_LISTINVITERINVITESRESPONSE']._serialized_end=6174
  _globals['_DEPARTMENTUSER']._serialized_start=6176
  _globals['_DEPARTMENTUSER']._serialized_end=6267
  _globals['_GETDEPARTMENTUSERSREQUEST']._serialized_start=6269
  _globals['_GETDEPARTMENTUSERSREQUEST']._serialized_end=6377
  _globals['_GETDEPARTMENTUSERSRESPONSE']._serialized_start=6380
  _globals['_GETDEPARTMENTUSERSRESPONSE']._serialized_end=6591
  _globals['_ADDMEMBERTODEPARTMENTREQUEST']._serialized_start=6594
  _globals['_ADDMEMBERTODEPARTMENTREQUEST']._serialized_end=6776
  _globals['_ADDMEMBERTODEPARTMENTRESPONSE']._serialized_start=6778
  _globals['_ADDMEMBERTODEPARTMENTRESPONSE']._serialized_end=6843
  _globals['_ORGANISATIONSERVICE']._serialized_start=6998
  _globals['_ORGANISATIONSERVICE']._serialized_end=9068
# @@protoc_insertion_point(module_scope)
