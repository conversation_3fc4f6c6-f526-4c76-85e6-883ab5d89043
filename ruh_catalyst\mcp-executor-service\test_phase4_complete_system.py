#!/usr/bin/env python3
"""
Phase 4: Complete System Testing and Validation
Tests the entire MCP abstraction layer with environment switching and large response handling.
"""

import os
import sys
import asyncio
import logging
import json
from typing import Dict, Any

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_configuration_loading():
    """Test that all configuration is loaded correctly."""
    try:
        from app.config.config import settings
        
        print("✅ Configuration loaded successfully:")
        print(f"  - USE_MCP_SDK: {settings.use_mcp_sdk}")
        print(f"  - MCP_SDK_TIMEOUT: {settings.mcp_sdk_timeout}")
        print(f"  - MCP_SDK_BUFFER_SIZE: {settings.mcp_sdk_buffer_size}")
        print(f"  - Environment: {os.getenv('ENV', 'dev')}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_factory_environment_switching():
    """Test environment-based factory switching."""
    try:
        from app.core_.mcp_client_factory import MCPClientFactory
        from app.config.config import refresh_settings
        
        print("\n🔄 Testing environment-based factory switching:")
        
        # Test dev environment (should use SDK)
        os.environ["ENV"] = "dev"
        os.environ.pop("USE_MCP_SDK", None)  # Remove explicit override
        refresh_settings()
        
        client_dev = MCPClientFactory.create_client()
        dev_uses_sdk = client_dev.__class__.__name__ == "MCPSDKClient"
        print(f"  - ENV=dev -> {client_dev.__class__.__name__} (SDK: {dev_uses_sdk})")
        
        # Test prod environment (should use custom)
        os.environ["ENV"] = "prod"
        os.environ.pop("USE_MCP_SDK", None)  # Remove explicit override
        refresh_settings()
        
        client_prod = MCPClientFactory.create_client()
        prod_uses_custom = client_prod.__class__.__name__ == "MCPCustomClient"
        print(f"  - ENV=prod -> {client_prod.__class__.__name__} (Custom: {prod_uses_custom})")
        
        # Test explicit override
        os.environ["USE_MCP_SDK"] = "false"
        refresh_settings()
        
        client_override = MCPClientFactory.create_client()
        override_uses_custom = client_override.__class__.__name__ == "MCPCustomClient"
        print(f"  - USE_MCP_SDK=false -> {client_override.__class__.__name__} (Custom: {override_uses_custom})")
        
        # Reset environment
        os.environ.pop("USE_MCP_SDK", None)
        os.environ["ENV"] = "dev"
        refresh_settings()
        
        success = dev_uses_sdk and prod_uses_custom and override_uses_custom
        if success:
            print("✅ Environment switching works correctly")
        else:
            print("❌ Environment switching failed")
        
        return success
        
    except Exception as e:
        print(f"❌ Environment switching test failed: {e}")
        return False

async def test_sdk_client_functionality():
    """Test SDK client basic functionality."""
    try:
        from app.core_.mcp_sdk_client import MCPSDKClient
        
        print("\n🧪 Testing SDK client functionality:")
        
        client = MCPSDKClient()
        
        # Test connection
        config = {
            "connection_type": "ssh_docker",
            "container_name": "test_container",
            "ssh_host": "test_host",
            "ssh_user": "test_user"
        }
        
        await client.connect(config)
        print("  - Connection: ✅")
        
        # Test is_connected
        connected = await client.is_connected()
        print(f"  - Is Connected: {'✅' if connected else '❌'}")
        
        # Test list_tools
        tools = await client.list_tools()
        print(f"  - List Tools: ✅ ({len(tools)} tools)")
        
        # Test call_tool
        result = await client.call_tool("test_tool", {"message": "test"})
        print(f"  - Call Tool: ✅ (result type: {type(result).__name__})")
        
        # Test health_check
        health = await client.health_check()
        print(f"  - Health Check: ✅ (status: {health.get('status', 'unknown')})")
        
        # Test close
        await client.close()
        print("  - Close: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ SDK client test failed: {e}")
        return False

async def test_factory_with_fallback():
    """Test factory fallback functionality."""
    try:
        from app.core_.mcp_client_factory import MCPClientFactory
        
        print("\n🔄 Testing factory fallback functionality:")
        
        # Test creating client with fallback
        config = {"connection_type": "test", "server_url": "http://test"}
        
        # Try SDK first
        client_sdk_first = MCPClientFactory.create_client_with_fallback(
            config, prefer_sdk=True
        )
        print(f"  - SDK-first fallback: ✅ ({client_sdk_first.__class__.__name__})")
        
        # Try custom first
        client_custom_first = MCPClientFactory.create_client_with_fallback(
            config, prefer_sdk=False
        )
        print(f"  - Custom-first fallback: ✅ ({client_custom_first.__class__.__name__})")
        
        return True
        
    except Exception as e:
        print(f"❌ Factory fallback test failed: {e}")
        return False

def test_implementation_info():
    """Test factory implementation information."""
    try:
        from app.core_.mcp_client_factory import MCPClientFactory
        
        print("\n📊 Testing implementation information:")
        
        info = MCPClientFactory.get_implementation_info()
        
        print(f"  - Available implementations: {info['available_implementations']}")
        print(f"  - Current default: {info['current_default']}")
        print(f"  - SDK benefits: {len(info['sdk_benefits'])} listed")
        print(f"  - Custom benefits: {len(info['custom_benefits'])} listed")
        
        required_keys = ['available_implementations', 'current_default', 'configuration']
        success = all(key in info for key in required_keys)
        
        if success:
            print("✅ Implementation info complete")
        else:
            print("❌ Implementation info incomplete")
        
        return success
        
    except Exception as e:
        print(f"❌ Implementation info test failed: {e}")
        return False

async def test_large_response_simulation():
    """Simulate large response handling."""
    try:
        from app.core_.mcp_sdk_client import MCPSDKClient
        from app.config.config import settings
        
        print("\n📦 Testing large response simulation:")
        
        client = MCPSDKClient()
        
        # Test with large mock data
        config = {"connection_type": "test"}
        await client.connect(config)
        
        # Simulate large response
        large_data = "x" * (settings.mcp_sdk_buffer_size // 2)  # Half buffer size
        result = await client.call_tool("large_test", {"data": large_data})
        
        print(f"  - Large response handling: ✅ (buffer size: {settings.mcp_sdk_buffer_size})")
        print(f"  - Test data size: {len(large_data)} bytes")
        print(f"  - Result received: {'✅' if result else '❌'}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Large response test failed: {e}")
        return False

def test_executor_integration():
    """Test integration with executor service."""
    try:
        from app.core_.mcp_executor import MCPExecutor
        from app.core_.mcp_client_factory import create_mcp_client
        
        print("\n🔗 Testing executor integration:")
        
        # Mock producer for testing
        class MockProducer:
            async def send(self, topic, message, headers=None):
                pass
        
        # Create executor
        executor = MCPExecutor(MockProducer())
        print("  - Executor creation: ✅")
        
        # Test client creation through factory
        client = create_mcp_client()
        print(f"  - Factory client creation: ✅ ({client.__class__.__name__})")
        
        return True
        
    except Exception as e:
        print(f"❌ Executor integration test failed: {e}")
        return False

async def main():
    """Run all Phase 4 tests."""
    print("🚀 Phase 4: Complete System Testing and Validation")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
    
    # Synchronous tests
    sync_tests = [
        ("Configuration Loading", test_configuration_loading),
        ("Factory Environment Switching", test_factory_environment_switching),
        ("Implementation Info", test_implementation_info),
        ("Executor Integration", test_executor_integration),
    ]
    
    # Asynchronous tests
    async_tests = [
        ("SDK Client Functionality", test_sdk_client_functionality),
        ("Factory Fallback", test_factory_with_fallback),
        ("Large Response Simulation", test_large_response_simulation),
    ]
    
    results = []
    
    # Run synchronous tests
    for test_name, test_func in sync_tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Run asynchronous tests
    for test_name, test_func in async_tests:
        print(f"\n📋 {test_name}:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Phase 4 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for production.")
        print("\n🚀 MCP SDK abstraction layer implementation complete!")
        print("\n📋 Summary of achievements:")
        print("  ✅ Phase 1: MCP SDK dependencies and configuration")
        print("  ✅ Phase 2: SDK abstraction layer with interface")
        print("  ✅ Phase 3: Environment-based client factory")
        print("  ✅ Phase 4: Complete system testing and validation")
        print("\n🔧 Ready to resolve buffer overflow issues with:")
        print("  - Environment-based SDK/Custom switching")
        print("  - Configurable buffer sizes (1MB default)")
        print("  - Fallback mechanisms for reliability")
        print("  - Cross-platform compatibility")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
