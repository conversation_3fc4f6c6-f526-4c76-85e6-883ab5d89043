import React from "react";
import { InputDefinition } from "@/types";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";

interface BooleanInputProps {
  inputDef: InputDefinition;
  value: boolean;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering boolean inputs
 */
export function BooleanInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: BooleanInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  
  return (
    <div className="mt-2 flex items-center space-x-2">
      <Switch
        id={inputId}
        checked={!!value}
        onCheckedChange={(checked) => onChange(inputDef.name, checked)}
        disabled={isDisabled}
        className={isDisabled ? "opacity-50" : ""}
      />
      <Label htmlFor={inputId} className="text-xs">
        {value ? "Enabled" : "Disabled"}
      </Label>
      {isConnected && <ConnectedIndicator className="ml-2" />}
    </div>
  );
}
