#!/usr/bin/env python3
"""
OAuth Integration Test

This script tests the OAuth integration between the API gateway
and authentication service to ensure the migration is working correctly.
"""

import asyncio
import sys
import os

# Add API gateway to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'api-gateway'))

async def test_oauth_imports():
    """Test that OAuth-related imports work correctly."""
    print("🔍 Testing OAuth imports...")
    
    try:
        # Test authentication service client import
        from app.services.authentication_service import AuthenticationServiceClient
        print("  ✅ Authentication service client imported")
        
        # Test OAuth provider enum
        from app.core.oauth_providers import OAuthProvider
        print("  ✅ OAuth provider enum imported")
        
        # Test OAuth schemas
        from app.schemas.oauth import (
            OAuthCredentialResponse,
            OAuthProvidersListResponse,
            ServerOAuthCredentialResponse
        )
        print("  ✅ OAuth schemas imported")
        
        # Test gRPC proto imports
        from app.grpc_ import authentication_pb2, authentication_pb2_grpc
        print("  ✅ gRPC proto files imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import test failed: {e}")
        return False


async def test_authentication_service_client():
    """Test authentication service client creation."""
    print("\n🔗 Testing authentication service client...")
    
    try:
        from app.services.authentication_service import AuthenticationServiceClient
        
        # Create client instance
        client = AuthenticationServiceClient()
        print("  ✅ Authentication service client created")
        
        # Test client has required methods
        required_methods = [
            'list_oauth_providers',
            'get_tool_scopes', 
            'initiate_oauth',
            'handle_oauth_callback',
            'get_oauth_credentials',
            'get_server_oauth_credentials',
            'health_check'
        ]
        
        for method in required_methods:
            if hasattr(client, method):
                print(f"  ✅ Method {method} available")
            else:
                print(f"  ❌ Method {method} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Client test failed: {e}")
        return False


async def test_oauth_routes_import():
    """Test that OAuth routes can be imported."""
    print("\n📡 Testing OAuth routes import...")
    
    try:
        # Test generalized auth routes (updated)
        from app.api.routers import generalized_auth_routes
        print("  ✅ Generalized auth routes imported")
        
        # Test v2 auth routes
        from app.api.routers import generalized_auth_routes_v2
        print("  ✅ Generalized auth routes v2 imported")
        
        # Test legacy auth routes
        from app.api.routers import auth_routes
        print("  ✅ Legacy auth routes imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Routes import test failed: {e}")
        return False


async def test_grpc_message_creation():
    """Test gRPC message creation."""
    print("\n📨 Testing gRPC message creation...")
    
    try:
        from app.grpc_ import authentication_pb2
        from app.core.oauth_providers import OAuthProvider
        
        # Test OAuth authorize request
        request = authentication_pb2.OAuthAuthorizeRequest(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider=authentication_pb2.OAUTH_PROVIDER_GOOGLE,
            scopes=["https://www.googleapis.com/auth/calendar"]
        )
        print("  ✅ OAuth authorize request created")
        print(f"    User ID: {request.user_id}")
        print(f"    Tool: {request.tool_name}")
        print(f"    Provider: {request.provider}")
        
        # Test OAuth callback request
        callback_request = authentication_pb2.OAuthCallbackRequest(
            code="test_code",
            state="test_state",
            error=""
        )
        print("  ✅ OAuth callback request created")
        
        # Test credential request
        cred_request = authentication_pb2.OAuthCredentialRequest(
            user_id="test_user",
            mcp_id="test_mcp", 
            tool_name="google_calendar",
            provider=authentication_pb2.OAUTH_PROVIDER_GOOGLE
        )
        print("  ✅ OAuth credential request created")
        
        return True
        
    except Exception as e:
        print(f"  ❌ gRPC message test failed: {e}")
        return False


async def test_oauth_provider_enum():
    """Test OAuth provider enum functionality."""
    print("\n🔧 Testing OAuth provider enum...")
    
    try:
        from app.core.oauth_providers import OAuthProvider
        
        # Test enum values
        providers = [
            OAuthProvider.GOOGLE,
            OAuthProvider.MICROSOFT,
            OAuthProvider.GITHUB,
            OAuthProvider.CUSTOM
        ]
        
        for provider in providers:
            print(f"  ✅ Provider {provider.value} available")
        
        # Test enum conversion
        google_provider = OAuthProvider("google")
        assert google_provider == OAuthProvider.GOOGLE
        print("  ✅ String to enum conversion works")
        
        return True
        
    except Exception as e:
        print(f"  ❌ OAuth provider enum test failed: {e}")
        return False


async def main():
    """Run all OAuth integration tests."""
    print("🚀 Starting OAuth Integration Tests\n")
    
    tests = [
        ("OAuth Imports", test_oauth_imports),
        ("Authentication Service Client", test_authentication_service_client),
        ("OAuth Routes Import", test_oauth_routes_import),
        ("gRPC Message Creation", test_grpc_message_creation),
        ("OAuth Provider Enum", test_oauth_provider_enum),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 OAUTH INTEGRATION TEST RESULTS")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All OAuth integration tests passed!")
        print("\n📋 OAuth Migration Status:")
        print("✅ Authentication service client working")
        print("✅ gRPC communication ready")
        print("✅ OAuth routes updated")
        print("✅ Provider management functional")
        print("\n🚀 OAuth migration to authentication service is COMPLETE!")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test runner crashed: {e}")
        sys.exit(1)
