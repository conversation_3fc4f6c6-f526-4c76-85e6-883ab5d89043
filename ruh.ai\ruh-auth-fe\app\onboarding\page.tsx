"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { userApi } from "@/app/api/user";
import { UserProfileUpdateRequest } from "@/app/shared/interfaces";
import { toast } from "sonner";
import { onboardingSchema } from "@/lib/schemas/onboarding";
import { onboardingDepartmentsAndRoles } from "../shared/constants";
import {
  getAccessToken,
  getRefreshToken,
  setAuthCookies,
  clearAuthCookies,
} from "@/services/authCookies";
import { getRedirectUrl } from "@/services/helper";
import { LoadingScreen } from "@/components/shared/LoadingScreen";
import { hasCompletedOnboarding } from "../api/auth";

const departmentsData = onboardingDepartmentsAndRoles[0].departments;
// Define a constant for the 'Other' value used in this component
const OTHER_VALUE = "Other";

export default function OnboardingPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [userName, setUserName] = useState<string | null>(null);

  const [rolesForSelectedDepartment, setRolesForSelectedDepartment] = useState<
    string[]
  >([]);
  const [redirect_url, setRedirect_url] = useState<string | null>(
    process.env.NEXT_PUBLIC_DEFAULT_REDIRECT!
  );

  const router = useRouter();

  const form = useForm<z.infer<typeof onboardingSchema>>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      company: "",
      department: "",
      customDepartment: "",
      role: undefined,
      customRole: "",
    },
  });

  // Watch for changes in department and role fields to manage dependent form state
  const watchedDepartment = form.watch("department");
  const watchedRole = form.watch("role");

  useEffect(() => {
    const initializePage = async () => {
      try {
        // Get user info
        const user = await userApi.getCurrentUser();
        setUserName(user.fullName);

        // Get and set redirect URL
        const redirectUrl = getRedirectUrl();
        if (redirectUrl) {
          setRedirect_url(redirectUrl);
        }

        // Check onboarding status after redirect URL is set
        const hasCompleted = await hasCompletedOnboarding();
        if (hasCompleted) {
          // Use the redirectUrl directly instead of from state
          router.push(redirectUrl || process.env.NEXT_PUBLIC_DEFAULT_REDIRECT!);
        }
      } catch (error) {
        console.error("Error initializing page:", error);
        toast.error("Failed to initialize page");
      } finally {
        setPageLoading(false);
      }
    };

    initializePage();
  }, [router]);

  useEffect(() => {
    // Initialize state management flags
    let newRoles: string[] = [];
    let shouldResetRole = false;
    let shouldResetCustomDepartment = false;
    let shouldResetCustomRole = false;

    // Case 1: Department is selected and it's not "Other"
    if (watchedDepartment && watchedDepartment !== OTHER_VALUE) {
      // Find the roles associated with the selected department
      const selectedDeptData = departmentsData.find(
        (dept) => dept.name === watchedDepartment
      );
      newRoles = selectedDeptData ? selectedDeptData.roles : [];
      shouldResetCustomDepartment = true; // Clear custom department as standard dept is selected

      // Check if current role is valid for the new department
      const currentRole = form.getValues("role");
      if (currentRole === undefined || !newRoles.includes(currentRole)) {
        shouldResetRole = true; // Reset role if it's not valid for new department
      }

      // Clear custom role if a standard role is selected
      if (watchedRole && watchedRole !== OTHER_VALUE) {
        shouldResetCustomRole = true;
      }
    }
    // Case 2: Department is "Other"
    else if (watchedDepartment === OTHER_VALUE) {
      newRoles = []; // No predefined roles for custom department
      shouldResetRole = true; // Reset role selection
    }
    // Case 3: No department selected
    else {
      newRoles = []; // Clear roles list
      shouldResetRole = true; // Reset role selection
      shouldResetCustomDepartment = true; // Reset custom department
      shouldResetCustomRole = true; // Reset custom role
    }

    // Update available roles for the selected department
    setRolesForSelectedDepartment(newRoles);

    // Apply form field resets based on flags
    if (shouldResetRole && form.getValues("role") !== undefined) {
      form.setValue("role", undefined);
    }
    if (
      shouldResetCustomDepartment &&
      form.getValues("customDepartment") !== ""
    ) {
      form.setValue("customDepartment", "");
    }
    if (shouldResetCustomRole && form.getValues("customRole") !== "") {
      form.setValue("customRole", "");
    }
  }, [watchedDepartment, watchedRole, form]);

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof onboardingSchema>) => {
    const { company, department, customDepartment, role, customRole } = data;

    setIsLoading(true);
    // Prepare final data object with appropriate values based on selection type
    const finalData = {
      company: company,
      // Use custom department if "Other" was selected, otherwise use standard department
      department: department === OTHER_VALUE ? customDepartment : department,
      // Role selection logic:
      // 1. If department is "Other", use custom role
      // 2. If standard department but role is "Other", use custom role
      // 3. Otherwise use standard role
      job_role:
        department === OTHER_VALUE
          ? customRole
          : role === OTHER_VALUE
          ? customRole
          : role,
    };

    // Prepare data for the API call, ensuring optional fields are handled
    const apiData: UserProfileUpdateRequest = {
      company: finalData.company || null, // Send null if empty string
      department: finalData.department || null,
      job_role: finalData.job_role || null,
    };

    try {
      await userApi.updateUserProfile(apiData);

      // Get current tokens from cookies
      const accessToken = await getAccessToken();
      const refreshToken = await getRefreshToken();

      if (accessToken && refreshToken) {
        // Clear local cookies first
        await clearAuthCookies(false);

        // Set domain-wide cookies after successful onboarding
        await setAuthCookies(
          accessToken,
          refreshToken,
          3600, // 1 hour for access token
          86400, // 24 hours for refresh token
          true // Set domain-wide cookies
        );
      }

      toast.success("Profile updated successfully!"); // Use toast for feedback
      router.push(redirect_url!);
    } catch (error: any) {
      // Display specific error message from API if available, otherwise generic
      toast.error(
        error.message || "Failed to save onboarding details. Please try again."
      );
    } finally {
      setIsLoading(false); // Ensure loading state is turned off
    }
  };

  if (pageLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="flex items-center justify-center p-4 min-h-screen">
      <div className="max-w-[600px] flex flex-col gap-6 w-full">
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl font-bold text-brand-primary-font font-primary">
            Hi {userName}!
          </h1>
          <p className="text-brand-secondary-font text-lg">Welcome to RUH.AI</p>
        </div>

        <p className="text-brand-primary-font text-lg">
          Before you start automating with our soulful digital employees, we
          want to know more about you, so:
        </p>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-6"
          >
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="What company do you work in?"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Department <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                    }}
                    defaultValue={field.value}
                    disabled={isLoading}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="What department do you contribute to?" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {departmentsData.map((department) => (
                        <SelectItem
                          key={department.name}
                          value={department.name}
                        >
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Custom Department Input - Shown only if Department is 'Other' */}
            {watchedDepartment === OTHER_VALUE && (
              <FormField
                control={form.control}
                name="customDepartment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Specify Department <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Please specify your department"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Role Selection - Show unless Department is 'Other'. Disabled if no Department selected. */}
            {watchedDepartment !== OTHER_VALUE && (
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Role <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value ?? ""}
                      disabled={isLoading || !watchedDepartment}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              !watchedDepartment
                                ? "Select a department first"
                                : "What is your role in said department?"
                            }
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {rolesForSelectedDepartment.map((roleName) => (
                          <SelectItem key={roleName} value={roleName}>
                            {roleName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Custom Role Input - shown only if department is selected, is not 'Other', AND Role selected is 'Other' */}
            {watchedDepartment &&
              watchedDepartment !== OTHER_VALUE &&
              watchedRole === OTHER_VALUE && (
                <FormField
                  control={form.control}
                  name="customRole"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Specify Role <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Please specify your role"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

            {/* Custom Role Input - shown ONLY if Department is 'Other' (replaces Role Select) */}
            {watchedDepartment === OTHER_VALUE && (
              <FormField
                control={form.control}
                name="customRole"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Specify Role <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Please specify your role"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end w-full ">
              <PrimaryButton
                className="max-w-[150px] rounded-sm"
                isLoading={isLoading}
              >
                {isLoading ? "Saving..." : "Submit"}
              </PrimaryButton>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
