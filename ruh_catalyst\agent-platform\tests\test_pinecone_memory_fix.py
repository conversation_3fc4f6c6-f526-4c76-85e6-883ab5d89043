#!/usr/bin/env python3
"""
Test script to verify the Pinecone memory update_context fix.

This script tests that the update_context method works correctly
with the AutoGen Memory interface.
"""

import asyncio
import logging
import os
import sys
from typing import List

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.memory import PineconeMemory
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_agentchat.messages import TextMessage, UserMessage

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_update_context_method():
    """Test the update_context method with correct signature."""
    try:
        logger.info("Testing Pinecone memory update_context method...")
        
        # Create a Pinecone memory instance
        memory = PineconeMemory(
            agent_id="test-agent-fix",
            user_id="test-user-fix",
            namespace="test-fix",
            auto_create_index=True
        )
        
        # Create a model context
        context = BufferedChatCompletionContext(buffer_size=1000)
        
        # Add a test message to the context
        test_message = UserMessage(content="What is Python programming?", source="user")
        await context.add_message(test_message)
        
        # Test the update_context method (this should not raise an error)
        await memory.update_context(context)
        
        logger.info("✅ update_context method works correctly!")
        
        # Get messages from context to verify memory was added
        messages = await context.get_messages()
        logger.info(f"Context now has {len(messages)} messages")
        
        # Check if memory context was added
        memory_added = False
        for message in messages:
            if hasattr(message, 'content') and "Relevant memories:" in str(message.content):
                memory_added = True
                logger.info("✅ Memory context was added to the conversation")
                break
        
        if not memory_added:
            logger.info("ℹ️ No relevant memories found (this is expected for a new agent)")
        
        # Clean up
        await memory.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


async def test_memory_interface_compatibility():
    """Test that PineconeMemory is compatible with AutoGen's Memory interface."""
    try:
        logger.info("Testing Memory interface compatibility...")
        
        from autogen_core.memory import Memory
        
        # Create a Pinecone memory instance
        memory = PineconeMemory(
            agent_id="test-interface",
            user_id="test-user",
            namespace="test-interface"
        )
        
        # Check if it's an instance of Memory
        if isinstance(memory, Memory):
            logger.info("✅ PineconeMemory correctly implements Memory interface")
        else:
            logger.error("❌ PineconeMemory does not implement Memory interface")
            return False
        
        # Check if required methods exist
        required_methods = ['add', 'query', 'update_context', 'clear']
        for method_name in required_methods:
            if hasattr(memory, method_name):
                logger.info(f"✅ Method '{method_name}' exists")
            else:
                logger.error(f"❌ Method '{method_name}' missing")
                return False
        
        # Test method signatures
        import inspect
        
        # Check update_context signature
        update_context_sig = inspect.signature(memory.update_context)
        params = list(update_context_sig.parameters.keys())
        
        # Should have 'self' and 'context' parameters only
        expected_params = ['context']  # 'self' is implicit
        actual_params = [p for p in params if p != 'self']
        
        if actual_params == expected_params:
            logger.info("✅ update_context method has correct signature")
        else:
            logger.error(f"❌ update_context signature incorrect. Expected {expected_params}, got {actual_params}")
            return False
        
        await memory.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Interface compatibility test failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("TESTING PINECONE MEMORY FIX")
    logger.info("=" * 60)
    
    tests = [
        ("Memory Interface Compatibility", test_memory_interface_compatibility),
        ("Update Context Method", test_update_context_method),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running test: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"Test '{test_name}': {status}")
        except Exception as e:
            logger.error(f"Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The fix is working correctly.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
