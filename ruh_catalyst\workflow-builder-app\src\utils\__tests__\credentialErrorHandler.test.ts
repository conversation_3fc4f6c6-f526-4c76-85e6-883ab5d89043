/**
 * Tests for credential error handling utilities
 * Following TDD methodology - tests written first
 */

import {
  handleCredentialError,
  getErrorMessage,
  shouldRetry,
  isRetryableError,
  createCredentialError,
} from '../credentialErrorHandler';
import { CredentialErrorType, CredentialError } from '../../types/credentials';

describe('credentialErrorHandler', () => {
  describe('handleCredentialError', () => {
    it('should handle 401 authentication errors', () => {
      const axiosError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized access' }
        }
      };

      const result = handleCredentialError(axiosError);

      expect(result).toEqual({
        type: CredentialErrorType.AUTHENTICATION_ERROR,
        message: 'Authentication required. Please log in again.',
        code: '401',
        originalError: axiosError
      });
    });

    it('should handle 403 permission errors', () => {
      const axiosError = {
        response: {
          status: 403,
          data: { message: 'Forbidden' }
        }
      };

      const result = handleCredentialError(axiosError);

      expect(result).toEqual({
        type: CredentialErrorType.PERMISSION_ERROR,
        message: 'You do not have permission to perform this action.',
        code: '403',
        originalError: axiosError
      });
    });

    it('should handle 404 not found errors', () => {
      const axiosError = {
        response: {
          status: 404,
          data: { message: 'Credential not found' }
        }
      };

      const result = handleCredentialError(axiosError);

      expect(result).toEqual({
        type: CredentialErrorType.NOT_FOUND_ERROR,
        message: 'Credential not found.',
        code: '404',
        originalError: axiosError
      });
    });

    it('should handle 400 validation errors with field information', () => {
      const axiosError = {
        response: {
          status: 400,
          data: { 
            message: 'Invalid key_name',
            field: 'key_name'
          }
        }
      };

      const result = handleCredentialError(axiosError);

      expect(result).toEqual({
        type: CredentialErrorType.VALIDATION_ERROR,
        message: 'Invalid key_name',
        field: 'key_name',
        code: '400',
        originalError: axiosError
      });
    });

    it('should handle 500 server errors', () => {
      const axiosError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' }
        }
      };

      const result = handleCredentialError(axiosError);

      expect(result).toEqual({
        type: CredentialErrorType.SERVER_ERROR,
        message: 'Server error occurred. Please try again later.',
        code: '500',
        originalError: axiosError
      });
    });

    it('should handle network errors without response', () => {
      const networkError = {
        message: 'Network Error',
        code: 'NETWORK_ERROR'
      };

      const result = handleCredentialError(networkError);

      expect(result).toEqual({
        type: CredentialErrorType.NETWORK_ERROR,
        message: 'Network error. Please check your connection and try again.',
        code: 'NETWORK_ERROR',
        originalError: networkError
      });
    });

    it('should handle timeout errors', () => {
      const timeoutError = {
        message: 'timeout of 5000ms exceeded',
        code: 'ECONNABORTED'
      };

      const result = handleCredentialError(timeoutError);

      expect(result).toEqual({
        type: CredentialErrorType.NETWORK_ERROR,
        message: 'Request timeout. Please try again.',
        code: 'ECONNABORTED',
        originalError: timeoutError
      });
    });

    it('should handle unknown errors', () => {
      const unknownError = {
        someProperty: 'unknown error structure'
      };

      const result = handleCredentialError(unknownError);

      expect(result).toEqual({
        type: CredentialErrorType.UNKNOWN_ERROR,
        message: 'An unexpected error occurred. Please try again.',
        originalError: unknownError
      });
    });

    it('should handle string errors', () => {
      const stringError = 'Something went wrong';

      const result = handleCredentialError(stringError);

      expect(result).toEqual({
        type: CredentialErrorType.UNKNOWN_ERROR,
        message: 'Something went wrong',
        originalError: stringError
      });
    });
  });

  describe('getErrorMessage', () => {
    it('should return the error message for credential errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.VALIDATION_ERROR,
        message: 'Invalid credential data',
        field: 'name'
      };

      const result = getErrorMessage(error);

      expect(result).toBe('Invalid credential data');
    });

    it('should return field-specific message for validation errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.VALIDATION_ERROR,
        message: 'Field is required',
        field: 'value'
      };

      const result = getErrorMessage(error);

      expect(result).toBe('Field is required');
    });

    it('should return the message even for unknown error types', () => {
      const error = {
        type: 'UNKNOWN_TYPE',
        message: 'Some message'
      } as any;

      const result = getErrorMessage(error);

      expect(result).toBe('Some message');
    });
  });

  describe('shouldRetry', () => {
    it('should return true for network errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.NETWORK_ERROR,
        message: 'Network error'
      };

      expect(shouldRetry(error)).toBe(true);
    });

    it('should return true for server errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.SERVER_ERROR,
        message: 'Server error'
      };

      expect(shouldRetry(error)).toBe(true);
    });

    it('should return false for authentication errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.AUTHENTICATION_ERROR,
        message: 'Auth error'
      };

      expect(shouldRetry(error)).toBe(false);
    });

    it('should return false for validation errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.VALIDATION_ERROR,
        message: 'Validation error'
      };

      expect(shouldRetry(error)).toBe(false);
    });

    it('should return false for not found errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.NOT_FOUND_ERROR,
        message: 'Not found'
      };

      expect(shouldRetry(error)).toBe(false);
    });

    it('should return false for permission errors', () => {
      const error: CredentialError = {
        type: CredentialErrorType.PERMISSION_ERROR,
        message: 'Permission denied'
      };

      expect(shouldRetry(error)).toBe(false);
    });
  });

  describe('isRetryableError', () => {
    it('should identify retryable error types', () => {
      expect(isRetryableError(CredentialErrorType.NETWORK_ERROR)).toBe(true);
      expect(isRetryableError(CredentialErrorType.SERVER_ERROR)).toBe(true);
      expect(isRetryableError(CredentialErrorType.AUTHENTICATION_ERROR)).toBe(false);
      expect(isRetryableError(CredentialErrorType.VALIDATION_ERROR)).toBe(false);
      expect(isRetryableError(CredentialErrorType.NOT_FOUND_ERROR)).toBe(false);
      expect(isRetryableError(CredentialErrorType.PERMISSION_ERROR)).toBe(false);
      expect(isRetryableError(CredentialErrorType.UNKNOWN_ERROR)).toBe(false);
    });
  });

  describe('createCredentialError', () => {
    it('should create a credential error with all properties', () => {
      const result = createCredentialError(
        CredentialErrorType.VALIDATION_ERROR,
        'Invalid input',
        'name',
        '400',
        { original: 'error' }
      );

      expect(result).toEqual({
        type: CredentialErrorType.VALIDATION_ERROR,
        message: 'Invalid input',
        field: 'name',
        code: '400',
        originalError: { original: 'error' }
      });
    });

    it('should create a credential error with minimal properties', () => {
      const result = createCredentialError(
        CredentialErrorType.NETWORK_ERROR,
        'Network failed'
      );

      expect(result).toEqual({
        type: CredentialErrorType.NETWORK_ERROR,
        message: 'Network failed',
        field: undefined,
        code: undefined,
        originalError: undefined
      });
    });
  });
});
