"use client";

import { useState, useEffect } from "react";
import { validatePassword } from "@/lib/utils/password-validation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { resetPasswordSchema, ResetPasswordType } from "@/lib/schemas/auth";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { useRouter } from "next/navigation";
import { authApi } from "@/app/api/auth";
import { loginRoute } from "@/app/shared/routes";
import { PasswordValidationIndicator } from "../../../_components/PasswordValidationIndicator";

export function UpdatePasswordForm({ token }: { token: string }) {
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);
  const [passwordValidations, setPasswordValidations] = useState({
    length: false,
    hasNumber: false,
    hasSymbol: false,
  });
  const [isFormValid, setIsFormValid] = useState(false);
  const [formSubmitAttempted, setFormSubmitAttempted] = useState(false);

  const router = useRouter();

  const form = useForm<ResetPasswordType>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      newPassword: "",
      confirmNewPassword: "",
    },
  });

  const { mutate: resetPasswordMutation, isPending } = useMutation({
    mutationFn: (data: ResetPasswordType) => authApi.resetPassword(token, data),
    onSuccess: (data) => {
      toast.success(data.message);
      router.push(loginRoute);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Password reset failed. Please try again.");
    },
  });

  // Update password validations when password changes
  const updatePasswordValidations = (password: string) => {
    setPasswordValidations(validatePassword(password));
  };

  // Watch for all form changes to update validations
  useEffect(() => {
    const subscription = form.watch((values) => {
      // Update password validations for the indicator only
      const password = values.newPassword || "";
      updatePasswordValidations(password);

      // Enable button when both fields have any content
      const passwordsFilled =
        Boolean(values.newPassword) && Boolean(values.confirmNewPassword);

      setIsFormValid(passwordsFilled);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  function onSubmit(data: ResetPasswordType) {
    setFormSubmitAttempted(true);
    resetPasswordMutation(data);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(
          onSubmit,
          // This runs when form validation fails
          () => setFormSubmitAttempted(true)
        )}
        className="space-y-6"
      >
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="newPassword"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Enter your new password"
                      type={showNewPassword ? "text" : "password"}
                      {...field}
                      disabled={isPending}
                      className="h-11 bg-background"
                      onChange={(e) => {
                        field.onChange(e);
                        updatePasswordValidations(e.target.value);
                      }}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowNewPassword((prev) => !prev)}
                      disabled={isPending}
                    >
                      {showNewPassword ? (
                        <EyeOffIcon className="h-4 w-4 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-4 w-4 text-gray-500" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmNewPassword"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Confirm your new password"
                      type={showConfirmNewPassword ? "text" : "password"}
                      {...field}
                      disabled={isPending}
                      className="h-11 bg-background"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmNewPassword((prev) => !prev)}
                      disabled={isPending}
                    >
                      {showConfirmNewPassword ? (
                        <EyeOffIcon className="h-4 w-4 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-4 w-4 text-gray-500" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <PasswordValidationIndicator
          password={form.getValues("newPassword") || ""}
          showValidation={formSubmitAttempted}
        />

        <PrimaryButton
          isLoading={isPending}
          type="submit"
          className="w-full h-11 mt-6"
          disabled={!isFormValid || isPending}
        >
          {isPending ? "Resetting Password..." : "Change Password"}
        </PrimaryButton>
      </form>
    </Form>
  );
}
