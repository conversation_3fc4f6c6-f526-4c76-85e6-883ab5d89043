/**
 * Configuration system for the universal dynamic input type handling
 * Allows gradual migration and feature flags
 */

export interface InputSystemConfig {
  // Global feature flags
  enableUniversalDynamicSystem: boolean;
  enableLegacyFallback: boolean;
  enableMigrationLogging: boolean;
  
  // Component-specific overrides
  componentOverrides: Record<string, {
    useDynamicSystem: boolean;
    inputTypeOverrides?: Record<string, string>;
  }>;
  
  // Input type specific settings
  inputTypeSettings: Record<string, {
    preferredComponent: string;
    validationLevel: 'strict' | 'lenient' | 'disabled';
    fallbackBehavior: 'error' | 'warn' | 'silent';
  }>;
  
  // CLEANED UP: Migration settings simplified since system is now universal
  migration: {
    enableGradualRollout: boolean; // Kept for potential future use
    rolloutPercentage: number; // Always 100% now
    blacklistedComponents: string[]; // Emergency disable list
  };
}

/**
 * Default configuration - ENABLED FOR ALL COMPONENTS
 */
const DEFAULT_CONFIG: InputSystemConfig = {
  enableUniversalDynamicSystem: true, // ✅ ENABLED GLOBALLY
  enableLegacyFallback: true, // ✅ SAFETY: Keep legacy fallback
  enableMigrationLogging: true, // ✅ MONITORING: Log all transitions
  
  componentOverrides: {
    // ✅ ALL COMPONENTS NOW USE DYNAMIC SYSTEM BY DEFAULT
    // MCP components
    'MCPMarketplaceComponent': { useDynamicSystem: true },
    'MCPToolsComponent': { useDynamicSystem: true },

    // Built-in workflow components
    'ApiRequestNode': { useDynamicSystem: true },
    'WebhookComponent': { useDynamicSystem: true },
    'StartNode': { useDynamicSystem: true },
    'EndNode': { useDynamicSystem: true },
    'ConditionalNode': { useDynamicSystem: true },
    'LoopNode': { useDynamicSystem: true },
    'DelayNode': { useDynamicSystem: true },
    'LogNode': { useDynamicSystem: true },
    'TransformDataNode': { useDynamicSystem: true },
    'SelectDataNode': { useDynamicSystem: true },
    'MergeDataNode': { useDynamicSystem: true },
    'FilterDataNode': { useDynamicSystem: true },
    'SortDataNode': { useDynamicSystem: true },
    'GroupDataNode': { useDynamicSystem: true },
    'AggregateDataNode': { useDynamicSystem: true },
    'MessageToDataNode': { useDynamicSystem: true },
    'DataToDataframeNode': { useDynamicSystem: true },
    'AlterMetadataNode': { useDynamicSystem: true },

    // AI components
    'AgenticAINode': { useDynamicSystem: true },
    'BaseAgentComponent': { useDynamicSystem: true },
    'ChatCompletionNode': { useDynamicSystem: true },
    'EmbeddingNode': { useDynamicSystem: true },
    'ImageGenerationNode': { useDynamicSystem: true },
    'TextToSpeechNode': { useDynamicSystem: true },
    'SpeechToTextNode': { useDynamicSystem: true },

    // Data interaction components
    'DatabaseQueryNode': { useDynamicSystem: true },
    'FileReadNode': { useDynamicSystem: true },
    'FileWriteNode': { useDynamicSystem: true },
    'EmailSendNode': { useDynamicSystem: true },
    'SlackMessageNode': { useDynamicSystem: true },
    'DiscordMessageNode': { useDynamicSystem: true },
    'TwitterPostNode': { useDynamicSystem: true },
    'GoogleSheetsNode': { useDynamicSystem: true },
    'GoogleDriveNode': { useDynamicSystem: true },
    'DropboxNode': { useDynamicSystem: true },
    'OneDriveNode': { useDynamicSystem: true },
    'S3Node': { useDynamicSystem: true },
    'FTPNode': { useDynamicSystem: true },

    // Utility components
    'VariableNode': { useDynamicSystem: true },
    'ConstantNode': { useDynamicSystem: true },
    'RandomNode': { useDynamicSystem: true },
    'DateTimeNode': { useDynamicSystem: true },
    'MathNode': { useDynamicSystem: true },
    'StringManipulationNode': { useDynamicSystem: true },
    'RegexNode': { useDynamicSystem: true },
    'JsonParseNode': { useDynamicSystem: true },
    'JsonStringifyNode': { useDynamicSystem: true },
    'Base64EncodeNode': { useDynamicSystem: true },
    'Base64DecodeNode': { useDynamicSystem: true },
    'HashNode': { useDynamicSystem: true },
    'EncryptNode': { useDynamicSystem: true },
    'DecryptNode': { useDynamicSystem: true },
  },
  
  inputTypeSettings: {
    // High-confidence mappings
    'string': { preferredComponent: 'StringInput', validationLevel: 'lenient', fallbackBehavior: 'silent' },
    'number': { preferredComponent: 'NumberInput', validationLevel: 'lenient', fallbackBehavior: 'silent' },
    'boolean': { preferredComponent: 'BooleanInput', validationLevel: 'lenient', fallbackBehavior: 'silent' },
    'url': { preferredComponent: 'StringInput', validationLevel: 'strict', fallbackBehavior: 'warn' },
    'email': { preferredComponent: 'StringInput', validationLevel: 'strict', fallbackBehavior: 'warn' },
    
    // Medium-confidence mappings
    'object': { preferredComponent: 'ObjectInput', validationLevel: 'lenient', fallbackBehavior: 'warn' },
    'array': { preferredComponent: 'ArrayInput', validationLevel: 'lenient', fallbackBehavior: 'warn' },
    
    // Special types that need careful handling
    'button': { preferredComponent: 'Button', validationLevel: 'disabled', fallbackBehavior: 'error' },
    'handle': { preferredComponent: 'HandleInput', validationLevel: 'disabled', fallbackBehavior: 'error' },
    'credential': { preferredComponent: 'CredentialInput', validationLevel: 'strict', fallbackBehavior: 'error' },
  },
  
  migration: {
    enableGradualRollout: false, // ✅ DISABLED: Universal system active
    rolloutPercentage: 100, // ✅ 100%: All components use dynamic system
    blacklistedComponents: [], // ✅ EMPTY: No components excluded (maximum compatibility)
  }
};

/**
 * Configuration manager
 */
class InputSystemConfigManager {
  private config: InputSystemConfig;
  
  constructor() {
    this.config = { ...DEFAULT_CONFIG };
    this.loadFromEnvironment();
  }
  
  /**
   * Load configuration from environment variables
   */
  private loadFromEnvironment(): void {
    // Check for environment-based overrides
    if (typeof window !== 'undefined') {
      // Client-side environment checks
      const urlParams = new URLSearchParams(window.location.search);
      
      if (urlParams.get('disable-dynamic-inputs') === 'true') {
        this.config.enableUniversalDynamicSystem = false;
      }
      
      if (urlParams.get('enable-migration-logging') === 'true') {
        this.config.enableMigrationLogging = true;
      }
      
      // Check localStorage for persistent settings
      const savedConfig = localStorage.getItem('inputSystemConfig');
      if (savedConfig) {
        try {
          const parsed = JSON.parse(savedConfig);
          this.config = { ...this.config, ...parsed };
        } catch (error) {
          console.warn('Failed to parse saved input system config:', error);
        }
      }
    }
    
    // Server-side environment variables
    if (process.env.DISABLE_DYNAMIC_INPUTS === 'true') {
      this.config.enableUniversalDynamicSystem = false;
    }
    
    if (process.env.INPUT_SYSTEM_ROLLOUT_PERCENTAGE) {
      const percentage = parseInt(process.env.INPUT_SYSTEM_ROLLOUT_PERCENTAGE, 10);
      if (!isNaN(percentage) && percentage >= 0 && percentage <= 100) {
        this.config.migration.rolloutPercentage = percentage;
      }
    }
  }
  
  /**
   * Get the current configuration
   */
  getConfig(): InputSystemConfig {
    return { ...this.config };
  }
  
  /**
   * Update configuration
   */
  updateConfig(updates: Partial<InputSystemConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Save to localStorage if available
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('inputSystemConfig', JSON.stringify(this.config));
      } catch (error) {
        console.warn('Failed to save input system config:', error);
      }
    }
  }
  
  /**
   * Check if dynamic system should be used for a component
   * CLEANED UP: Simplified since system is now universal
   */
  shouldUseDynamicSystem(componentType?: string, nodeId?: string): boolean {
    // Global disable (emergency only)
    if (!this.config.enableUniversalDynamicSystem) {
      return false;
    }

    // Emergency blacklist check (for critical issues only)
    if (componentType && this.config.migration.blacklistedComponents.includes(componentType)) {
      console.warn(`[INPUT SYSTEM CONFIG] Component ${componentType} is blacklisted - using emergency fallback`);
      return false;
    }

    // Component-specific override (for special cases)
    if (componentType && this.config.componentOverrides[componentType]) {
      return this.config.componentOverrides[componentType].useDynamicSystem;
    }

    // Default: Use dynamic system for all components
    return true;
  }
  
  /**
   * Get input type settings
   */
  getInputTypeSettings(inputType: string) {
    return this.config.inputTypeSettings[inputType] || {
      preferredComponent: 'StringInput',
      validationLevel: 'lenient',
      fallbackBehavior: 'warn'
    };
  }
  
  /**
   * Check if migration logging is enabled
   */
  shouldLogMigration(): boolean {
    return this.config.enableMigrationLogging;
  }
  
  /**
   * Simple hash function for consistent rollout
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
  
  /**
   * Reset to default configuration
   */
  resetToDefaults(): void {
    this.config = { ...DEFAULT_CONFIG };
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('inputSystemConfig');
    }
  }
  
  /**
   * Enable dynamic system for specific components
   */
  enableForComponents(componentTypes: string[]): void {
    componentTypes.forEach(type => {
      if (!this.config.componentOverrides[type]) {
        this.config.componentOverrides[type] = { useDynamicSystem: true };
      } else {
        this.config.componentOverrides[type].useDynamicSystem = true;
      }
    });
    
    this.updateConfig(this.config);
  }
  
  /**
   * Disable dynamic system for specific components
   */
  disableForComponents(componentTypes: string[]): void {
    componentTypes.forEach(type => {
      if (!this.config.componentOverrides[type]) {
        this.config.componentOverrides[type] = { useDynamicSystem: false };
      } else {
        this.config.componentOverrides[type].useDynamicSystem = false;
      }
    });
    
    this.updateConfig(this.config);
  }
}

// Global configuration manager instance
export const inputSystemConfig = new InputSystemConfigManager();

/**
 * Convenience functions
 */
export function shouldUseDynamicInputSystem(componentType?: string, nodeId?: string): boolean {
  return inputSystemConfig.shouldUseDynamicSystem(componentType, nodeId);
}

export function getInputTypeSettings(inputType: string) {
  return inputSystemConfig.getInputTypeSettings(inputType);
}

export function logMigrationEvent(event: string, details?: any): void {
  if (inputSystemConfig.shouldLogMigration()) {
    console.log(`[Input System Migration] ${event}`, details);
  }
}

/**
 * Development utilities
 */
export const devUtils = {
  getConfig: () => inputSystemConfig.getConfig(),
  updateConfig: (updates: Partial<InputSystemConfig>) => inputSystemConfig.updateConfig(updates),
  resetConfig: () => inputSystemConfig.resetToDefaults(),
  enableForComponents: (types: string[]) => inputSystemConfig.enableForComponents(types),
  disableForComponents: (types: string[]) => inputSystemConfig.disableForComponents(types),
};
