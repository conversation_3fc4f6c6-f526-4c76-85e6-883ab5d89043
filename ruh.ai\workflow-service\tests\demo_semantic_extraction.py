#!/usr/bin/env python3
"""
Demonstration of semantic type extraction in workflow schema converter.

This script shows how semantic types are extracted from schema 1 (workflow capture schema)
and added to the format field in schema 2 (transition schema) for the orchestration engine.
"""

import json
from app.services.workflow_builder.workflow_schema_converter import (
    extract_semantic_type_from_field,
    convert_node_to_transition_node,
)


def demo_semantic_type_extraction():
    """Demonstrate semantic type extraction functionality."""
    print("🚀 Semantic Type Extraction Demo")
    print("=" * 50)
    
    # Demo 1: Basic semantic type extraction
    print("\n1. Basic Semantic Type Extraction from Field Names:")
    print("-" * 50)
    
    test_fields = [
        ("user_email", {"type": "string", "description": "User email address"}),
        ("website_url", {"type": "string", "description": "Website URL"}),
        ("created_at", {"type": "string", "description": "Creation timestamp"}),
        ("price", {"type": "number", "description": "Product price"}),
        ("profile_image", {"type": "string", "description": "Profile image URL"}),
        ("description", {"type": "string", "description": "General description"}),
    ]
    
    for field_name, field_props in test_fields:
        semantic_type = extract_semantic_type_from_field(field_name, field_props)
        print(f"  {field_name:15} → {semantic_type}")
    
    # Demo 2: Existing format field preservation
    print("\n2. Existing Format Field Preservation:")
    print("-" * 50)
    
    field_with_format = {
        "type": "string",
        "format": "datetime",  # Existing format
        "description": "Custom datetime field"
    }
    
    semantic_type = extract_semantic_type_from_field("custom_field", field_with_format)
    print(f"  custom_field with existing format 'datetime' → {semantic_type}")
    
    # Demo 3: Node conversion with semantic types
    print("\n3. Node Conversion with Semantic Types:")
    print("-" * 50)
    
    # Create a sample node with output schema
    sample_node = {
        "id": "demo_node",
        "data": {
            "type": "mcp",
            "definition": {
                "name": "user_profile_tool",
                "mcp_info": {
                    "tool_name": "get_user_profile",
                    "server_id": "user_service",
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "user_email": {
                                "type": "string",
                                "description": "User email address"
                            },
                            "profile_url": {
                                "type": "string",
                                "description": "Profile URL"
                            },
                            "created_at": {
                                "type": "string",
                                "description": "Account creation date"
                            },
                            "avatar_image": {
                                "type": "string",
                                "description": "Avatar image URL"
                            },
                            "account_balance": {
                                "type": "number",
                                "description": "Account balance"
                            },
                            "bio": {
                                "type": "string",
                                "description": "User biography"
                            }
                        }
                    }
                }
            }
        }
    }
    
    # Convert the node
    converted_node = convert_node_to_transition_node(sample_node, [])
    
    # Extract and display the output schema with semantic types
    output_schema = converted_node["server_tools"][0]["output_schema"]
    predefined_fields = output_schema["predefined_fields"]
    
    print("  Original Schema → Enhanced Schema with Semantic Types:")
    print("  " + "-" * 48)
    
    for field in predefined_fields:
        field_name = field["field_name"]
        data_type = field["data_type"]["type"]
        semantic_type = field["data_type"]["format"]
        description = field["data_type"]["description"]
        
        print(f"  {field_name:15} ({data_type:6}) → format: '{semantic_type}'")
        print(f"  {'':17} Description: {description}")
        print()
    
    # Demo 4: Show the complete enhanced schema
    print("4. Complete Enhanced Output Schema:")
    print("-" * 50)
    
    print("Enhanced output schema for orchestration engine:")
    print(json.dumps(output_schema, indent=2))
    
    print("\n✅ Semantic Type Extraction Complete!")
    print("The orchestration engine can now use these semantic types")
    print("for enhanced result formatting and frontend display.")


if __name__ == "__main__":
    demo_semantic_type_extraction()
