"use client";

import { <PERSON><PERSON>heck, AlertCircleIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { PasswordValidationIndicatorProps } from "@/app/shared/interfaces";
import { validatePassword } from "@/lib/utils/password-validation";

export function PasswordValidationIndicator({
  password,
  showValidation = false,
}: PasswordValidationIndicatorProps) {
  const [validations, setValidations] = useState({
    length: false,
    hasNumber: false,
    hasSymbol: false,
    hasUppercase: false,
  });

  useEffect(() => {
    // Use the utility function to validate the password
    setValidations(validatePassword(password));
  }, [password]);

  return (
    <div className="flex flex-col gap-4 py-4 text-sm">
      <h3 className="font-medium mb-3">Your password must have:</h3>
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          {validations.length ? (
            <CircleCheck className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircleIcon className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-brand-secondary-font">6-15 characters</span>
        </div>
        <div className="flex items-center gap-2">
          {validations.hasNumber ? (
            <CircleCheck className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircleIcon className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-brand-secondary-font">
            At least one number (0-9)
          </span>
        </div>
        <div className="flex items-center gap-2">
          {validations.hasSymbol ? (
            <CircleCheck className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircleIcon className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-brand-secondary-font">
            At least one symbol (@, #, $, %, etc.)
          </span>
        </div>
        <div className="flex items-center gap-2">
          {validations.hasUppercase ? (
            <CircleCheck className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircleIcon className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-brand-secondary-font">
            At least one uppercase letter
          </span>
        </div>
      </div>
    </div>
  );
}
