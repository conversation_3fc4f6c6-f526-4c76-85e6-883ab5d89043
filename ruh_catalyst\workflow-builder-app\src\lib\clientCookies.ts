/**
 * Client-side cookie utilities
 * These functions can be used in client components to get/set cookies in the browser
 */

import Cookies from "js-cookie";

// Get access token from client-side cookie
export const getClientAccessToken = (): string => {
  return Cookies.get("accessToken") || "";
};

// Get refresh token from client-side cookie
// Note: This will always return null now that refresh token is HTTP-only
export const getClientRefreshToken = (): string | null => {
  // The refresh token is now HTTP-only and not accessible from client-side JavaScript
  // This function is kept for backward compatibility but will always return null
  return null;
};

// Check if access token exists in client-side cookies
export const checkClientAccessToken = (): boolean => {
  const token = Cookies.get("accessToken");
  console.log("Client-side access token check:", !!token);
  return !!token;
};

// Set auth cookies on the client side
export const setClientAuthCookies = (
  accessToken: string,
  refreshToken: string | null,
  accessTokenAge: number,
  refreshTokenAge: number | null,
): void => {
  // Set access token cookie
  // Note: The refresh token is handled by the server-side HTTP-only cookie
  // and should not be set on the client side
  Cookies.set("accessToken", accessToken, {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    secure: true,
    sameSite: "lax", // Changed from "none" to "lax" for better CSRF protection
    expires: accessTokenAge / (60 * 60 * 24), // Convert seconds to days
  });

  // We no longer set the refresh token on the client side
  // as it's now handled by an HTTP-only cookie set by the server
  // This improves security by preventing client-side JavaScript from accessing the refresh token
};

// Clear auth cookies on the client side
export const clearClientAuthCookies = (): void => {
  // Clear access token from client-side cookie
  Cookies.remove("accessToken", {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
  });

  // Attempt to clear refresh token with both possible paths
  // Even though it's HTTP-only, we try to clear it from client-side as a fallback
  Cookies.remove("refreshToken", {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
  });

  Cookies.remove("refreshToken", {
    path: "/api/auth/refresh",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
  });

  console.log("Client-side cookie clearing attempted for both tokens");
};
