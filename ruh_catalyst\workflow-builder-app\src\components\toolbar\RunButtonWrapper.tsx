import React from "react";
import { ReactFlowProvider } from "reactflow";
import { RunButton } from "./RunButton";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

interface RunButtonWrapperProps {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  disabled?: boolean;
  onRun?: () => void;
  workflowId?: string;
}

/**
 * Wrapper component that provides ReactFlowProvider context for RunButton
 * This ensures that useReactFlow hook in useWorkflowValidation works correctly
 */
export const RunButtonWrapper = React.memo(function RunButtonWrapper({
  nodes,
  edges,
  disabled,
  onRun,
  workflowId,
}: RunButtonWrapperProps) {
  return (
    <ReactFlowProvider>
      <RunButton
        nodes={nodes}
        edges={edges}
        disabled={disabled}
        onRun={onRun}
        workflowId={workflowId}
      />
    </ReactFlowProvider>
  );
});
