#!/usr/bin/env python3
"""
Test script to verify MCP connection with SSH host key verification.

This script simulates the actual MCP connection flow that was failing
with "Host key verification failed" error.
"""

import asyncio
import logging
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_mcp_connection_with_host_verification():
    """Test MCP connection with the new host key verification."""
    
    print("🔧 Testing MCP Connection with SSH Host Key Verification")
    print("=" * 60)
    
    try:
        from app.core_.client import MCPClient
        from app.schemas.client import ConnectionConfig
        from app.services.authentication_manager import AuthenticationManager
        from app.config.config import settings
        
        # Test configuration (similar to the failing case)
        ssh_host = getattr(settings, 'ssh_host', '************')
        ssh_user = getattr(settings, 'ssh_user', 'ubuntu')
        ssh_key_content = getattr(settings, 'ssh_key_content', None)
        
        if not ssh_key_content:
            print("❌ No SSH key content found in settings")
            return False
        
        print(f"📡 SSH Host: {ssh_host}")
        print(f"👤 SSH User: {ssh_user}")
        print(f"🔑 SSH Key: {'Present' if ssh_key_content else 'Missing'}")
        
        # Initialize global SSH key (like the service does on startup)
        print("\n🔑 Initializing global SSH key...")
        from app.services.ssh_manager import initialize_global_ssh_key
        initialize_global_ssh_key(ssh_key_content)
        print("✅ Global SSH key initialized")
        
        # Create connection config
        connection_config = ConnectionConfig(
            timeout=30,
            max_retries=2,
            retry_delay=2,
            health_check_interval=60,
            sse_read_timeout=30
        )
        
        # Create authentication manager
        auth_manager = AuthenticationManager()
        
        # Create MCP client with SSH Docker connection
        print("\n🔌 Creating MCP client...")
        client = MCPClient(
            server_url="",  # Not used for SSH connections
            connection_type="ssh_docker",
            connection_config=connection_config,
            auth_manager=auth_manager,
            ssh_host=ssh_host,
            ssh_user=ssh_user,
            ssh_port=22,
            ssh_key_content=ssh_key_content,
            docker_image="test-container",  # This would be the actual container name
            container_command="python server.py",  # This would be detected dynamically
            container_name="test-container"
        )
        
        print("✅ MCP client created")
        
        # Test connection establishment (this is where the error was occurring)
        print("\n🌐 Testing connection establishment...")
        
        try:
            async with client:
                print("✅ MCP connection established successfully!")
                
                # Try to list tools (if the container exists and is running)
                try:
                    print("\n🔧 Testing tool listing...")
                    tools = await client.list_tools()
                    print(f"✅ Found {len(tools)} tools")
                    for tool in tools[:3]:  # Show first 3 tools
                        print(f"  - {tool.name}: {tool.description}")
                except Exception as e:
                    print(f"⚠️ Tool listing failed (expected if container doesn't exist): {e}")
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            
            # Analyze the error
            error_str = str(e)
            if "Host key verification failed" in error_str:
                print("💡 Host key verification failed - the fix may need adjustment")
                return False
            elif "Permission denied" in error_str:
                print("💡 SSH authentication failed - check SSH key and server config")
                return False
            elif "Connection refused" in error_str:
                print("💡 SSH connection refused - check if SSH server is accessible")
                return False
            elif "Container" in error_str or "docker" in error_str.lower():
                print("💡 Container-related error - this is expected if test container doesn't exist")
                print("✅ SSH connection part worked (container error is expected)")
                return True
            else:
                print(f"💡 Unexpected error: {error_str}")
                return False
        
        print("\n🎉 MCP connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        logger.exception("Test failed")
        return False


async def test_ssh_command_building():
    """Test SSH command building with host verification."""
    
    print("\n🔧 Testing SSH Command Building")
    print("=" * 40)
    
    try:
        from app.core_.client import MCPClient
        from app.schemas.client import ConnectionConfig
        from app.services.authentication_manager import AuthenticationManager
        from app.config.config import settings
        
        ssh_host = getattr(settings, 'ssh_host', '************')
        ssh_user = getattr(settings, 'ssh_user', 'ubuntu')
        ssh_key_content = getattr(settings, 'ssh_key_content', None)
        
        if not ssh_key_content:
            print("❌ No SSH key content found in settings")
            return False
        
        # Initialize global SSH key
        from app.services.ssh_manager import initialize_global_ssh_key
        initialize_global_ssh_key(ssh_key_content)
        
        # Create MCP client
        connection_config = ConnectionConfig()
        auth_manager = AuthenticationManager()
        
        client = MCPClient(
            server_url="",
            connection_type="ssh_docker",
            connection_config=connection_config,
            auth_manager=auth_manager,
            ssh_host=ssh_host,
            ssh_user=ssh_user,
            ssh_port=22,
            ssh_key_content=ssh_key_content,
            docker_image="test-container",
            container_command="python server.py",
            container_name="test-container"
        )
        
        # Test SSH command building
        print("🔧 Building SSH command...")
        ssh_command = client._build_simple_ssh_command()
        
        print("✅ SSH command built successfully:")
        print(f"   {' '.join(ssh_command)}")
        
        # Verify the command includes proper SSH options
        expected_options = ["-i", "-o", "StrictHostKeyChecking=no", "-o", "UserKnownHostsFile=/dev/null"]
        for option in expected_options:
            if option in ssh_command:
                print(f"✅ Found expected option: {option}")
            else:
                print(f"❌ Missing expected option: {option}")
        
        return True
        
    except Exception as e:
        print(f"❌ SSH command building test failed: {e}")
        logger.exception("SSH command building test failed")
        return False


if __name__ == "__main__":
    async def main():
        print("🚀 Starting MCP Connection Tests with Host Key Verification")
        print("=" * 70)
        
        # Test 1: SSH command building
        test1_result = await test_ssh_command_building()
        
        # Test 2: MCP connection with host verification
        test2_result = await test_mcp_connection_with_host_verification()
        
        print("\n📊 Test Results Summary")
        print("=" * 30)
        print(f"✅ SSH Command Building: {'PASS' if test1_result else 'FAIL'}")
        print(f"✅ MCP Connection Test: {'PASS' if test2_result else 'FAIL'}")
        
        if test1_result and test2_result:
            print("\n🎉 All tests passed! The SSH host key verification fix is working.")
            print("💡 The original 'Host key verification failed' error should be resolved.")
            return True
        else:
            print("\n❌ Some tests failed. Please check the errors above.")
            return False
    
    # Run the tests
    success = asyncio.run(main())
    exit(0 if success else 1)
