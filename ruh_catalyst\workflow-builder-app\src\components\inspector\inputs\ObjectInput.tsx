import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Textarea } from "@/components/ui/textarea";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { ObjectInputEditor } from "@/components/ui/object-input-editor";
import { JsonObjectInput } from "./JsonObjectInput";
import { cn, debounce } from "@/lib/utils";
import { formatValueForDisplay } from "@/utils/valueFormatting";

interface ObjectInputProps {
  inputDef: InputDefinition;
  value: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering object inputs
 */
export function ObjectInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: ObjectInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;

  // Check if this is a JSON object input from an MCP component
  const isMcpJsonObject =
    inputDef.input_type === "json" ||
    (inputDef.name === "keywords") ||
    (inputDef.mcp_info && inputDef.mcp_info.input_schema);

  // Use JsonObjectInput for JSON object inputs
  if (isMcpJsonObject) {
    return (
      <JsonObjectInput
        inputDef={inputDef}
        value={value}
        onChange={onChange}
        isDisabled={isDisabled}
        isConnected={isConnected}
        nodeId={nodeId}
      />
    );
  }

  // Use ObjectInputEditor if the input has properties defined
  if (inputDef.properties && Object.keys(inputDef.properties).length > 0) {
    return (
      <ObjectInputEditor
        inputDef={inputDef}
        currentValue={value}
        onChange={onChange}
        isDisabled={isDisabled}
        isConnected={isConnected}
      />
    );
  }

  // Fallback to JSON editor
  const [jsonText, setJsonText] = React.useState(() => formatValueForDisplay(value, "object"));

  // Debounced function to parse JSON and update the value
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedParseJson = React.useCallback(
    debounce((text: string) => {
      try {
        const parsed = JSON.parse(text);
        onChange(inputDef.name, parsed);

        // Immediately update window.currentWorkflowNodes if available
        if (typeof window !== 'undefined' && window.currentWorkflowNodes && nodeId) {
          const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === nodeId);
          if (nodeIndex !== -1) {
            // Create a deep copy to avoid reference issues
            const updatedNodes = [...window.currentWorkflowNodes];
            if (!updatedNodes[nodeIndex].data.config) {
              updatedNodes[nodeIndex].data.config = {};
            }
            updatedNodes[nodeIndex].data.config[inputDef.name] = parsed;
            window.currentWorkflowNodes = updatedNodes;

            if (process.env.NODE_ENV === 'development') {
              console.log(`Updated window.currentWorkflowNodes with new JSON value for node ${nodeId}, input ${inputDef.name}`);
            }
          }
        }
      } catch {
        // If parsing fails, just keep the text value but don't update the object
        // This prevents validation errors during typing
      }
    }, 50), // Reduced from 500ms to 50ms for immediate updates
    [onChange, inputDef.name]
  );

  // Update jsonText when value changes from outside
  React.useEffect(() => {
    const formatted = formatValueForDisplay(value, "object");
    if (formatted !== jsonText) {
      setJsonText(formatted);
    }
  }, [value]);

  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="relative">
        <Textarea
          id={inputId}
          value={jsonText}
          onChange={(e) => {
            const newText = e.target.value;
            setJsonText(newText);
            debouncedParseJson(newText);
          }}
          placeholder={`Enter ${inputDef.display_name} (JSON format)`}
          className={cn(
            "bg-background/50 mt-1 font-mono text-xs",
            isDisabled && "opacity-50"
          )}
          rows={5}
          disabled={isDisabled}
        />
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
