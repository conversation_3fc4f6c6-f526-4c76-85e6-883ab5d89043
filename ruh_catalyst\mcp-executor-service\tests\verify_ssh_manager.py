#!/usr/bin/env python3
"""
Verify SSH key manager functionality.
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_ssh_key_file():
    """Test the existing SSH key file."""
    print("🧪 Testing existing SSH key file...")
    
    ssh_key_path = "mcp_ssh_key.pem"
    
    if not os.path.exists(ssh_key_path):
        print("❌ SSH key file not found")
        return False
    
    print(f"✅ SSH key file exists: {ssh_key_path}")
    
    # Check file size
    file_size = os.path.getsize(ssh_key_path)
    print(f"File size: {file_size} bytes")
    
    # Check file content
    with open(ssh_key_path, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"Number of lines: {len(lines)}")
    
    # Check formatting
    if lines[0].startswith('-----BEGIN'):
        print("✅ SSH key starts with proper header")
    else:
        print("❌ SSH key header not found")
        return False
    
    if lines[-1].startswith('-----END') or (len(lines) > 1 and lines[-2].startswith('-----END')):
        print("✅ SSH key ends with proper footer")
    else:
        print("❌ SSH key footer not found")
        return False
    
    # Check line lengths
    key_content_lines = [line for line in lines if not line.startswith('-----') and line.strip()]
    if key_content_lines:
        max_line_length = max(len(line) for line in key_content_lines)
        print(f"Maximum line length in key content: {max_line_length}")
        if max_line_length <= 64:
            print("✅ SSH key lines are properly formatted")
        else:
            print("❌ SSH key lines are too long")
    
    print("\nFirst 3 lines:")
    for i, line in enumerate(lines[:3]):
        print(f"  {i+1}: {line}")
    
    print("\nLast 3 lines:")
    for i, line in enumerate(lines[-3:]):
        print(f"  {len(lines)-2+i}: {line}")
    
    return True


def test_ssh_command_construction():
    """Test SSH command construction."""
    print("\n🧪 Testing SSH command construction...")
    
    try:
        from app.core_.client import MCPClient
        from app.config.config import settings
        
        print(f"SSH Host: {settings.ssh_host}")
        print(f"SSH User: {settings.ssh_user}")
        print(f"SSH Port: {settings.ssh_port}")
        
        # Create MCP client
        client = MCPClient(
            connection_type="ssh_docker",
            docker_image="test-container",
            container_command="echo 'test'"
        )
        
        # Build SSH command
        ssh_command = client._build_ssh_command()
        print(f"\nGenerated SSH command:")
        print(f"  {' '.join(ssh_command)}")
        
        # Check if SSH key is in the command
        ssh_key_found = False
        for i, arg in enumerate(ssh_command):
            if arg == "-i" and i + 1 < len(ssh_command):
                key_path = ssh_command[i + 1]
                print(f"\nSSH key path in command: {key_path}")
                if os.path.exists(key_path):
                    print("✅ SSH key file exists and is accessible")
                    ssh_key_found = True
                else:
                    print("❌ SSH key file not found")
                break
        
        if not ssh_key_found:
            print("❌ SSH key not found in command")
            return False
        
        print("✅ SSH command construction test passed")
        return True
        
    except Exception as e:
        print(f"❌ SSH command construction failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Verifying SSH Manager Functionality...")
    
    test1 = test_ssh_key_file()
    test2 = test_ssh_command_construction()
    
    if test1 and test2:
        print("\n🎉 All SSH manager tests passed!")
        return True
    else:
        print("\n💥 Some SSH manager tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
