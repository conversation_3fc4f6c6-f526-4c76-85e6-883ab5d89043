import logging
from typing import Dict, Any, Optional

import openai
from openai import OpenAI

from app.core.config import settings

logger = logging.getLogger(__name__)


class OpenAIService:
    """Service for interacting with OpenAI API."""

    def __init__(self):
        """Initialize the OpenAI service with API key from settings."""
        self.api_key = settings.OPENAI_API_KEY
        self.model = settings.OPENAI_MODEL
        self.client = OpenAI(api_key=self.api_key)

    def improve_system_prompt(
        self, original_prompt: str, agent_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Improve an agent's system prompt using OpenAI.

        Args:
            original_prompt: The original system prompt to improve
            agent_context: Optional context about the agent (capabilities, purpose, etc.)

        Returns:
            str: The improved system prompt
        """
        try:
            if not self.api_key:
                logger.error("OpenAI API key is not set")
                return original_prompt

            # Prepare context information
            context_str = ""
            if agent_context:
                context_str = "Agent context:\n"
                for key, value in agent_context.items():
                    context_str += f"- {key}: {value}\n"

            # Create the prompt for OpenAI
            prompt_improvement_prompt = f"""
            You are an expert AI prompt engineer. Your task is to improve the following system prompt for an AI agent.
            
            {context_str}
            
            ORIGINAL PROMPT:
            ```
            {original_prompt}
            ```
            
            Please improve this prompt to make it:
            1. More clear and specific
            2. Better at guiding the agent to perform its intended tasks
            3. Include appropriate constraints and guidelines
            4. Follow best practices for AI system prompts
            
            Return ONLY the improved prompt text without any explanations or additional text.
            """

            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert AI prompt engineer specializing in creating effective system prompts for AI agents.",
                    },
                    {"role": "user", "content": prompt_improvement_prompt},
                ],
                temperature=0.7,
                max_tokens=4000,
            )

            # Extract and return the improved prompt
            improved_prompt = response.choices[0].message.content.strip()
            return improved_prompt

        except Exception as e:
            logger.error(f"Error improving system prompt with OpenAI: {str(e)}")
            return original_prompt  # Return original prompt if there's an error
