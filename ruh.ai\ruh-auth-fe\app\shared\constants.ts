import { GoogleIcon } from "@/components/customIcons/GoogleIcons";
import { CarouselSlide } from "./interfaces";
import { authApi } from "../api/auth";

export const fullLogoPath = "/assets/logos/ruh-full-logo.svg";
export const noTextLogoPath = "/assets/logos/ruh-logo-no-text.svg";

// carousel images for auth carousel component
export const carouselSlides: CarouselSlide[] = [
  {
    image: "/assets/carousel/carousel-1.svg",
    title: "Digitalize Your Workforce with AI Employees You Can Build",
  },
  {
    image: "/assets/carousel/carousel-2.svg",
    title: "Automate Your Everyday Work with Intelligent AI-Powered Workflows",
  },
  {
    image: "/assets/carousel/carousel-3.svg",
    title: "Connect Your AI Employees Across Multiple Communication Channels",
  },
];

// Social login providers for authentication
export const socialProviders = [
  {
    name: "Google",
    icon: GoogleIcon,
    onClick: () => authApi.googleLogin(),
  },
  // TODO: Currently Commenting out the social login providers as we don't have them implemented yet
  // {
  //   name: "Windows",
  //   icon: WindowsIcon,
  //   onClick: () => console.log("Windows login"),
  // },
  // {
  //   name: "GitHub",
  //   icon: GithubIcon,
  //   onClick: () => console.log("GitHub login"),
  // },
  // {
  //   name: "LinkedIn",
  //   icon: LinkedInIcon,
  //   onClick: () => console.log("Linkedin login"),
  // },
];

// Onboarding departments and their respective roles
export const onboardingDepartmentsAndRoles = [
  {
    departments: [
      {
        name: "Marketing",
        roles: [
          "Marketing Manager",
          "Content Strategist",
          "SEO Specialist",
          "Social Media Manager",
          "Email Marketing Specialist",
          "Brand Manager",
          "Other",
        ],
      },
      {
        name: "Sales",
        roles: [
          "Sales Executive",
          "SDR (Sales Development Rep)",
          "Account Manager",
          "Business Development Manager",
          "Revenue Operations Analyst",
          "Other",
        ],
      },
      {
        name: "Customer Support / Success",
        roles: [
          "Customer Success Manager",
          "Support Agent",
          "Client Relationship Manager",
          "Customer Experience Lead",
          "Helpdesk Manager",
          "Other",
        ],
      },
      {
        name: "Human Resources (HR)",
        roles: [
          "HR Manager",
          "Recruiter / Talent Acquisition",
          "People Operations Specialist",
          "Learning & Development Lead",
          "HR Business Partner",
          "Other",
        ],
      },
      {
        name: "Finance & Accounting",
        roles: [
          "Financial Analyst",
          "Accountant",
          "FP&A Lead",
          "Controller",
          "Bookkeeper",
          "Other",
        ],
      },
      {
        name: "Operations",
        roles: [
          "Operations Manager",
          "Supply Chain Analyst",
          "Procurement Specialist",
          "Office Manager",
          "Ops Coordinator",
          "Other",
        ],
      },
      {
        name: "Product Management / R&D",
        roles: [
          "Product Manager",
          "UX Researcher",
          "Technical Product Owner",
          "R&D Analyst",
          "Product Designer",
          "Other",
        ],
      },
      {
        name: "Leadership / Strategy",
        roles: [
          "CEO / Founder",
          "COO",
          "Strategy Lead",
          "Chief of Staff",
          "Executive Assistant",
          "Other",
        ],
      },
      {
        name: "Other",
        roles: ["Other"],
      },
    ],
  },
];
