#!/usr/bin/env python3
"""
Simple script to send MCP task to Kaf<PERSON>.
"""

import asyncio
import json
from aiokafka import AIOKafkaProducer

# Ka<PERSON>ka config
KAFKA_SERVERS = ["**************:9092"]
TOPIC = "mcp-execution-request"

# Task payload
payload = {
    "user_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "mcp_id": "0dc83245-794f-405d-8814-7771260d3c60",
    "retries": 3,
    "tool_name": "research",
    "tool_parameters": {"topic": "Wildlife"},
}


async def send_task():
    # Send to Kafka
    producer = AIOKafkaProducer(
        bootstrap_servers=KAFKA_SERVERS,
        value_serializer=lambda v: json.dumps(v).encode("utf-8"),
    )

    await producer.start()
    await producer.send(TOPIC, payload)
    await producer.stop()

    print("✅ Task sent to Kafka")
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")


if __name__ == "__main__":
    asyncio.run(send_task())
