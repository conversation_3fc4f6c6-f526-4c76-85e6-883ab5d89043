import React, { useState, useEffect, useRef } from "react";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import Image from "next/image";
import { ClientRunButtonWrapper } from "@/components/toolbar/ClientRunButtonWrapper";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { format } from "date-fns";
import {
  Save,
  Trash2,
  ChevronsUpDown,
  CheckCircle2,
  FileDown,
  FileUp,
  HelpCircle,
  Moon,
  Sun,
  Key,
} from "lucide-react";
import { UserProfileButton } from "@/components/auth/UserProfileButton";

interface TopBarProps {
  onSave: () => void;
  onRun: () => void;
  workflowTitle?: string;
  onTitleChange?: (title: string) => void;
  onValidate?: () => void;
  onExport?: () => void;
  onImport?: () => void;
  onDelete?: () => void;
  onLoad?: () => void;
  isDarkMode?: boolean;
  onToggleTheme?: () => void;
  className?: string;
  nodes?: Node<WorkflowNodeData>[];
  edges?: Edge[];
  workflowId?: string;
}

export const TopBar = React.memo(function TopBar({
  onSave,
  onRun,
  workflowTitle = "Untitled Workflow",
  onTitleChange,
  onValidate,
  onExport,
  onImport,
  onDelete,
  onLoad,
  isDarkMode,
  onToggleTheme,
  className,
  nodes = [],
  edges = [],
  workflowId,
}: TopBarProps) {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [title, setTitle] = useState(workflowTitle);
  const [isSaved, setIsSaved] = useState(false);
  const [lastSavedTime, setLastSavedTime] = useState<Date | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local title state when workflowTitle prop changes
  useEffect(() => {
    setTitle(workflowTitle);
  }, [workflowTitle]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  const handleTitleClick = () => {
    if (onTitleChange) {
      setIsEditingTitle(true);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const handleTitleBlur = () => {
    setIsEditingTitle(false);
    if (onTitleChange && title.trim()) {
      onTitleChange(title);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleTitleBlur();
    }
  };

  // Handle save with visual feedback
  const handleSave = () => {
    // Call the original onSave function
    onSave();

    // Update saved state and time
    setIsSaved(true);
    setLastSavedTime(new Date());

    // Reset to "Save" after 2 seconds
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    saveTimeoutRef.current = setTimeout(() => {
      setIsSaved(false);
    }, 2000);
  };

  return (
    <div
      className={`bg-card/30 flex h-16 shrink-0 items-center justify-between border-b px-4 py-2 shadow-sm backdrop-blur-sm ${className || ""}`}
    >
      {/* Left side: Logo, Title and primary actions */}
      <div className="flex items-center gap-3">
        <Link href="/workflows" className="mr-2 flex items-center">
          <Image
            src={!isDarkMode ? "/wflogo.svg" : "/wflogo_white.svg"}
            alt="Workflow Builder Logo"
            width={120}
            height={30}
            className="h-8 w-auto"
          />
        </Link>

        {isEditingTitle ? (
          <Input
            value={title}
            onChange={handleTitleChange}
            onBlur={handleTitleBlur}
            onKeyDown={handleTitleKeyDown}
            autoFocus
            className="bg-background/50 h-9 w-64 text-lg font-semibold"
          />
        ) : (
          <h1
            className="hover:text-primary cursor-pointer text-lg font-semibold transition-colors"
            onClick={handleTitleClick}
            title={onTitleChange ? "Click to edit workflow title" : ""}
          >
            {workflowTitle}
          </h1>
        )}

        <Separator orientation="vertical" className="h-8" />
      </div>

      {/* Right side: Secondary actions */}
      <div className="flex items-center gap-2">
        {onExport && (
          <button
            type="button"
            className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-xs"
            onClick={onExport}
            title="Export workflow"
          >
            <FileDown className="h-4 w-4" />
          </button>
        )}

        {onImport && (
          <button
            type="button"
            className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-xs"
            onClick={onImport}
            title="Import workflow"
          >
            <FileUp className="h-4 w-4" />
          </button>
        )}

        {onDelete && (
          <button
            type="button"
            className="bg-background hover:bg-accent text-destructive hover:bg-destructive/10 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-xs"
            onClick={onDelete || (() => console.log("Delete clicked"))}
            title="Delete workflow"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        )}

        <Separator orientation="vertical" className="mx-1 h-8" />

        <Link
          href="/credentials"
          className="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium"
          title="Manage Credentials"
        >
          <Key className="h-4 w-4" />
        </Link>

        {onToggleTheme && (
          <button
            type="button"
            className="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium"
            onClick={onToggleTheme}
            title="Toggle theme"
          >
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </button>
        )}

        {/* <button
          type="button"
          className="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium"
          title="Help"
        >
          <HelpCircle className="h-4 w-4" />
        </button> */}

        {/* <Separator orientation="vertical" className="mx-1 h-8" /> */}

        {/* {onValidate && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  type="button"
                  className="inline-flex h-8 items-center justify-center gap-1.5 rounded-md border border-yellow-500/30 bg-yellow-500/10 px-3 py-2 text-sm font-medium text-yellow-700 hover:bg-yellow-500/20 dark:text-yellow-400"
                  onClick={onValidate || (() => console.log("Validate clicked"))}
                >
                  <CheckCircle2 className="h-4 w-4" /> Validate Workflow
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  Validate the entire workflow.
                  <br />
                  Fields are not validated during editing.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )} */}

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                type="button"
                className="bg-secondary text-secondary-foreground hover:bg-secondary/80 inline-flex h-8 items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium shadow-xs"
                onClick={handleSave}
              >
                <Save className="h-4 w-4" /> {isSaved ? "Saved" : "Save"}
              </button>
            </TooltipTrigger>
            <TooltipContent>
              {lastSavedTime && <>Last saved at {format(lastSavedTime, "h:mm:ss a")}</>}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <button
          type="button"
          className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center gap-1.5 rounded-md border px-3 py-2 text-sm font-medium shadow-xs"
          onClick={onLoad || (() => console.log("Load clicked"))}
          title="Load existing workflow"
        >
          Load
        </button>
        {/* Use our ClientRunButtonWrapper component */}
        <ClientRunButtonWrapper
          nodes={nodes}
          edges={edges}
          disabled={nodes.length === 0}
          onRun={onRun}
          workflowId={workflowId}
        />

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* User profile button */}
        <UserProfileButton />
      </div>
    </div>
  );
});
