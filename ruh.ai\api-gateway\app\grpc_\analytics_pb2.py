# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: analytics.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'analytics.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0f\x61nalytics.proto\x12\tanalytics\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xa1\x01\n\x11TrackEventRequest\x12(\n\nevent_type\x18\x01 \x01(\x0e\x32\x14.analytics.EventType\x12,\n\x0cservice_type\x18\x02 \x01(\x0e\x32\x16.analytics.ServiceType\x12\x11\n\tentity_id\x18\x03 \x01(\t\x12\x0f\n\x07user_id\x18\x04 \x01(\t\x12\x10\n\x08metadata\x18\x05 \x01(\t\"H\n\x12TrackEventResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08\x65vent_id\x18\x03 \x01(\t\"u\n\x18GetServiceMetricsRequest\x12,\n\x0cservice_type\x18\x01 \x01(\x0e\x32\x16.analytics.ServiceType\x12\x11\n\tentity_id\x18\x02 \x01(\t\x12\x18\n\x10time_period_days\x18\x03 \x01(\x05\"i\n\x19GetServiceMetricsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x07metrics\x18\x03 \x01(\x0b\x32\x19.analytics.ServiceMetrics\"\xcf\x01\n\x0eServiceMetrics\x12\x11\n\tentity_id\x18\x01 \x01(\t\x12,\n\x0cservice_type\x18\x02 \x01(\x0e\x32\x16.analytics.ServiceType\x12\x13\n\x0busage_count\x18\x03 \x01(\x05\x12\x16\n\x0e\x61verage_rating\x18\x04 \x01(\x02\x12\x14\n\x0crating_count\x18\x05 \x01(\x05\x12\x39\n\x11usage_time_series\x18\x06 \x03(\x0b\x32\x1e.analytics.TimeSeriesDataPoint\"2\n\x13TimeSeriesDataPoint\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\t\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\"C\n\x16GetUserActivityRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x18\n\x10time_period_days\x18\x02 \x01(\x05\"f\n\x17GetUserActivityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x08\x61\x63tivity\x18\x03 \x01(\x0b\x32\x17.analytics.UserActivity\"\xe8\x01\n\x0cUserActivity\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x17\n\x0fmcp_usage_count\x18\x02 \x01(\x05\x12\x1c\n\x14workflow_usage_count\x18\x03 \x01(\x05\x12\x19\n\x11\x61gent_usage_count\x18\x04 \x01(\x05\x12\x1a\n\x12mcp_creation_count\x18\x05 \x01(\x05\x12\x1f\n\x17workflow_creation_count\x18\x06 \x01(\x05\x12\x1c\n\x14\x61gent_creation_count\x18\x07 \x01(\x05\x12\x1a\n\x12last_activity_date\x18\x08 \x01(\t\"v\n\x19GetRatingAnalyticsRequest\x12,\n\x0cservice_type\x18\x01 \x01(\x0e\x32\x16.analytics.ServiceType\x12\x11\n\tentity_id\x18\x02 \x01(\t\x12\x18\n\x10time_period_days\x18\x03 \x01(\x05\"m\n\x1aGetRatingAnalyticsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12-\n\tanalytics\x18\x03 \x01(\x0b\x32\x1a.analytics.RatingAnalytics\"\x8c\x02\n\x0fRatingAnalytics\x12,\n\x0cservice_type\x18\x01 \x01(\x0e\x32\x16.analytics.ServiceType\x12\x11\n\tentity_id\x18\x02 \x01(\t\x12\x16\n\x0e\x61verage_rating\x18\x03 \x01(\x02\x12\x14\n\x0crating_count\x18\x04 \x01(\x05\x12O\n\x13rating_distribution\x18\x05 \x03(\x0b\x32\x32.analytics.RatingAnalytics.RatingDistributionEntry\x1a\x39\n\x17RatingDistributionEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"`\n\x1bGetOverviewAnalyticsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x18\n\x10time_period_days\x18\x02 \x01(\x05\x12\x16\n\x0e\x61pplication_id\x18\x03 \x01(\t\"q\n\x1cGetOverviewAnalyticsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12/\n\tanalytics\x18\x03 \x01(\x0b\x32\x1c.analytics.OverviewAnalytics\"\x8a\x03\n\x11OverviewAnalytics\x12,\n\x0c\x63redit_usage\x18\x01 \x01(\x0b\x32\x16.analytics.CreditUsage\x12\x1b\n\x13\x61\x63tive_agents_count\x18\x02 \x01(\x05\x12\x1e\n\x16\x61\x63tive_workflows_count\x18\x03 \x01(\x05\x12\x19\n\x11\x61\x63tive_mcps_count\x18\x04 \x01(\x05\x12\x1a\n\x12\x61pplications_count\x18\x05 \x01(\x05\x12\x32\n\x0frequest_metrics\x18\x06 \x01(\x0b\x32\x19.analytics.RequestMetrics\x12\x34\n\x11recent_activities\x18\x07 \x03(\x0b\x32\x19.analytics.RecentActivity\x12\x33\n\x0busage_trend\x18\x08 \x03(\x0b\x32\x1e.analytics.TimeSeriesDataPoint\x12\x34\n\x0c\x63redit_trend\x18\t \x03(\x0b\x32\x1e.analytics.TimeSeriesDataPoint\"\x8d\x01\n\x0b\x43reditUsage\x12\x1a\n\x12total_credits_used\x18\x01 \x01(\x02\x12\x19\n\x11\x63redits_remaining\x18\x02 \x01(\x02\x12\x15\n\rcredits_limit\x18\x03 \x01(\x02\x12\x15\n\rdaily_average\x18\x04 \x01(\x02\x12\x19\n\x11projected_monthly\x18\x05 \x01(\x02\"\xb4\x01\n\x0eRequestMetrics\x12\x16\n\x0etotal_requests\x18\x01 \x01(\x05\x12\x1b\n\x13successful_requests\x18\x02 \x01(\x05\x12\x17\n\x0f\x66\x61iled_requests\x18\x03 \x01(\x05\x12\x1d\n\x15\x61verage_response_time\x18\x04 \x01(\x02\x12\x35\n\rrequest_trend\x18\x05 \x03(\x0b\x32\x1e.analytics.TimeSeriesDataPoint\"\x99\x01\n\x0eRecentActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\t\x12\x15\n\ractivity_type\x18\x02 \x01(\t\x12\x11\n\tentity_id\x18\x03 \x01(\t\x12\x13\n\x0b\x65ntity_name\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x11\n\ttimestamp\x18\x06 \x01(\t\x12\x10\n\x08metadata\x18\x07 \x01(\t\"\x89\x01\n\x18\x43reateApplicationRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x14\n\x0cworkflow_ids\x18\x04 \x03(\t\x12\x11\n\tagent_ids\x18\x05 \x03(\t\x12\x10\n\x08\x61pi_keys\x18\x06 \x03(\t\"j\n\x19\x43reateApplicationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x0b\x61pplication\x18\x03 \x01(\x0b\x32\x16.analytics.Application\"v\n\x16GetApplicationsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12,\n\x06status\x18\x02 \x01(\x0e\x32\x1c.analytics.ApplicationStatus\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\"~\n\x17GetApplicationsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x0c\x61pplications\x18\x03 \x03(\x0b\x32\x16.analytics.Application\x12\x13\n\x0btotal_count\x18\x04 \x01(\x05\"@\n\x15GetApplicationRequest\x12\x16\n\x0e\x61pplication_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"\x97\x01\n\x16GetApplicationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x0b\x61pplication\x18\x03 \x01(\x0b\x32\x16.analytics.Application\x12.\n\x07metrics\x18\x04 \x01(\x0b\x32\x1d.analytics.ApplicationMetrics\"\xcf\x01\n\x18UpdateApplicationRequest\x12\x16\n\x0e\x61pplication_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x14\n\x0cworkflow_ids\x18\x05 \x03(\t\x12\x11\n\tagent_ids\x18\x06 \x03(\t\x12,\n\x06status\x18\x07 \x01(\x0e\x32\x1c.analytics.ApplicationStatus\x12\x10\n\x08\x61pi_keys\x18\x08 \x03(\t\"j\n\x19UpdateApplicationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x0b\x61pplication\x18\x03 \x01(\x0b\x32\x16.analytics.Application\"C\n\x18\x44\x65leteApplicationRequest\x12\x16\n\x0e\x61pplication_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"=\n\x19\x44\x65leteApplicationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x9b\x01\n\x1f\x41ttachImageToApplicationRequest\x12\x16\n\x0e\x61pplication_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x12\n\nimage_name\x18\x03 \x01(\t\x12\x12\n\nimage_type\x18\x04 \x01(\t\x12\x12\n\nimage_data\x18\x05 \x01(\x0c\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\"i\n AttachImageToApplicationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08image_id\x18\x03 \x01(\t\x12\x11\n\timage_url\x18\x04 \x01(\t\"\xf2\x01\n\x0b\x41pplication\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x14\n\x0cworkflow_ids\x18\x05 \x03(\t\x12\x11\n\tagent_ids\x18\x06 \x03(\t\x12,\n\x06status\x18\x07 \x01(\x0e\x32\x1c.analytics.ApplicationStatus\x12\x12\n\ncreated_at\x18\x08 \x01(\t\x12\x12\n\nupdated_at\x18\t \x01(\t\x12\x10\n\x08\x61pi_keys\x18\n \x03(\t\x12\x12\n\nis_deleted\x18\x0b \x01(\x08\"\xde\x01\n\x12\x41pplicationMetrics\x12\x16\n\x0e\x61pplication_id\x18\x01 \x01(\t\x12\x16\n\x0etotal_requests\x18\x02 \x01(\x05\x12\x1b\n\x13successful_requests\x18\x03 \x01(\x05\x12\x17\n\x0f\x66\x61iled_requests\x18\x04 \x01(\x05\x12\x14\n\x0c\x63redits_used\x18\x05 \x01(\x02\x12\x17\n\x0flast_request_at\x18\x06 \x01(\t\x12\x33\n\x0busage_trend\x18\x07 \x03(\x0b\x32\x1e.analytics.TimeSeriesDataPoint\"\x82\x01\n\x16TrackActivationRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x32\n\nevent_type\x18\x02 \x01(\x0e\x32\x1e.analytics.ActivationEventType\x12\x11\n\tentity_id\x18\x03 \x01(\t\x12\x10\n\x08metadata\x18\x04 \x01(\t\"R\n\x17TrackActivationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x15\n\ractivation_id\x18\x03 \x01(\t\"H\n\x1bGetActivationMetricsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x18\n\x10time_period_days\x18\x02 \x01(\x05\"o\n\x1cGetActivationMetricsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12-\n\x07metrics\x18\x03 \x01(\x0b\x32\x1c.analytics.ActivationMetrics\"\xaf\x02\n\x11\x41\x63tivationMetrics\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x15\n\rtotal_signups\x18\x02 \x01(\x05\x12\x17\n\x0f\x61\x63tivated_users\x18\x03 \x01(\x05\x12\x17\n\x0f\x61\x63tivation_rate\x18\x04 \x01(\x02\x12M\n\x11\x61\x63tivation_funnel\x18\x05 \x03(\x0b\x32\x32.analytics.ActivationMetrics.ActivationFunnelEntry\x12\x38\n\x10\x61\x63tivation_trend\x18\x06 \x03(\x0b\x32\x1e.analytics.TimeSeriesDataPoint\x1a\x37\n\x15\x41\x63tivationFunnelEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"\xb6\x01\n\x14\x43reateWebhookRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x16\n\x0e\x61pplication_id\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\x12\x30\n\x0b\x65vent_types\x18\x04 \x03(\x0e\x32\x1b.analytics.WebhookEventType\x12\x0e\n\x06secret\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x11\n\tis_active\x18\x07 \x01(\x08\"^\n\x15\x43reateWebhookResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12#\n\x07webhook\x18\x03 \x01(\x0b\x32\x12.analytics.Webhook\"\x86\x01\n\x12GetWebhooksRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x16\n\x0e\x61pplication_id\x18\x02 \x01(\t\x12(\n\x06status\x18\x03 \x01(\x0e\x32\x18.analytics.WebhookStatus\x12\r\n\x05limit\x18\x04 \x01(\x05\x12\x0e\n\x06offset\x18\x05 \x01(\x05\"r\n\x13GetWebhooksResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12$\n\x08webhooks\x18\x03 \x03(\x0b\x32\x12.analytics.Webhook\x12\x13\n\x0btotal_count\x18\x04 \x01(\x05\"\xb2\x01\n\x14UpdateWebhookRequest\x12\x12\n\nwebhook_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\x12\x30\n\x0b\x65vent_types\x18\x04 \x03(\x0e\x32\x1b.analytics.WebhookEventType\x12\x0e\n\x06secret\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x11\n\tis_active\x18\x07 \x01(\x08\"^\n\x15UpdateWebhookResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12#\n\x07webhook\x18\x03 \x01(\x0b\x32\x12.analytics.Webhook\";\n\x14\x44\x65leteWebhookRequest\x12\x12\n\nwebhook_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"9\n\x15\x44\x65leteWebhookResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"u\n\x15GetWebhookLogsRequest\x12\x12\n\nwebhook_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x18\n\x10time_period_days\x18\x03 \x01(\x05\x12\r\n\x05limit\x18\x04 \x01(\x05\x12\x0e\n\x06offset\x18\x05 \x01(\x05\"t\n\x16GetWebhookLogsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12#\n\x04logs\x18\x03 \x03(\x0b\x32\x15.analytics.WebhookLog\x12\x13\n\x0btotal_count\x18\x04 \x01(\x05\"\xc0\x02\n\x07Webhook\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x16\n\x0e\x61pplication_id\x18\x03 \x01(\t\x12\x0b\n\x03url\x18\x04 \x01(\t\x12\x30\n\x0b\x65vent_types\x18\x05 \x03(\x0e\x32\x1b.analytics.WebhookEventType\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x11\n\tis_active\x18\x07 \x01(\x08\x12(\n\x06status\x18\x08 \x01(\x0e\x32\x18.analytics.WebhookStatus\x12\x12\n\ncreated_at\x18\t \x01(\t\x12\x12\n\nupdated_at\x18\n \x01(\t\x12\x16\n\x0e\x64\x65livery_count\x18\x0b \x01(\x05\x12\x15\n\rfailure_count\x18\x0c \x01(\x05\x12\x18\n\x10last_delivery_at\x18\r \x01(\t\"\xf1\x01\n\nWebhookLog\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nwebhook_id\x18\x02 \x01(\t\x12/\n\nevent_type\x18\x03 \x01(\x0e\x32\x1b.analytics.WebhookEventType\x12\x0f\n\x07payload\x18\x04 \x01(\t\x12\x17\n\x0fresponse_status\x18\x05 \x01(\x05\x12\x15\n\rresponse_body\x18\x06 \x01(\t\x12\x15\n\rerror_message\x18\x07 \x01(\t\x12\x14\n\x0c\x64\x65livered_at\x18\x08 \x01(\t\x12\x0f\n\x07success\x18\t \x01(\x08\x12\x13\n\x0bretry_count\x18\n \x01(\x05\"H\n\x1bGetDashboardOverviewRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x18\n\x10time_period_days\x18\x02 \x01(\x05\"p\n\x1cGetDashboardOverviewResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12.\n\x08overview\x18\x03 \x01(\x0b\x32\x1c.analytics.DashboardOverview\"\xcd\x03\n\x11\x44\x61shboardOverview\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x15\n\ractive_agents\x18\x02 \x01(\x05\x12\x14\n\x0c\x63redit_usage\x18\x03 \x01(\x02\x12\x16\n\x0e\x61gent_requests\x18\x04 \x01(\x05\x12\x19\n\x11workflow_requests\x18\x05 \x01(\x05\x12\x13\n\x0b\x63ustom_mcps\x18\x06 \x01(\x05\x12K\n\x10\x63redit_breakdown\x18\x07 \x03(\x0b\x32\x31.analytics.DashboardOverview.CreditBreakdownEntry\x12=\n\x10\x61pp_credit_usage\x18\x08 \x03(\x0b\x32#.analytics.DashboardTimeSeriesPoint\x12?\n\x11\x61gent_performance\x18\t \x03(\x0b\x32$.analytics.AgentPerformanceDataPoint\x12-\n\rrecent_events\x18\n \x03(\x0b\x32\x16.analytics.RecentEvent\x1a\x36\n\x14\x43reditBreakdownEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"<\n\x18\x44\x61shboardTimeSeriesPoint\x12\x11\n\ttimestamp\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02\"Y\n\x19\x41gentPerformanceDataPoint\x12\x11\n\ttimestamp\x18\x01 \x01(\t\x12\x10\n\x08requests\x18\x02 \x01(\x05\x12\x17\n\x0f\x63ompletion_rate\x18\x03 \x01(\x02\"p\n\x0bRecentEvent\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\t\x12\x10\n\x08\x64uration\x18\x05 \x01(\t\x12\x0c\n\x04user\x18\x06 \x01(\t\";\n\x1aGetDashboardMetricsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ys\x18\x02 \x01(\x05\"m\n\x1bGetDashboardMetricsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x07metrics\x18\x03 \x01(\x0b\x32\x1b.analytics.DashboardMetrics\"\x9d\x02\n\x10\x44\x61shboardMetrics\x12\x15\n\ractive_agents\x18\x01 \x01(\x05\x12\x14\n\x0c\x63redit_usage\x18\x02 \x01(\x02\x12\x16\n\x0e\x61gent_requests\x18\x03 \x01(\x05\x12\x19\n\x11workflow_requests\x18\x04 \x01(\x05\x12\x13\n\x0b\x63ustom_mcps\x18\x05 \x01(\x05\x12\x1b\n\x13\x63redit_usage_change\x18\x06 \x01(\x02\x12!\n\x19\x61gent_requests_change_pct\x18\x07 \x01(\x02\x12$\n\x1cworkflow_requests_change_pct\x18\x08 \x01(\x02\x12\x1a\n\x12\x63ustom_mcps_change\x18\t \x01(\x05\x12\x12\n\ntotal_cost\x18\n \x01(\x02\"?\n\x1eGetCreditUsageBreakdownRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ys\x18\x02 \x01(\x05\"{\n\x1fGetCreditUsageBreakdownResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x36\n\tbreakdown\x18\x03 \x03(\x0b\x32#.analytics.CreditUsageBreakdownItem\"g\n\x18\x43reditUsageBreakdownItem\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\x14\n\x0c\x63redits_used\x18\x02 \x01(\x02\x12\x0c\n\x04\x63ost\x18\x03 \x01(\x02\x12\x15\n\rrequest_count\x18\x04 \x01(\x05\"Q\n\x18GetAppCreditUsageRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x16\n\x0e\x61pplication_id\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x61ys\x18\x03 \x01(\x05\"j\n\x19GetAppCreditUsageResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32\x1d.analytics.AppCreditUsageData\"x\n\x12\x41ppCreditUsageData\x12\x15\n\rtotal_credits\x18\x01 \x01(\x02\x12\x12\n\ntotal_cost\x18\x02 \x01(\x02\x12\x37\n\ntimeseries\x18\x03 \x03(\x0b\x32#.analytics.AppCreditTimeSeriesPoint\"\x86\x01\n\x18\x41ppCreditTimeSeriesPoint\x12\x11\n\ttimestamp\x18\x01 \x01(\t\x12\x14\n\x0c\x63redits_used\x18\x02 \x01(\x02\x12\x0c\n\x04\x63ost\x18\x03 \x01(\x02\x12\x1a\n\x12\x63umulative_credits\x18\x04 \x01(\x02\x12\x17\n\x0f\x63umulative_cost\x18\x05 \x01(\x02\"=\n\x1bGetLatestApiRequestsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\"p\n\x1cGetLatestApiRequestsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12.\n\x06\x65vents\x18\x03 \x03(\x0b\x32\x1e.analytics.ApiRequestEventItem\"\xd8\x01\n\x13\x41piRequestEventItem\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x03 \x01(\t\x12\x0e\n\x06method\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x11\n\ttimestamp\x18\x06 \x01(\t\x12\x13\n\x0b\x64uration_ms\x18\x07 \x01(\x05\x12\x12\n\nuser_email\x18\x08 \x01(\t\x12\x15\n\rerror_message\x18\t \x01(\t\x12\x14\n\x0c\x63redits_used\x18\n \x01(\x02\x12\x0c\n\x04\x63ost\x18\x0b \x01(\x02\";\n\x1aGetAgentPerformanceRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ys\x18\x02 \x01(\x05\"u\n\x1bGetAgentPerformanceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x34\n\x0bperformance\x18\x03 \x03(\x0b\x32\x1f.analytics.AgentPerformanceItem\"\x81\x02\n\x14\x41gentPerformanceItem\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x12\n\nagent_name\x18\x02 \x01(\t\x12\x16\n\x0etotal_requests\x18\x03 \x01(\x05\x12\x1b\n\x13successful_requests\x18\x04 \x01(\x05\x12\x17\n\x0f\x66\x61iled_requests\x18\x05 \x01(\x05\x12\x14\n\x0csuccess_rate\x18\x06 \x01(\x02\x12\x1c\n\x14\x61vg_response_time_ms\x18\x07 \x01(\x02\x12\x1a\n\x12total_credits_used\x18\x08 \x01(\x02\x12\x12\n\ntotal_cost\x18\t \x01(\x02\x12\x11\n\tis_active\x18\n \x01(\x08\">\n\x1dGetWorkflowUtilizationRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ys\x18\x02 \x01(\x05\"{\n\x1eGetWorkflowUtilizationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x37\n\x0butilization\x18\x03 \x03(\x0b\x32\".analytics.WorkflowUtilizationItem\"\x9b\x02\n\x17WorkflowUtilizationItem\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x15\n\rworkflow_name\x18\x02 \x01(\t\x12\x18\n\x10total_executions\x18\x03 \x01(\x05\x12\x1d\n\x15successful_executions\x18\x04 \x01(\x05\x12\x19\n\x11\x66\x61iled_executions\x18\x05 \x01(\x05\x12\x14\n\x0csuccess_rate\x18\x06 \x01(\x02\x12\x1d\n\x15\x61vg_execution_time_ms\x18\x07 \x01(\x02\x12\x1b\n\x13\x63ompletion_rate_pct\x18\x08 \x01(\x02\x12\x1a\n\x12total_credits_used\x18\t \x01(\x02\x12\x12\n\ntotal_cost\x18\n \x01(\x02\":\n\x18GetSystemActivityRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\"p\n\x19GetSystemActivityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x31\n\nactivities\x18\x03 \x03(\x0b\x32\x1d.analytics.SystemActivityItem\"\xc8\x01\n\x12SystemActivityItem\x12\n\n\x02id\x18\x01 \x01(\t\x12\x15\n\ractivity_type\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x10\n\x08severity\x18\x05 \x01(\t\x12\x0e\n\x06status\x18\x06 \x01(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\t\x12\x0f\n\x07user_id\x18\x08 \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\t \x01(\t\x12\x10\n\x08metadata\x18\n \x01(\t\"\x9c\x03\n\x17RecordApiRequestRequest\x12,\n\x0crequest_type\x18\x01 \x01(\x0e\x32\x16.analytics.RequestType\x12\x10\n\x08\x65ndpoint\x18\x02 \x01(\t\x12(\n\x06status\x18\x03 \x01(\x0e\x32\x18.analytics.RequestStatus\x12\x13\n\x0b\x64uration_ms\x18\x04 \x01(\x05\x12\x0f\n\x07user_id\x18\x05 \x01(\t\x12\x12\n\nuser_email\x18\x06 \x01(\t\x12\x0e\n\x06method\x18\x07 \x01(\t\x12\x12\n\nip_address\x18\x08 \x01(\t\x12\x12\n\nuser_agent\x18\t \x01(\t\x12\x14\n\x0crequest_data\x18\n \x01(\t\x12\x15\n\rresponse_data\x18\x0b \x01(\t\x12\x15\n\rerror_message\x18\x0c \x01(\t\x12\x10\n\x08\x61gent_id\x18\r \x01(\t\x12\x13\n\x0bworkflow_id\x18\x0e \x01(\t\x12\x16\n\x0e\x61pplication_id\x18\x0f \x01(\t\x12\x14\n\x0c\x63redits_used\x18\x10 \x01(\x02\x12\x0c\n\x04\x63ost\x18\x11 \x01(\x02\"N\n\x18RecordApiRequestResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08\x65vent_id\x18\x03 \x01(\t\"\xb2\x01\n\x1bRecordSystemActivityRequest\x12\x15\n\ractivity_type\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x10\n\x08severity\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x0f\n\x07user_id\x18\x06 \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x07 \x01(\t\x12\x10\n\x08metadata\x18\x08 \x01(\t\"U\n\x1cRecordSystemActivityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x13\n\x0b\x61\x63tivity_id\x18\x03 \x01(\t\"\xee\x02\n\x08\x41\x63tivity\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0bresource_id\x18\x02 \x01(\t\x12%\n\x04type\x18\x03 \x01(\x0e\x32\x17.analytics.ActivityType\x12\x0f\n\x07user_id\x18\x04 \x01(\t\x12)\n\x06status\x18\x05 \x01(\x0e\x32\x19.analytics.ActivityStatus\x12.\n\ruser_metadata\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\x12$\n\x04logs\x18\x07 \x03(\x0b\x32\x16.analytics.ActivityLog\x12(\n\x06\x65vents\x18\x08 \x03(\x0b\x32\x18.analytics.ActivityEvent\x12.\n\ncreated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xdc\x01\n\x0b\x41\x63tivityLog\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\t\x12$\n\x08log_type\x18\x03 \x01(\x0e\x32\x12.analytics.LogType\x12(\n\nlog_status\x18\x04 \x01(\x0e\x32\x14.analytics.LogStatus\x12,\n\x0blog_details\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12.\n\ncreated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xa4\x01\n\rActivityEvent\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\t\x12\x12\n\nevent_name\x18\x03 \x01(\t\x12.\n\revent_details\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\x12.\n\ncreated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xbf\x01\n\x15\x43reateActivityRequest\x12\x13\n\x0bresource_id\x18\x01 \x01(\t\x12%\n\x04type\x18\x02 \x01(\x0e\x32\x17.analytics.ActivityType\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12)\n\x06status\x18\x04 \x01(\x0e\x32\x19.analytics.ActivityStatus\x12.\n\ruser_metadata\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\"a\n\x16\x43reateActivityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\x08\x61\x63tivity\x18\x03 \x01(\x0b\x32\x13.analytics.Activity\"\xad\x01\n\x18\x43reateActivityLogRequest\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\t\x12$\n\x08log_type\x18\x02 \x01(\x0e\x32\x12.analytics.LogType\x12(\n\nlog_status\x18\x03 \x01(\x0e\x32\x14.analytics.LogStatus\x12,\n\x0blog_details\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\"\x88\x02\n\x1a\x43reateActivityEventRequest\x12\x13\n\x0bresource_id\x18\x01 \x01(\t\x12%\n\x04type\x18\x02 \x01(\x0e\x32\x17.analytics.ActivityType\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12)\n\x06status\x18\x04 \x01(\x0e\x32\x19.analytics.ActivityStatus\x12.\n\ruser_metadata\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x12\n\nevent_name\x18\x06 \x01(\t\x12.\n\revent_details\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\"h\n\x1b\x43reateActivityEventResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\'\n\x05\x65vent\x18\x03 \x01(\x0b\x32\x18.analytics.ActivityEvent\")\n\x12GetActivityRequest\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\t\"\x8c\x01\n\x13GetActivityResponse\x12%\n\x08\x61\x63tivity\x18\x01 \x01(\x0b\x32\x13.analytics.Activity\x12$\n\x04logs\x18\x02 \x03(\x0b\x32\x16.analytics.ActivityLog\x12(\n\x06\x65vents\x18\x03 \x03(\x0b\x32\x18.analytics.ActivityEvent\"v\n\x15ListActivitiesRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12%\n\x04type\x18\x02 \x01(\x0e\x32\x17.analytics.ActivityType\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x12\n\npage_token\x18\x04 \x01(\t\"\x93\x01\n\x12PaginationMetadata\x12\r\n\x05total\x18\x01 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x02 \x01(\x05\x12\x14\n\x0c\x63urrent_page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\x12\x15\n\rhas_next_page\x18\x05 \x01(\x08\x12\x19\n\x11has_previous_page\x18\x06 \x01(\x08\"\x94\x01\n\x16ListActivitiesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\'\n\nactivities\x18\x03 \x03(\x0b\x32\x13.analytics.Activity\x12/\n\x08metadata\x18\x04 \x01(\x0b\x32\x1d.analytics.PaginationMetadata\"\xb2\x01\n\x17ListActivityLogsRequest\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12$\n\x08log_type\x18\x03 \x01(\x0e\x32\x12.analytics.LogType\x12(\n\nlog_status\x18\x04 \x01(\x0e\x32\x14.analytics.LogStatus\x12\x11\n\tpage_size\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\"\x93\x01\n\x18ListActivityLogsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12$\n\x04\x64\x61ta\x18\x03 \x03(\x0b\x32\x16.analytics.ActivityLog\x12/\n\x08metadata\x18\x04 \x01(\x0b\x32\x1d.analytics.PaginationMetadata\"x\n\x19ListActivityEventsRequest\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x12\n\nevent_name\x18\x03 \x01(\t\x12\x11\n\tpage_size\x18\x04 \x01(\x05\x12\x0e\n\x06offset\x18\x05 \x01(\x05\"\x97\x01\n\x1aListActivityEventsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12&\n\x04\x64\x61ta\x18\x03 \x03(\x0b\x32\x18.analytics.ActivityEvent\x12/\n\x08metadata\x18\x04 \x01(\x0b\x32\x1d.analytics.PaginationMetadata\"\x8f\x01\n\x15UpdateActivityRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12)\n\x06status\x18\x03 \x01(\x0e\x32\x19.analytics.ActivityStatus\x12.\n\ruser_metadata\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\"a\n\x16UpdateActivityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\x08\x61\x63tivity\x18\x03 \x01(\x0b\x32\x13.analytics.Activity\"4\n\x15\x44\x65leteActivityRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\":\n\x16\x44\x65leteActivityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t*\xfc\x01\n\tEventType\x12\x1a\n\x16\x45VENT_TYPE_UNSPECIFIED\x10\x00\x12\x14\n\x10\x45VENT_TYPE_USAGE\x10\x01\x12\x15\n\x11\x45VENT_TYPE_RATING\x10\x02\x12\x17\n\x13\x45VENT_TYPE_CREATION\x10\x03\x12\x1b\n\x17\x45VENT_TYPE_MODIFICATION\x10\x04\x12\x17\n\x13\x45VENT_TYPE_DELETION\x10\x05\x12\x14\n\x10\x45VENT_TYPE_LOGIN\x10\x06\x12\x15\n\x11\x45VENT_TYPE_LOGOUT\x10\x07\x12\x14\n\x10\x45VENT_TYPE_ERROR\x10\x08\x12\x14\n\x10\x45VENT_TYPE_OTHER\x10\t*\xdb\x01\n\x0bServiceType\x12\x1c\n\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x14\n\x10SERVICE_TYPE_MCP\x10\x01\x12\x19\n\x15SERVICE_TYPE_WORKFLOW\x10\x02\x12\x16\n\x12SERVICE_TYPE_AGENT\x10\x03\x12\x15\n\x11SERVICE_TYPE_USER\x10\x04\x12\x1c\n\x18SERVICE_TYPE_APPLICATION\x10\x05\x12\x18\n\x14SERVICE_TYPE_WEBHOOK\x10\x06\x12\x16\n\x12SERVICE_TYPE_OTHER\x10\x07*\x99\x01\n\x11\x41pplicationStatus\x12\"\n\x1e\x41PPLICATION_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x41PPLICATION_STATUS_ACTIVE\x10\x01\x12\x1f\n\x1b\x41PPLICATION_STATUS_INACTIVE\x10\x02\x12 \n\x1c\x41PPLICATION_STATUS_SUSPENDED\x10\x03*\x82\x01\n\rWebhookStatus\x12\x1e\n\x1aWEBHOOK_STATUS_UNSPECIFIED\x10\x00\x12\x19\n\x15WEBHOOK_STATUS_ACTIVE\x10\x01\x12\x1b\n\x17WEBHOOK_STATUS_INACTIVE\x10\x02\x12\x19\n\x15WEBHOOK_STATUS_FAILED\x10\x03*\xd0\x03\n\x10WebhookEventType\x12\"\n\x1eWEBHOOK_EVENT_TYPE_UNSPECIFIED\x10\x00\x12&\n\"WEBHOOK_EVENT_TYPE_AGENT_EXECUTION\x10\x01\x12$\n WEBHOOK_EVENT_TYPE_AGENT_INVOKED\x10\x02\x12*\n&WEBHOOK_EVENT_TYPE_WORKFLOW_COMPLETION\x10\x03\x12(\n$WEBHOOK_EVENT_TYPE_WORKFLOW_EXECUTED\x10\x04\x12 \n\x1cWEBHOOK_EVENT_TYPE_MCP_USAGE\x10\x05\x12\x1f\n\x1bWEBHOOK_EVENT_TYPE_MCP_USED\x10\x06\x12&\n\"WEBHOOK_EVENT_TYPE_USER_ACTIVATION\x10\x07\x12\"\n\x1eWEBHOOK_EVENT_TYPE_USER_SIGNUP\x10\x08\x12%\n!WEBHOOK_EVENT_TYPE_USER_ACTIVATED\x10\t\x12 \n\x1cWEBHOOK_EVENT_TYPE_API_USAGE\x10\n\x12\x1c\n\x18WEBHOOK_EVENT_TYPE_ERROR\x10\x0b*\xcb\x02\n\x13\x41\x63tivationEventType\x12%\n!ACTIVATION_EVENT_TYPE_UNSPECIFIED\x10\x00\x12%\n!ACTIVATION_EVENT_TYPE_USER_SIGNUP\x10\x01\x12-\n)ACTIVATION_EVENT_TYPE_FIRST_AGENT_CREATED\x10\x02\x12\x30\n,ACTIVATION_EVENT_TYPE_FIRST_WORKFLOW_CREATED\x10\x03\x12.\n*ACTIVATION_EVENT_TYPE_FIRST_MCP_REGISTERED\x10\x04\x12(\n$ACTIVATION_EVENT_TYPE_FIRST_API_CALL\x10\x05\x12+\n\'ACTIVATION_EVENT_TYPE_MARKETPLACE_USAGE\x10\x06*\xc3\x01\n\x0bRequestType\x12\x1c\n\x18REQUEST_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18REQUEST_TYPE_API_REQUEST\x10\x01\x12\x1e\n\x1aREQUEST_TYPE_WORKFLOW_EXEC\x10\x02\x12\x1b\n\x17REQUEST_TYPE_AUTH_EVENT\x10\x03\x12\x1d\n\x19REQUEST_TYPE_AGENT_INVOKE\x10\x04\x12\x1c\n\x18REQUEST_TYPE_MCP_REQUEST\x10\x05*\x9d\x01\n\rRequestStatus\x12\x1e\n\x1aREQUEST_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n\x16REQUEST_STATUS_SUCCESS\x10\x01\x12\x18\n\x14REQUEST_STATUS_ERROR\x10\x02\x12\x1a\n\x16REQUEST_STATUS_PENDING\x10\x03\x12\x1a\n\x16REQUEST_STATUS_TIMEOUT\x10\x04*\xc9\x01\n\x0e\x43reditCategory\x12\x1f\n\x1b\x43REDIT_CATEGORY_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x43REDIT_CATEGORY_AGENTS\x10\x01\x12\x1d\n\x19\x43REDIT_CATEGORY_WORKFLOWS\x10\x02\x12\x1f\n\x1b\x43REDIT_CATEGORY_CUSTOM_MCPS\x10\x03\x12\x1f\n\x1b\x43REDIT_CATEGORY_APP_CREDITS\x10\x04\x12\x19\n\x15\x43REDIT_CATEGORY_OTHER\x10\x05*F\n\x0c\x41\x63tivityType\x12\x1d\n\x19\x41\x43TIVITY_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08WORKFLOW\x10\x01\x12\t\n\x05\x41GENT\x10\x02*j\n\x0e\x41\x63tivityStatus\x12\x1f\n\x1b\x41\x43TIVITY_STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07STARTED\x10\x01\x12\x0f\n\x0bIN_PROGRESS\x10\x02\x12\r\n\tCOMPLETED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04*K\n\x07LogType\x12\x18\n\x14LOG_TYPE_UNSPECIFIED\x10\x00\x12\x12\n\x0eTOOL_EXECUTION\x10\x01\x12\x12\n\x0eNODE_EXECUTION\x10\x02*A\n\tLogStatus\x12\x1a\n\x16LOG_STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07SUCCESS\x10\x01\x12\x0b\n\x07\x46\x41ILURE\x10\x02\x32\xf8\x10\n\x10\x41nalyticsService\x12K\n\nTrackEvent\x12\x1c.analytics.TrackEventRequest\x1a\x1d.analytics.TrackEventResponse\"\x00\x12`\n\x11GetServiceMetrics\x12#.analytics.GetServiceMetricsRequest\x1a$.analytics.GetServiceMetricsResponse\"\x00\x12Z\n\x0fGetUserActivity\x12!.analytics.GetUserActivityRequest\x1a\".analytics.GetUserActivityResponse\"\x00\x12\x63\n\x12GetRatingAnalytics\x12$.analytics.GetRatingAnalyticsRequest\x1a%.analytics.GetRatingAnalyticsResponse\"\x00\x12i\n\x14GetOverviewAnalytics\x12&.analytics.GetOverviewAnalyticsRequest\x1a\'.analytics.GetOverviewAnalyticsResponse\"\x00\x12Z\n\x0fTrackActivation\x12!.analytics.TrackActivationRequest\x1a\".analytics.TrackActivationResponse\"\x00\x12i\n\x14GetActivationMetrics\x12&.analytics.GetActivationMetricsRequest\x1a\'.analytics.GetActivationMetricsResponse\"\x00\x12T\n\rCreateWebhook\x12\x1f.analytics.CreateWebhookRequest\x1a .analytics.CreateWebhookResponse\"\x00\x12N\n\x0bGetWebhooks\x12\x1d.analytics.GetWebhooksRequest\x1a\x1e.analytics.GetWebhooksResponse\"\x00\x12T\n\rUpdateWebhook\x12\x1f.analytics.UpdateWebhookRequest\x1a .analytics.UpdateWebhookResponse\"\x00\x12T\n\rDeleteWebhook\x12\x1f.analytics.DeleteWebhookRequest\x1a .analytics.DeleteWebhookResponse\"\x00\x12W\n\x0eGetWebhookLogs\x12 .analytics.GetWebhookLogsRequest\x1a!.analytics.GetWebhookLogsResponse\"\x00\x12i\n\x14GetDashboardOverview\x12&.analytics.GetDashboardOverviewRequest\x1a\'.analytics.GetDashboardOverviewResponse\"\x00\x12\x66\n\x13GetDashboardMetrics\x12%.analytics.GetDashboardMetricsRequest\x1a&.analytics.GetDashboardMetricsResponse\"\x00\x12r\n\x17GetCreditUsageBreakdown\x12).analytics.GetCreditUsageBreakdownRequest\x1a*.analytics.GetCreditUsageBreakdownResponse\"\x00\x12`\n\x11GetAppCreditUsage\x12#.analytics.GetAppCreditUsageRequest\x1a$.analytics.GetAppCreditUsageResponse\"\x00\x12i\n\x14GetLatestApiRequests\x12&.analytics.GetLatestApiRequestsRequest\x1a\'.analytics.GetLatestApiRequestsResponse\"\x00\x12\x66\n\x13GetAgentPerformance\x12%.analytics.GetAgentPerformanceRequest\x1a&.analytics.GetAgentPerformanceResponse\"\x00\x12o\n\x16GetWorkflowUtilization\x12(.analytics.GetWorkflowUtilizationRequest\x1a).analytics.GetWorkflowUtilizationResponse\"\x00\x12`\n\x11GetSystemActivity\x12#.analytics.GetSystemActivityRequest\x1a$.analytics.GetSystemActivityResponse\"\x00\x12]\n\x10RecordApiRequest\x12\".analytics.RecordApiRequestRequest\x1a#.analytics.RecordApiRequestResponse\"\x00\x12i\n\x14RecordSystemActivity\x12&.analytics.RecordSystemActivityRequest\x1a\'.analytics.RecordSystemActivityResponse\"\x00\x32\xe6\x04\n\x12\x41pplicationService\x12`\n\x11\x43reateApplication\x12#.analytics.CreateApplicationRequest\x1a$.analytics.CreateApplicationResponse\"\x00\x12Z\n\x0fGetApplications\x12!.analytics.GetApplicationsRequest\x1a\".analytics.GetApplicationsResponse\"\x00\x12W\n\x0eGetApplication\x12 .analytics.GetApplicationRequest\x1a!.analytics.GetApplicationResponse\"\x00\x12`\n\x11UpdateApplication\x12#.analytics.UpdateApplicationRequest\x1a$.analytics.UpdateApplicationResponse\"\x00\x12`\n\x11\x44\x65leteApplication\x12#.analytics.DeleteApplicationRequest\x1a$.analytics.DeleteApplicationResponse\"\x00\x12u\n\x18\x41ttachImageToApplication\x12*.analytics.AttachImageToApplicationRequest\x1a+.analytics.AttachImageToApplicationResponse\"\x00\x32\xb3\x06\n\x0f\x41\x63tivityService\x12U\n\x0e\x43reateActivity\x12 .analytics.CreateActivityRequest\x1a!.analytics.CreateActivityResponse\x12U\n\x0eUpdateActivity\x12 .analytics.UpdateActivityRequest\x1a!.analytics.UpdateActivityResponse\x12U\n\x0e\x44\x65leteActivity\x12 .analytics.DeleteActivityRequest\x1a!.analytics.DeleteActivityResponse\x12P\n\x11\x43reateActivityLog\x12#.analytics.CreateActivityLogRequest\x1a\x16.analytics.ActivityLog\x12\x64\n\x13\x43reateActivityEvent\x12%.analytics.CreateActivityEventRequest\x1a&.analytics.CreateActivityEventResponse\x12L\n\x0bGetActivity\x12\x1d.analytics.GetActivityRequest\x1a\x1e.analytics.GetActivityResponse\x12U\n\x0eListActivities\x12 .analytics.ListActivitiesRequest\x1a!.analytics.ListActivitiesResponse\x12[\n\x10ListActivityLogs\x12\".analytics.ListActivityLogsRequest\x1a#.analytics.ListActivityLogsResponse\x12\x61\n\x12ListActivityEvents\x12$.analytics.ListActivityEventsRequest\x1a%.analytics.ListActivityEventsResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'analytics_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_RATINGANALYTICS_RATINGDISTRIBUTIONENTRY']._loaded_options = None
  _globals['_RATINGANALYTICS_RATINGDISTRIBUTIONENTRY']._serialized_options = b'8\001'
  _globals['_ACTIVATIONMETRICS_ACTIVATIONFUNNELENTRY']._loaded_options = None
  _globals['_ACTIVATIONMETRICS_ACTIVATIONFUNNELENTRY']._serialized_options = b'8\001'
  _globals['_DASHBOARDOVERVIEW_CREDITBREAKDOWNENTRY']._loaded_options = None
  _globals['_DASHBOARDOVERVIEW_CREDITBREAKDOWNENTRY']._serialized_options = b'8\001'
  _globals['_EVENTTYPE']._serialized_start=14918
  _globals['_EVENTTYPE']._serialized_end=15170
  _globals['_SERVICETYPE']._serialized_start=15173
  _globals['_SERVICETYPE']._serialized_end=15392
  _globals['_APPLICATIONSTATUS']._serialized_start=15395
  _globals['_APPLICATIONSTATUS']._serialized_end=15548
  _globals['_WEBHOOKSTATUS']._serialized_start=15551
  _globals['_WEBHOOKSTATUS']._serialized_end=15681
  _globals['_WEBHOOKEVENTTYPE']._serialized_start=15684
  _globals['_WEBHOOKEVENTTYPE']._serialized_end=16148
  _globals['_ACTIVATIONEVENTTYPE']._serialized_start=16151
  _globals['_ACTIVATIONEVENTTYPE']._serialized_end=16482
  _globals['_REQUESTTYPE']._serialized_start=16485
  _globals['_REQUESTTYPE']._serialized_end=16680
  _globals['_REQUESTSTATUS']._serialized_start=16683
  _globals['_REQUESTSTATUS']._serialized_end=16840
  _globals['_CREDITCATEGORY']._serialized_start=16843
  _globals['_CREDITCATEGORY']._serialized_end=17044
  _globals['_ACTIVITYTYPE']._serialized_start=17046
  _globals['_ACTIVITYTYPE']._serialized_end=17116
  _globals['_ACTIVITYSTATUS']._serialized_start=17118
  _globals['_ACTIVITYSTATUS']._serialized_end=17224
  _globals['_LOGTYPE']._serialized_start=17226
  _globals['_LOGTYPE']._serialized_end=17301
  _globals['_LOGSTATUS']._serialized_start=17303
  _globals['_LOGSTATUS']._serialized_end=17368
  _globals['_TRACKEVENTREQUEST']._serialized_start=94
  _globals['_TRACKEVENTREQUEST']._serialized_end=255
  _globals['_TRACKEVENTRESPONSE']._serialized_start=257
  _globals['_TRACKEVENTRESPONSE']._serialized_end=329
  _globals['_GETSERVICEMETRICSREQUEST']._serialized_start=331
  _globals['_GETSERVICEMETRICSREQUEST']._serialized_end=448
  _globals['_GETSERVICEMETRICSRESPONSE']._serialized_start=450
  _globals['_GETSERVICEMETRICSRESPONSE']._serialized_end=555
  _globals['_SERVICEMETRICS']._serialized_start=558
  _globals['_SERVICEMETRICS']._serialized_end=765
  _globals['_TIMESERIESDATAPOINT']._serialized_start=767
  _globals['_TIMESERIESDATAPOINT']._serialized_end=817
  _globals['_GETUSERACTIVITYREQUEST']._serialized_start=819
  _globals['_GETUSERACTIVITYREQUEST']._serialized_end=886
  _globals['_GETUSERACTIVITYRESPONSE']._serialized_start=888
  _globals['_GETUSERACTIVITYRESPONSE']._serialized_end=990
  _globals['_USERACTIVITY']._serialized_start=993
  _globals['_USERACTIVITY']._serialized_end=1225
  _globals['_GETRATINGANALYTICSREQUEST']._serialized_start=1227
  _globals['_GETRATINGANALYTICSREQUEST']._serialized_end=1345
  _globals['_GETRATINGANALYTICSRESPONSE']._serialized_start=1347
  _globals['_GETRATINGANALYTICSRESPONSE']._serialized_end=1456
  _globals['_RATINGANALYTICS']._serialized_start=1459
  _globals['_RATINGANALYTICS']._serialized_end=1727
  _globals['_RATINGANALYTICS_RATINGDISTRIBUTIONENTRY']._serialized_start=1670
  _globals['_RATINGANALYTICS_RATINGDISTRIBUTIONENTRY']._serialized_end=1727
  _globals['_GETOVERVIEWANALYTICSREQUEST']._serialized_start=1729
  _globals['_GETOVERVIEWANALYTICSREQUEST']._serialized_end=1825
  _globals['_GETOVERVIEWANALYTICSRESPONSE']._serialized_start=1827
  _globals['_GETOVERVIEWANALYTICSRESPONSE']._serialized_end=1940
  _globals['_OVERVIEWANALYTICS']._serialized_start=1943
  _globals['_OVERVIEWANALYTICS']._serialized_end=2337
  _globals['_CREDITUSAGE']._serialized_start=2340
  _globals['_CREDITUSAGE']._serialized_end=2481
  _globals['_REQUESTMETRICS']._serialized_start=2484
  _globals['_REQUESTMETRICS']._serialized_end=2664
  _globals['_RECENTACTIVITY']._serialized_start=2667
  _globals['_RECENTACTIVITY']._serialized_end=2820
  _globals['_CREATEAPPLICATIONREQUEST']._serialized_start=2823
  _globals['_CREATEAPPLICATIONREQUEST']._serialized_end=2960
  _globals['_CREATEAPPLICATIONRESPONSE']._serialized_start=2962
  _globals['_CREATEAPPLICATIONRESPONSE']._serialized_end=3068
  _globals['_GETAPPLICATIONSREQUEST']._serialized_start=3070
  _globals['_GETAPPLICATIONSREQUEST']._serialized_end=3188
  _globals['_GETAPPLICATIONSRESPONSE']._serialized_start=3190
  _globals['_GETAPPLICATIONSRESPONSE']._serialized_end=3316
  _globals['_GETAPPLICATIONREQUEST']._serialized_start=3318
  _globals['_GETAPPLICATIONREQUEST']._serialized_end=3382
  _globals['_GETAPPLICATIONRESPONSE']._serialized_start=3385
  _globals['_GETAPPLICATIONRESPONSE']._serialized_end=3536
  _globals['_UPDATEAPPLICATIONREQUEST']._serialized_start=3539
  _globals['_UPDATEAPPLICATIONREQUEST']._serialized_end=3746
  _globals['_UPDATEAPPLICATIONRESPONSE']._serialized_start=3748
  _globals['_UPDATEAPPLICATIONRESPONSE']._serialized_end=3854
  _globals['_DELETEAPPLICATIONREQUEST']._serialized_start=3856
  _globals['_DELETEAPPLICATIONREQUEST']._serialized_end=3923
  _globals['_DELETEAPPLICATIONRESPONSE']._serialized_start=3925
  _globals['_DELETEAPPLICATIONRESPONSE']._serialized_end=3986
  _globals['_ATTACHIMAGETOAPPLICATIONREQUEST']._serialized_start=3989
  _globals['_ATTACHIMAGETOAPPLICATIONREQUEST']._serialized_end=4144
  _globals['_ATTACHIMAGETOAPPLICATIONRESPONSE']._serialized_start=4146
  _globals['_ATTACHIMAGETOAPPLICATIONRESPONSE']._serialized_end=4251
  _globals['_APPLICATION']._serialized_start=4254
  _globals['_APPLICATION']._serialized_end=4496
  _globals['_APPLICATIONMETRICS']._serialized_start=4499
  _globals['_APPLICATIONMETRICS']._serialized_end=4721
  _globals['_TRACKACTIVATIONREQUEST']._serialized_start=4724
  _globals['_TRACKACTIVATIONREQUEST']._serialized_end=4854
  _globals['_TRACKACTIVATIONRESPONSE']._serialized_start=4856
  _globals['_TRACKACTIVATIONRESPONSE']._serialized_end=4938
  _globals['_GETACTIVATIONMETRICSREQUEST']._serialized_start=4940
  _globals['_GETACTIVATIONMETRICSREQUEST']._serialized_end=5012
  _globals['_GETACTIVATIONMETRICSRESPONSE']._serialized_start=5014
  _globals['_GETACTIVATIONMETRICSRESPONSE']._serialized_end=5125
  _globals['_ACTIVATIONMETRICS']._serialized_start=5128
  _globals['_ACTIVATIONMETRICS']._serialized_end=5431
  _globals['_ACTIVATIONMETRICS_ACTIVATIONFUNNELENTRY']._serialized_start=5376
  _globals['_ACTIVATIONMETRICS_ACTIVATIONFUNNELENTRY']._serialized_end=5431
  _globals['_CREATEWEBHOOKREQUEST']._serialized_start=5434
  _globals['_CREATEWEBHOOKREQUEST']._serialized_end=5616
  _globals['_CREATEWEBHOOKRESPONSE']._serialized_start=5618
  _globals['_CREATEWEBHOOKRESPONSE']._serialized_end=5712
  _globals['_GETWEBHOOKSREQUEST']._serialized_start=5715
  _globals['_GETWEBHOOKSREQUEST']._serialized_end=5849
  _globals['_GETWEBHOOKSRESPONSE']._serialized_start=5851
  _globals['_GETWEBHOOKSRESPONSE']._serialized_end=5965
  _globals['_UPDATEWEBHOOKREQUEST']._serialized_start=5968
  _globals['_UPDATEWEBHOOKREQUEST']._serialized_end=6146
  _globals['_UPDATEWEBHOOKRESPONSE']._serialized_start=6148
  _globals['_UPDATEWEBHOOKRESPONSE']._serialized_end=6242
  _globals['_DELETEWEBHOOKREQUEST']._serialized_start=6244
  _globals['_DELETEWEBHOOKREQUEST']._serialized_end=6303
  _globals['_DELETEWEBHOOKRESPONSE']._serialized_start=6305
  _globals['_DELETEWEBHOOKRESPONSE']._serialized_end=6362
  _globals['_GETWEBHOOKLOGSREQUEST']._serialized_start=6364
  _globals['_GETWEBHOOKLOGSREQUEST']._serialized_end=6481
  _globals['_GETWEBHOOKLOGSRESPONSE']._serialized_start=6483
  _globals['_GETWEBHOOKLOGSRESPONSE']._serialized_end=6599
  _globals['_WEBHOOK']._serialized_start=6602
  _globals['_WEBHOOK']._serialized_end=6922
  _globals['_WEBHOOKLOG']._serialized_start=6925
  _globals['_WEBHOOKLOG']._serialized_end=7166
  _globals['_GETDASHBOARDOVERVIEWREQUEST']._serialized_start=7168
  _globals['_GETDASHBOARDOVERVIEWREQUEST']._serialized_end=7240
  _globals['_GETDASHBOARDOVERVIEWRESPONSE']._serialized_start=7242
  _globals['_GETDASHBOARDOVERVIEWRESPONSE']._serialized_end=7354
  _globals['_DASHBOARDOVERVIEW']._serialized_start=7357
  _globals['_DASHBOARDOVERVIEW']._serialized_end=7818
  _globals['_DASHBOARDOVERVIEW_CREDITBREAKDOWNENTRY']._serialized_start=7764
  _globals['_DASHBOARDOVERVIEW_CREDITBREAKDOWNENTRY']._serialized_end=7818
  _globals['_DASHBOARDTIMESERIESPOINT']._serialized_start=7820
  _globals['_DASHBOARDTIMESERIESPOINT']._serialized_end=7880
  _globals['_AGENTPERFORMANCEDATAPOINT']._serialized_start=7882
  _globals['_AGENTPERFORMANCEDATAPOINT']._serialized_end=7971
  _globals['_RECENTEVENT']._serialized_start=7973
  _globals['_RECENTEVENT']._serialized_end=8085
  _globals['_GETDASHBOARDMETRICSREQUEST']._serialized_start=8087
  _globals['_GETDASHBOARDMETRICSREQUEST']._serialized_end=8146
  _globals['_GETDASHBOARDMETRICSRESPONSE']._serialized_start=8148
  _globals['_GETDASHBOARDMETRICSRESPONSE']._serialized_end=8257
  _globals['_DASHBOARDMETRICS']._serialized_start=8260
  _globals['_DASHBOARDMETRICS']._serialized_end=8545
  _globals['_GETCREDITUSAGEBREAKDOWNREQUEST']._serialized_start=8547
  _globals['_GETCREDITUSAGEBREAKDOWNREQUEST']._serialized_end=8610
  _globals['_GETCREDITUSAGEBREAKDOWNRESPONSE']._serialized_start=8612
  _globals['_GETCREDITUSAGEBREAKDOWNRESPONSE']._serialized_end=8735
  _globals['_CREDITUSAGEBREAKDOWNITEM']._serialized_start=8737
  _globals['_CREDITUSAGEBREAKDOWNITEM']._serialized_end=8840
  _globals['_GETAPPCREDITUSAGEREQUEST']._serialized_start=8842
  _globals['_GETAPPCREDITUSAGEREQUEST']._serialized_end=8923
  _globals['_GETAPPCREDITUSAGERESPONSE']._serialized_start=8925
  _globals['_GETAPPCREDITUSAGERESPONSE']._serialized_end=9031
  _globals['_APPCREDITUSAGEDATA']._serialized_start=9033
  _globals['_APPCREDITUSAGEDATA']._serialized_end=9153
  _globals['_APPCREDITTIMESERIESPOINT']._serialized_start=9156
  _globals['_APPCREDITTIMESERIESPOINT']._serialized_end=9290
  _globals['_GETLATESTAPIREQUESTSREQUEST']._serialized_start=9292
  _globals['_GETLATESTAPIREQUESTSREQUEST']._serialized_end=9353
  _globals['_GETLATESTAPIREQUESTSRESPONSE']._serialized_start=9355
  _globals['_GETLATESTAPIREQUESTSRESPONSE']._serialized_end=9467
  _globals['_APIREQUESTEVENTITEM']._serialized_start=9470
  _globals['_APIREQUESTEVENTITEM']._serialized_end=9686
  _globals['_GETAGENTPERFORMANCEREQUEST']._serialized_start=9688
  _globals['_GETAGENTPERFORMANCEREQUEST']._serialized_end=9747
  _globals['_GETAGENTPERFORMANCERESPONSE']._serialized_start=9749
  _globals['_GETAGENTPERFORMANCERESPONSE']._serialized_end=9866
  _globals['_AGENTPERFORMANCEITEM']._serialized_start=9869
  _globals['_AGENTPERFORMANCEITEM']._serialized_end=10126
  _globals['_GETWORKFLOWUTILIZATIONREQUEST']._serialized_start=10128
  _globals['_GETWORKFLOWUTILIZATIONREQUEST']._serialized_end=10190
  _globals['_GETWORKFLOWUTILIZATIONRESPONSE']._serialized_start=10192
  _globals['_GETWORKFLOWUTILIZATIONRESPONSE']._serialized_end=10315
  _globals['_WORKFLOWUTILIZATIONITEM']._serialized_start=10318
  _globals['_WORKFLOWUTILIZATIONITEM']._serialized_end=10601
  _globals['_GETSYSTEMACTIVITYREQUEST']._serialized_start=10603
  _globals['_GETSYSTEMACTIVITYREQUEST']._serialized_end=10661
  _globals['_GETSYSTEMACTIVITYRESPONSE']._serialized_start=10663
  _globals['_GETSYSTEMACTIVITYRESPONSE']._serialized_end=10775
  _globals['_SYSTEMACTIVITYITEM']._serialized_start=10778
  _globals['_SYSTEMACTIVITYITEM']._serialized_end=10978
  _globals['_RECORDAPIREQUESTREQUEST']._serialized_start=10981
  _globals['_RECORDAPIREQUESTREQUEST']._serialized_end=11393
  _globals['_RECORDAPIREQUESTRESPONSE']._serialized_start=11395
  _globals['_RECORDAPIREQUESTRESPONSE']._serialized_end=11473
  _globals['_RECORDSYSTEMACTIVITYREQUEST']._serialized_start=11476
  _globals['_RECORDSYSTEMACTIVITYREQUEST']._serialized_end=11654
  _globals['_RECORDSYSTEMACTIVITYRESPONSE']._serialized_start=11656
  _globals['_RECORDSYSTEMACTIVITYRESPONSE']._serialized_end=11741
  _globals['_ACTIVITY']._serialized_start=11744
  _globals['_ACTIVITY']._serialized_end=12110
  _globals['_ACTIVITYLOG']._serialized_start=12113
  _globals['_ACTIVITYLOG']._serialized_end=12333
  _globals['_ACTIVITYEVENT']._serialized_start=12336
  _globals['_ACTIVITYEVENT']._serialized_end=12500
  _globals['_CREATEACTIVITYREQUEST']._serialized_start=12503
  _globals['_CREATEACTIVITYREQUEST']._serialized_end=12694
  _globals['_CREATEACTIVITYRESPONSE']._serialized_start=12696
  _globals['_CREATEACTIVITYRESPONSE']._serialized_end=12793
  _globals['_CREATEACTIVITYLOGREQUEST']._serialized_start=12796
  _globals['_CREATEACTIVITYLOGREQUEST']._serialized_end=12969
  _globals['_CREATEACTIVITYEVENTREQUEST']._serialized_start=12972
  _globals['_CREATEACTIVITYEVENTREQUEST']._serialized_end=13236
  _globals['_CREATEACTIVITYEVENTRESPONSE']._serialized_start=13238
  _globals['_CREATEACTIVITYEVENTRESPONSE']._serialized_end=13342
  _globals['_GETACTIVITYREQUEST']._serialized_start=13344
  _globals['_GETACTIVITYREQUEST']._serialized_end=13385
  _globals['_GETACTIVITYRESPONSE']._serialized_start=13388
  _globals['_GETACTIVITYRESPONSE']._serialized_end=13528
  _globals['_LISTACTIVITIESREQUEST']._serialized_start=13530
  _globals['_LISTACTIVITIESREQUEST']._serialized_end=13648
  _globals['_PAGINATIONMETADATA']._serialized_start=13651
  _globals['_PAGINATIONMETADATA']._serialized_end=13798
  _globals['_LISTACTIVITIESRESPONSE']._serialized_start=13801
  _globals['_LISTACTIVITIESRESPONSE']._serialized_end=13949
  _globals['_LISTACTIVITYLOGSREQUEST']._serialized_start=13952
  _globals['_LISTACTIVITYLOGSREQUEST']._serialized_end=14130
  _globals['_LISTACTIVITYLOGSRESPONSE']._serialized_start=14133
  _globals['_LISTACTIVITYLOGSRESPONSE']._serialized_end=14280
  _globals['_LISTACTIVITYEVENTSREQUEST']._serialized_start=14282
  _globals['_LISTACTIVITYEVENTSREQUEST']._serialized_end=14402
  _globals['_LISTACTIVITYEVENTSRESPONSE']._serialized_start=14405
  _globals['_LISTACTIVITYEVENTSRESPONSE']._serialized_end=14556
  _globals['_UPDATEACTIVITYREQUEST']._serialized_start=14559
  _globals['_UPDATEACTIVITYREQUEST']._serialized_end=14702
  _globals['_UPDATEACTIVITYRESPONSE']._serialized_start=14704
  _globals['_UPDATEACTIVITYRESPONSE']._serialized_end=14801
  _globals['_DELETEACTIVITYREQUEST']._serialized_start=14803
  _globals['_DELETEACTIVITYREQUEST']._serialized_end=14855
  _globals['_DELETEACTIVITYRESPONSE']._serialized_start=14857
  _globals['_DELETEACTIVITYRESPONSE']._serialized_end=14915
  _globals['_ANALYTICSSERVICE']._serialized_start=17371
  _globals['_ANALYTICSSERVICE']._serialized_end=19539
  _globals['_APPLICATIONSERVICE']._serialized_start=19542
  _globals['_APPLICATIONSERVICE']._serialized_end=20156
  _globals['_ACTIVITYSERVICE']._serialized_start=20159
  _globals['_ACTIVITYSERVICE']._serialized_end=20978
# @@protoc_insertion_point(module_scope)
