/**
 * Utility functions for migrating approval flags from config to definition
 */
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";

/**
 * Migrates approval flags from config to definition for all nodes in a workflow
 *
 * @param nodes The workflow nodes to migrate
 * @returns The migrated workflow nodes
 */
export function migrateApprovalFlagToDefinition(
  nodes: Node<WorkflowNodeData>[],
): Node<WorkflowNodeData>[] {
  return nodes.map((node: Node<WorkflowNodeData>) => {
    // If the node has requires_approval in config, move it to definition
    if (node.data?.config?.requires_approval !== undefined) {
      console.log(`Migrating requires_approval flag for node ${node.id} from config to definition`);

      const newNode: Node<WorkflowNodeData> = {
        ...node,
        data: {
          ...node.data,
          definition: node.data.definition
            ? {
                ...node.data.definition,
                requires_approval: node.data.config.requires_approval,
                // Ensure required properties are present with non-undefined values
                name: node.data.definition.name || "",
                display_name: node.data.definition.display_name || "",
                description: node.data.definition.description || "",
                category: node.data.definition.category || "",
                icon: node.data.definition.icon || "",
                beta: node.data.definition.beta || false,
                inputs: node.data.definition.inputs || [],
                outputs: node.data.definition.outputs || [],
                is_valid: node.data.definition.is_valid || false,
                path: node.data.definition.path || "",
              }
            : undefined,
        },
      };

      // Remove from config
      const newConfig = { ...node.data.config };
      delete newConfig.requires_approval;
      newNode.data.config = newConfig;

      return newNode;
    }

    return node;
  });
}
