import React from "react";
import { useInspector } from "./InspectorContext";
import { Notification } from "@/components/ui/notification";

/**
 * Component to manage notifications in the inspector panel
 */
export function NotificationManager() {
  const { notification, setNotification } = useInspector();

  return (
    <Notification
      isOpen={notification.isOpen}
      title={notification.title}
      message={notification.message}
      preserveState={notification.preserveState}
      onClose={() => setNotification({ ...notification, isOpen: false })}
    />
  );
}
