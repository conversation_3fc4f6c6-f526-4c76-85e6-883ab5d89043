"""
Test module for agent configuration extraction and nesting functionality.

This module tests the extract_and_nest_agent_config function to ensure it properly
transforms flattened agent configuration fields into the expected nested structure.
"""

import pytest
from app.services.workflow_builder.workflow_schema_converter import extract_and_nest_agent_config


class TestAgentConfigExtraction:
    """Test cases for agent configuration extraction and nesting."""

    def test_extract_basic_agent_config(self):
        """Test extraction of basic agent configuration fields."""
        # Arrange
        flattened_config = {
            "id": "agent-123",
            "name": "Test Agent",
            "description": "A test agent for validation",
            "system_message": "You are a helpful assistant",
            "model_provider": "openai",
            "model": "gpt-4o-mini",
            "temperature": 0.7,
            "max_tokens": 1000,
            "termination_condition": "task_complete",
            "tools": ["search", "calculator"],
            "capabilities": ["reasoning", "analysis"]
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        expected = {
            "id": "agent-123",
            "name": "Test Agent",
            "description": "A test agent for validation",
            "system_message": "You are a helpful assistant",
            "model_config": {
                "model_provider": "openai",
                "model": "gpt-4o-mini",
                "temperature": 0.7,
                "max_tokens": 1000
            },
            "termination_condition": "task_complete",
            "tools": ["search", "calculator"],
            "capabilities": ["reasoning", "analysis"]
        }
        assert result == expected

    def test_extract_with_prefixed_model_config(self):
        """Test extraction when model_config fields are prefixed."""
        # Arrange
        flattened_config = {
            "id": "agent-456",
            "name": "Prefixed Agent",
            "model_config_model_provider": "anthropic",
            "model_config_model": "claude-3-sonnet",
            "model_config_temperature": 0.5,
            "model_config_max_tokens": 2000,
            "tools": [],
            "capabilities": []
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        expected = {
            "id": "agent-456",
            "name": "Prefixed Agent",
            "model_config": {
                "model_provider": "anthropic",
                "model": "claude-3-sonnet",
                "temperature": 0.5,
                "max_tokens": 2000
            },
            "tools": [],
            "capabilities": []
        }
        assert result == expected

    def test_extract_with_mixed_field_sources(self):
        """Test extraction when some fields are prefixed and others are direct."""
        # Arrange
        flattened_config = {
            "id": "agent-789",
            "name": "Mixed Agent",
            "model_provider": "openai",  # Direct field
            "model_config_model": "gpt-4",  # Prefixed field
            "temperature": 0.8,  # Direct field
            "model_config_max_tokens": 1500,  # Prefixed field
            "system_message": "Mixed configuration test"
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        # Prefixed fields should take precedence over direct fields
        expected = {
            "id": "agent-789",
            "name": "Mixed Agent",
            "system_message": "Mixed configuration test",
            "model_config": {
                "model_provider": "openai",
                "model": "gpt-4",  # From prefixed field
                "temperature": 0.8,
                "max_tokens": 1500  # From prefixed field
            },
            "tools": [],
            "capabilities": []
        }
        assert result == expected

    def test_extract_with_defaults(self):
        """Test extraction with minimal input, ensuring defaults are applied."""
        # Arrange
        flattened_config = {
            "id": "minimal-agent"
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        expected = {
            "id": "minimal-agent",
            "model_config": {
                "model": "gpt-4o-mini",
                "temperature": 0.7,
                "max_tokens": 1000
            },
            "tools": [],
            "capabilities": []
        }
        assert result == expected

    def test_extract_with_invalid_numeric_values(self):
        """Test extraction with invalid numeric values, ensuring defaults are used."""
        # Arrange
        flattened_config = {
            "id": "invalid-agent",
            "name": "Invalid Numeric Agent",
            "temperature": "invalid_float",
            "max_tokens": "invalid_int",
            "model_provider": "openai"
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        expected = {
            "id": "invalid-agent",
            "name": "Invalid Numeric Agent",
            "model_config": {
                "model_provider": "openai",
                "model": "gpt-4o-mini",
                "temperature": 0.7,  # Default due to invalid input
                "max_tokens": 1000   # Default due to invalid input
            },
            "tools": [],
            "capabilities": []
        }
        assert result == expected

    def test_extract_removes_empty_strings(self):
        """Test that empty strings are removed from the result."""
        # Arrange
        flattened_config = {
            "id": "clean-agent",
            "name": "",  # Empty string should be removed
            "description": "Valid description",
            "system_message": "",  # Empty string should be removed
            "model_provider": "openai",
            "model": "",  # Empty string should be removed
            "tools": [],  # Empty array should be kept
            "capabilities": ["reasoning"]
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        expected = {
            "id": "clean-agent",
            "description": "Valid description",
            "model_config": {
                "model_provider": "openai",
                "model": "gpt-4o-mini",  # Default used due to empty string
                "temperature": 0.7,
                "max_tokens": 1000
            },
            "tools": [],  # Empty arrays are preserved
            "capabilities": ["reasoning"]
        }
        assert result == expected

    def test_extract_with_none_values(self):
        """Test that None values are removed from the result."""
        # Arrange
        flattened_config = {
            "id": "none-agent",
            "name": None,  # None should be removed
            "description": "Valid description",
            "system_message": None,  # None should be removed
            "model_provider": "anthropic",
            "temperature": None,  # None should use default
            "tools": None,  # None should use default (empty array)
            "capabilities": ["analysis"]
        }

        # Act
        result = extract_and_nest_agent_config(flattened_config)

        # Assert
        expected = {
            "id": "none-agent",
            "description": "Valid description",
            "model_config": {
                "model_provider": "anthropic",
                "model": "gpt-4o-mini",
                "temperature": 0.7,  # Default due to None
                "max_tokens": 1000
            },
            "tools": [],  # Default empty array
            "capabilities": ["analysis"]
        }
        assert result == expected


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
