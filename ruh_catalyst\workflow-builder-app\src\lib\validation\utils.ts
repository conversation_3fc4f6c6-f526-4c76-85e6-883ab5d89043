import { No<PERSON>, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";

/**
 * Checks if a node is a StartNode using multiple detection methods
 *
 * @param node The node to check
 * @returns True if the node is a StartNode, false otherwise
 */
export function isStartNode(node: Node<WorkflowNodeData>): boolean {
  // Safety check for null/undefined node or data
  if (!node || !node.data) return false;

  // Log detailed node information for debugging
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`[${timestamp}] [isStartNode] Checking if node is a StartNode:`, {
    id: node.id,
    type: node.type,
    dataType: node.data.type,
    originalType: node.data.originalType,
    definitionName: node.data.definition?.name,
    label: node.data.label
  });

  // Method 1: Check by originalType (primary method)
  if (node.data.originalType === "StartNode") {
    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by originalType`);
    return true;
  }

  // Method 2: Check by definition name
  if (node.data.definition?.name === "StartNode") {
    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by definition name`);
    return true;
  }

  // Method 3: Check by node type
  if (node.type === "StartNode" || node.data.type === "StartNode") {
    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by node type`);
    return true;
  }

  // Method 4: Check by label (as a fallback)
  if (node.data.label === "Start" || node.data.label === "StartNode") {
    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by label`);
    return true;
  }

  // Method 5: Check by component type and name pattern
  if (node.data.type === "component" &&
      node.data.definition?.name &&
      node.data.definition.name.toLowerCase().includes("start")) {
    console.log(`[${timestamp}] [isStartNode] Node ${node.id} identified as StartNode by component name pattern`);
    return true;
  }

  return false;
}

/**
 * Finds the StartNode in an array of nodes with detailed logging
 *
 * @param nodes The array of nodes to search
 * @returns The StartNode if found, undefined otherwise
 */
export function findStartNode(nodes: Node<WorkflowNodeData>[]): Node<WorkflowNodeData> | undefined {
  // Safety check for null/undefined nodes array
  if (!nodes || !Array.isArray(nodes)) {
    console.warn('[findStartNode] Nodes array is null, undefined, or not an array');
    return undefined;
  }

  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`[${timestamp}] [findStartNode] Searching for StartNode in ${nodes.length} nodes`);

  // Log all node types for debugging
  console.log(`[${timestamp}] [findStartNode] Node types in workflow:`,
    nodes.map(node => ({
      id: node.id,
      type: node.type,
      dataType: node.data?.type,
      originalType: node.data?.originalType,
      definitionName: node.data?.definition?.name,
      label: node.data?.label
    }))
  );

  // Find the first node that passes the isStartNode check
  const startNode = nodes.find(isStartNode);

  if (startNode) {
    console.log(`[${timestamp}] [findStartNode] Found StartNode with ID: ${startNode.id}`);
  } else {
    console.warn(`[${timestamp}] [findStartNode] No StartNode found in workflow with ${nodes.length} nodes`);
  }

  return startNode;
}

/**
 * Checks if a node is connected to the start node
 *
 * @param nodeId The ID of the node to check
 * @param connectedNodes Set of node IDs connected to the start node
 * @returns True if the node is connected, false otherwise
 */
export function isNodeConnected(nodeId: string, connectedNodes: Set<string>): boolean {
  return connectedNodes.has(nodeId);
}

/**
 * Gets nodes directly connected to the start node
 *
 * @param nodes The array of nodes
 * @param edges The array of edges
 * @param startNodeId The ID of the start node
 * @returns A set of node IDs directly connected to the start node
 */
export function getDirectlyConnectedNodes(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  startNodeId: string
): Set<string> {
  const directlyConnectedNodes = new Set<string>();

  // Find all edges where the start node is the source
  edges.forEach(edge => {
    if (edge.source === startNodeId) {
      directlyConnectedNodes.add(edge.target);
    }
  });

  return directlyConnectedNodes;
}

/**
 * Gets fields directly connected to the start node
 *
 * @param nodes The array of nodes
 * @param edges The array of edges
 * @param startNodeId The ID of the start node
 * @returns A map of node IDs to sets of field names that are directly connected to the start node
 */
export function getDirectlyConnectedFields(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  startNodeId: string
): Map<string, Set<string>> {
  const directlyConnectedFields = new Map<string, Set<string>>();

  // Find all edges where the start node is the source
  edges.forEach(edge => {
    if (edge.source === startNodeId && edge.target && edge.targetHandle) {
      // Initialize the set if it doesn't exist
      if (!directlyConnectedFields.has(edge.target)) {
        directlyConnectedFields.set(edge.target, new Set<string>());
      }

      // Add the field name to the set
      const fieldName = edge.targetHandle;
      directlyConnectedFields.get(edge.target)?.add(fieldName);
    }
  });

  return directlyConnectedFields;
}

/**
 * Gets all nodes connected to the start node using BFS
 *
 * @param nodes The array of nodes
 * @param edges The array of edges
 * @param startNodeId The ID of the start node
 * @returns A set of node IDs connected to the start node
 */
export function getConnectedNodes(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  startNodeId: string
): Set<string> {
  const connectedNodes = new Set<string>([startNodeId]);
  const queue: string[] = [startNodeId];

  // Create an adjacency list for faster lookups
  const adjacencyList = new Map<string, string[]>();

  // Initialize the adjacency list
  nodes.forEach(node => {
    adjacencyList.set(node.id, []);
  });

  // Populate the adjacency list
  edges.forEach(edge => {
    const sourceNeighbors = adjacencyList.get(edge.source) || [];
    sourceNeighbors.push(edge.target);
    adjacencyList.set(edge.source, sourceNeighbors);
  });

  // BFS to find all connected nodes
  while (queue.length > 0) {
    const currentNodeId = queue.shift()!;
    const neighbors = adjacencyList.get(currentNodeId) || [];

    for (const neighborId of neighbors) {
      if (!connectedNodes.has(neighborId)) {
        connectedNodes.add(neighborId);
        queue.push(neighborId);
      }
    }
  }

  return connectedNodes;
}

/**
 * Detects cycles in the workflow graph using DFS
 *
 * @param nodes The array of nodes
 * @param edges The array of edges
 * @returns An array of node IDs involved in cycles
 */
export function detectCycles(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): string[] {
  const adjacencyList = new Map<string, string[]>();

  // Initialize the adjacency list
  nodes.forEach(node => {
    adjacencyList.set(node.id, []);
  });

  // Populate the adjacency list
  edges.forEach(edge => {
    const sourceNeighbors = adjacencyList.get(edge.source) || [];
    sourceNeighbors.push(edge.target);
    adjacencyList.set(edge.source, sourceNeighbors);
  });

  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  const nodesInCycle = new Set<string>();

  // DFS function to detect cycles
  function dfs(nodeId: string): boolean {
    if (recursionStack.has(nodeId)) {
      nodesInCycle.add(nodeId);
      return true;
    }

    if (visited.has(nodeId)) {
      return false;
    }

    visited.add(nodeId);
    recursionStack.add(nodeId);

    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighborId of neighbors) {
      if (dfs(neighborId)) {
        nodesInCycle.add(nodeId);
        return true;
      }
    }

    recursionStack.delete(nodeId);
    return false;
  }

  // Run DFS from each node
  nodes.forEach(node => {
    if (!visited.has(node.id)) {
      dfs(node.id);
    }
  });

  return Array.from(nodesInCycle);
}
