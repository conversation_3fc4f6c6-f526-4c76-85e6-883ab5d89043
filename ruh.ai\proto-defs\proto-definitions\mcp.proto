syntax = "proto3";

package mcp;
import "google/protobuf/field_mask.proto";

service MCPService {
  rpc createMCP (CreateMCPRequest) returns (MCPResponse);
  rpc getMCP (GetMCPRequest) returns (MCPResponse);
  rpc updateMCP (UpdateMCPRequest) returns (MCPResponse);
  rpc deleteMCP (DeleteMCPRequest) returns (DeleteMCPResponse);
  rpc listMCPs (ListMCPsRequest) returns (ListMCPsResponse);
  rpc getMCPsByIds(GetMCPsByIdsRequest) returns (ListMCPsResponse);
  rpc UpdateDeploymentStatus(UpdateDeploymentStatusRequest) returns (MCPResponse);
  // Marketplace related RPCs
  rpc getMarketplaceMCPs(GetMarketplaceMCPsRequest) returns (GetMarketplaceMCPsResponse);
  rpc getMarketplaceMCPDetail(GetMarketplaceMCPDetailRequest) returns (GetMarketplaceMCPDetailResponse);
  rpc ToggleMcpVisibility(ToggleMcpVisibilityRequest) returns (ToggleMcpVisibilityResponse);
 
  // Rating and usage tracking RPCs
  rpc rateMCP(RateMCPRequest) returns (RateMCPResponse);
  rpc useMCP(UseMCPRequest) returns (UseMCPResponse);

  rpc CreateContainer(CreateContainerRequest) returns (CreateContainerResponse);
  rpc StopContainer(StopContainerRequest) returns (StopContainerResponse);
  rpc GetContainerStatus(GetContainerStatusRequest) returns (GetContainerStatusResponse);
  rpc DeleteContainer(DeleteContainerRequest) returns (DeleteContainerResponse);
  rpc UpdateToolOutputSchema(UpdateToolOutputSchemaRequest) returns (MCPResponse);

}

enum MCPDepartment {
    GENERAL = 0;
    SALES = 1;
    MARKETING = 2;
    ENGINEERING = 3;
    FINANCE = 4;
    HR = 5;
}

enum Visibility {
  PRIVATE = 0;
  PUBLIC = 1;
}

enum Status {
  ACTIVE = 0;
  INACTIVE = 1;
}

enum OwnerType {
  USER = 0;
  ENTERPRISE = 1;
  PLATFORM = 2;
}

message Owner {
  string id = 1;
  string email = 2;
  string full_name = 3;
  string fcm_token = 4;
}

enum UrlType{
  SSE = 0;
  HTTP = 1;
}
message MCPUrl {
  string url = 1;
  string type = 2;
}

message EnvKey {
  string key = 1;
  string description = 2;
}

message CreateMCPRequest {
  string name = 1;
  string description = 2;
  Owner owner = 3;
  OwnerType owner_type = 4;
  string git_url = 5;
  MCPDepartment department = 6;
  Visibility visibility = 7;
  repeated string tags = 8;
  Status status = 9;
  repeated MCPUrl urls = 10;
  string git_branch = 11;
  repeated string user_ids = 12;
  string logo = 13;
  string github_access_token = 14;
  repeated EnvKey env_keys = 15 ;
  string mcp_type = 16;
}
message UpdateMCPRequest {
  string id = 1;
  google.protobuf.FieldMask update_mask = 2;

  Owner owner = 3;
  optional string name = 4;
  optional string description = 5;
  optional string github_access_token = 6;
  optional Visibility visibility = 7;
  repeated string user_ids = 8;
  optional MCPDepartment department = 9;
  repeated string tags = 10;
  optional Status status = 11;
  repeated MCPUrl urls = 12;
  optional string git_url = 13;
  optional string logo = 14;
  optional string git_branch = 15;
  repeated EnvKey env_keys =16;
}

message MCP {
  string id = 1;
  string name = 2;
  string description = 3;
  string owner_id = 4;
  repeated string user_ids = 5;
  string owner_type = 6;
  optional string urls = 7;
  optional string git_branch = 8;
  string git_url = 9;
  string visibility = 10;
  repeated string tags = 11;
  string status = 12;
  string department = 13;
  string created_at = 14;
  string updated_at = 15;
  string mcp_tools_config = 16;
  string logo = 17;
  optional bool is_added = 18;
  optional string deployment_status = 19;
  repeated EnvKey env_keys = 20;
  string image_name = 21 ;
}

message MCPResponse {
  bool success = 1;
  string message = 2;
  MCP mcp = 3;
}

message GetMCPRequest {
  string id = 1;
  optional string user_id = 2;
}

message DeleteMCPRequest {
  string id = 1;
  Owner owner = 2;
}

message DeleteMCPResponse {
  bool success = 1;
  string message = 2;
}

message ListMCPsRequest {
  int32 page = 1;
  int32 page_size = 2;
  optional string owner_id = 3;
  optional string department = 4;
  optional Visibility visibility = 5;
  optional Status status = 6;
  optional UrlType url_type = 7;
  repeated string tags = 8;
  optional string deployment_status = 9;
  optional string search = 10;
}

message ListMCPsByUserIdRequest {
  string owner_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

message ListMCPsResponse {
  repeated MCP mcps = 1;
  int32 total = 2;
  int32 page = 3;
  int32 total_pages = 4;
}

message GetMCPsByIdsRequest {
  repeated string ids = 1;
}

// Marketplace related messages

enum MarketplaceItemSortEnum {
  NEWEST = 0;
  OLDEST = 1;
  MOST_POPULAR = 2;
  HIGHEST_RATED = 3;
}

message MarketplaceMCP {
  string id = 1;
  string name = 2;
  string description = 3;
  string logo = 4;
  string mcp_tools_config = 5;
  string visibility = 6;
  string owner_id = 7;
  string owner_type = 8;
  repeated string user_ids = 9;
  string department = 10;
  repeated string tags = 11;
  string status = 12;
  optional string urls = 13;
  string git_url = 14;
  string created_at = 15;
  string updated_at = 16;
  float average_rating = 17;
  int32 use_count = 18;
  string api_documentation = 29;
  repeated string capabilities = 20;
  optional bool is_added = 21;
}

message GetMarketplaceMCPsRequest {
  int32 page = 1;
  int32 page_size = 2;
  optional string search = 3;  // Search term for name or description
  optional MCPDepartment department = 4;  // Department filter
  optional string category = 5;  // Category filter
  repeated string tags = 6;
  optional string sort_by = 7;  // Sort criteria (NEWEST, OLDEST, MOST_POPULAR, HIGHEST_RATED)
  string visibility = 8;
}

message GetMarketplaceMCPsResponse {
  bool success = 1;
  string message = 2;
  repeated MarketplaceMCP mcps = 3;
  int32 total = 4;
  int32 page = 5;
  int32 page_size = 6;
  int32 total_pages = 7;
  bool has_next = 8;
  bool has_prev = 9;
  optional int32 next_page = 10;
  optional int32 prev_page = 11;
}

message GetMarketplaceMCPDetailRequest {
  string id = 1;
  optional string user_id = 2;
}

message GetMarketplaceMCPDetailResponse {
  bool success = 1;
  string message = 2;
  MarketplaceMCP mcp = 3;
}

// Rating and usage tracking messages
message RateMCPRequest {
  string mcp_id = 1;
  string user_id = 2;
  float rating = 3;  // Rating value between 1.0 and 5.0
}

message RateMCPResponse {
  bool success = 1;
  string message = 2;
  float average_rating = 3;  // Updated average rating after this rating is applied
}

message UseMCPRequest {
  string mcp_id = 1;
  string user_id = 2;
}

message UseMCPResponse {
  bool success = 1;
  string message = 2;
  int32 use_count = 3;  // Updated usage count after this use is recorded
}

message UpdateDeploymentStatusRequest {
  string id = 1;
  string deployment_status = 2;
  optional string type = 3;
  optional string image_name = 4;
  optional string error_message = 5;
  optional string url = 6;
}


message EnvVar {
  string key = 1;
  string value = 2;
}

message CreateContainerRequest {
  string mcp_id = 1;
  string user_id = 2;
  string type = 3;
  repeated EnvVar env_vars = 4;
}

message CreateContainerResponse {
  bool success = 1;
  string message = 2;
  string container_id = 3;
}

message StopContainerRequest {
  string container_id = 1;
  string user_id = 2;
  string mcp_id = 3;
}

message StopContainerResponse {
  bool success = 1;
  string message = 2;
}

message UpdateToolOutputSchemaRequest {
  string mcp_id = 1;
  string tool_name = 2;
  string output_schema_json = 3; // The output schema as a JSON string
}

message GetContainerStatusRequest {
  string container_id = 1;
  string user_id = 2;
  string mcp_id = 3;
}

message GetContainerStatusResponse {
  bool success = 1;
  string message = 2;
}

message DeleteContainerRequest {
  string container_id = 1;
}
message DeleteContainerResponse {
  bool success = 1;
  string message = 2;
}

message ToggleMcpVisibilityRequest {
  string mcp_id = 1;
  string user_id = 2;
}

message ToggleMcpVisibilityResponse {
  bool success = 1;
  string message = 2;
}