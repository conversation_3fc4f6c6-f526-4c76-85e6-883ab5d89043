"""
Test cases for improved MCP error handling in the MCP service.

This test file verifies that the MCP service properly handles errors when
retrieving tools from MCP servers and provides user-friendly messages.
"""

import pytest
from app.utils.MCP.fetch_tools import get_mcp_tools, tools_to_json_response


class TestMCPErrorHandling:
    """Test cases for MCP error handling improvements."""

    def test_error_message_generation_success(self):
        """Test error message generation for successful tools retrieval."""
        tools_retrieval_success = True
        tools_error_message = None
        mcp_name = "Test MCP"

        # Simulate the logic from createMCP
        if tools_retrieval_success:
            success_message = f"MCP '{mcp_name}' created successfully with tools configured."
        elif tools_error_message:
            success_message = f"MCP '{mcp_name}' created successfully, but {tools_error_message}. You can update the tools configuration later."
        else:
            success_message = f"MCP '{mcp_name}' created successfully."

        assert success_message == "MCP 'Test MCP' created successfully with tools configured."

    def test_error_message_generation_failure(self):
        """Test error message generation for failed tools retrieval."""
        tools_retrieval_success = False
        tools_error_message = "unable to retrieve tools from MCP server at http://localhost:8080/sse. The server may be unavailable or not responding"
        mcp_name = "Test MCP"

        # Simulate the logic from createMCP
        if tools_retrieval_success:
            success_message = f"MCP '{mcp_name}' created successfully with tools configured."
        elif tools_error_message:
            success_message = f"MCP '{mcp_name}' created successfully, but {tools_error_message}. You can update the tools configuration later."
        else:
            success_message = f"MCP '{mcp_name}' created successfully."

        expected = "MCP 'Test MCP' created successfully, but unable to retrieve tools from MCP server at http://localhost:8080/sse. The server may be unavailable or not responding. You can update the tools configuration later."
        assert success_message == expected

    def test_error_message_generation_exception(self):
        """Test error message generation for tools retrieval exception."""
        tools_retrieval_success = False
        tools_error_message = "error connecting to MCP server at http://localhost:8080/sse: Connection refused"
        mcp_name = "Test MCP"

        # Simulate the logic from createMCP
        if tools_retrieval_success:
            success_message = f"MCP '{mcp_name}' created successfully with tools configured."
        elif tools_error_message:
            success_message = f"MCP '{mcp_name}' created successfully, but {tools_error_message}. You can update the tools configuration later."
        else:
            success_message = f"MCP '{mcp_name}' created successfully."

        expected = "MCP 'Test MCP' created successfully, but error connecting to MCP server at http://localhost:8080/sse: Connection refused. You can update the tools configuration later."
        assert success_message == expected

    def test_update_error_message_generation_success(self):
        """Test error message generation for successful tools update."""
        tools_retrieval_success = True
        tools_error_message = None
        mcp_name = "Test MCP"
        updated_fields = ["config"]

        # Simulate the logic from updateMCP
        base_message = f"MCP '{mcp_name}' updated successfully. Fields updated: {', '.join(updated_fields)}"
        if "config" in updated_fields:
            if tools_retrieval_success:
                success_message = f"{base_message}. Tools configuration updated successfully."
            elif tools_error_message:
                success_message = f"{base_message}, but {tools_error_message}. You can update the tools configuration later."
            else:
                success_message = base_message
        else:
            success_message = base_message

        expected = "MCP 'Test MCP' updated successfully. Fields updated: config. Tools configuration updated successfully."
        assert success_message == expected

    def test_marketplace_is_added_field_logic(self):
        """Test the is_added field logic for marketplace MCPs using UserMcpAssignment table."""
        # Test case 1: User has added the MCP (assignment exists)
        user_id = "user123"
        mcp_id = "mcp456"

        # Simulate existing assignment
        existing_assignment = True  # Mock result from database query
        is_added = existing_assignment is not None and existing_assignment
        assert is_added is True

        # Test case 2: User has not added the MCP (no assignment)
        user_id = "user789"
        mcp_id = "mcp456"

        # Simulate no assignment
        existing_assignment = None  # Mock result from database query
        is_added = existing_assignment is not None
        assert is_added is False

        # Test case 3: No user_id provided
        user_id = None
        mcp_id = "mcp456"
        is_added = False
        if user_id:
            # Would query UserMcpAssignment table here
            pass
        assert is_added is False

        # Test case 4: Empty user_id
        user_id = ""
        mcp_id = "mcp456"
        is_added = False
        if user_id:
            # Would query UserMcpAssignment table here
            pass
        assert is_added is False

    def test_user_mcp_assignment_logic(self):
        """Test the UserMcpAssignment table logic simulation."""
        # Simulate the database query logic
        def check_assignment_exists(user_id, mcp_id):
            # Mock UserMcpAssignment table data
            mock_assignments = [
                {"user_id": "user123", "mcp_id": "mcp456"},
                {"user_id": "user123", "mcp_id": "mcp789"},
                {"user_id": "user456", "mcp_id": "mcp456"},
            ]

            for assignment in mock_assignments:
                if assignment["user_id"] == user_id and assignment["mcp_id"] == mcp_id:
                    return assignment
            return None

        # Test case 1: Assignment exists
        assignment = check_assignment_exists("user123", "mcp456")
        is_added = assignment is not None
        assert is_added is True

        # Test case 2: Assignment doesn't exist
        assignment = check_assignment_exists("user999", "mcp456")
        is_added = assignment is not None
        assert is_added is False

        # Test case 3: User exists but different MCP
        assignment = check_assignment_exists("user123", "mcp999")
        is_added = assignment is not None
        assert is_added is False


def test_get_mcp_tools_with_invalid_url():
    """Test get_mcp_tools function with invalid URL."""
    result = get_mcp_tools("invalid-url")
    assert result is None


def test_get_mcp_tools_with_unreachable_server():
    """Test get_mcp_tools function with unreachable server."""
    result = get_mcp_tools("http://unreachable-server:9999/sse")
    assert result is None


def test_tools_to_json_response_with_none():
    """Test tools_to_json_response function with None input."""
    result = tools_to_json_response(None)
    assert result is None


if __name__ == "__main__":
    pytest.main([__file__])
