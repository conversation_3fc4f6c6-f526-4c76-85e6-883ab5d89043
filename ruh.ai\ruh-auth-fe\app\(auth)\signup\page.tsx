"use client";

import { SocialSignIn } from "@/app/(auth)/_components/SocialSignIn";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { SignupForm } from "../_components/SignupForm";
import { Logo } from "@/app/shared/Logo";
import { loginRoute, onboardingRoute } from "@/app/shared/routes";
import { useEffect, useState } from "react";
import { ConfirmationScreen } from "../_components/ConfirmationScreen";
import { useRouter } from "next/navigation";
import { isAuthenticated } from "@/services/helper";
import { LoadingScreen } from "@/components/shared/LoadingScreen";
const SignupPage = () => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      const isUserAuthenticated = await isAuthenticated();
      if (isUserAuthenticated) {
        router.push(onboardingRoute);
      }
      setLoading(false);
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-6 ">
      {showConfirmation ? (
        <ConfirmationScreen
          title="Verify Your Email"
          message="We have sent a link to your registered mail ID for verification."
          description="Kindly check your inbox and click on it to verify and activate your account!
(if you don't find the link there, check your spam folder)"
        />
      ) : (
        <div className="w-fit border-2 border-brand-stroke  p-8 md:p-12 lg:p-16 flex flex-col items-center justify-center gap-6  bg-brand-overlay border-r border-gray-200/90 dark:border-brand-card rounded-xl">
          <div>
            <Logo />
          </div>

          <div className="max-w-md w-full flex flex-col justify-center gap-6">
            <div className="flex flex-col  justify-center gap-16">
              <div className="">
                <h1 className=" text-2xl font-bold mb-2 text-brand-primary-font font-primary text-center">
                  Sign Up & Build Your AI Workforce
                </h1>
                <p className="text-brand-secondary-font text-sm text-center ">
                  Join AI Workforce — Automate Your Tasks with Smart AI Agents
                </p>
              </div>

              <SignupForm setShowConfirmation={setShowConfirmation} />
            </div>

            <div className="mt-6 text-center text-sm font-semibold">
              You already have an account?{" "}
              <Link
                href={loginRoute}
                className="text-brand-primary hover:text-brand-light font-medium"
              >
                {" "}
                Login
              </Link>
            </div>

            {/* Separator with text overlay */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  or
                </span>
              </div>
            </div>

            <SocialSignIn />
          </div>
        </div>
      )}
    </div>
  );
};

export default SignupPage;
