# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google_drive.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'google_drive.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12google_drive.proto\x12\x0cgoogle_drive\"&\n\nFolderInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"1\n\x16\x44isconnectDriveRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\";\n\x17\x44isconnectDriveResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"O\n\x10SyncDriveRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x11\n\tfull_sync\x18\x03 \x01(\x08\"x\n\x11SyncDriveResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x14\n\x0c\x66iles_synced\x18\x03 \x01(\x05\x12\x16\n\x0e\x66olders_synced\x18\x04 \x01(\x05\x12\x13\n\x0bsync_status\x18\x05 \x01(\t\"W\n\x10ListFilesRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\tfolder_id\x18\x02 \x01(\t\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xe6\x01\n\x0e\x44riveFileModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tmime_type\x18\x03 \x01(\t\x12\x15\n\rweb_view_link\x18\x04 \x01(\t\x12\x14\n\x0c\x63reated_time\x18\x05 \x01(\t\x12\x15\n\rmodified_time\x18\x06 \x01(\t\x12\x18\n\x10parent_folder_id\x18\x07 \x01(\t\x12\x0c\n\x04size\x18\x08 \x01(\x03\x12\x13\n\x0bshared_with\x18\t \x03(\t\x12\x11\n\tis_folder\x18\n \x01(\x08\x12\x13\n\x0b\x63hild_count\x18\x0b \x01(\x05\"\x98\x01\n\x11ListFilesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x05\x66iles\x18\x03 \x03(\x0b\x32\x1c.google_drive.DriveFileModel\x12\x13\n\x0btotal_count\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x11\n\tpage_size\x18\x06 \x01(\x05\"9\n\x15GetFileDetailsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0f\n\x07\x66ile_id\x18\x02 \x01(\t\"f\n\x16GetFileDetailsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x04\x66ile\x18\x03 \x01(\x0b\x32\x1c.google_drive.DriveFileModel\"B\n\x14GetFolderByIdRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x11\n\tfolder_id\x18\x02 \x01(\t\"\x97\x01\n\x15GetFolderByIdResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x06\x66older\x18\x03 \x01(\x0b\x32\x1c.google_drive.DriveFileModel\x12.\n\x08\x63hildren\x18\x04 \x03(\x0b\x32\x1c.google_drive.DriveFileModel\"E\n\x16SyncFolderByIdsRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x12\n\nfolder_ids\x18\x02 \x03(\t\"\xb0\x01\n\x17SyncFolderByIdsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x14\n\x0c\x66iles_synced\x18\x03 \x01(\x05\x12\x16\n\x0e\x66olders_synced\x18\x04 \x01(\x05\x12\x13\n\x0bsync_status\x18\x05 \x01(\t\x12\x30\n\x0esynced_folders\x18\x06 \x03(\x0b\x32\x18.google_drive.FolderInfo\":\n\x16\x43heckFileAccessRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0f\n\x07\x66ile_id\x18\x02 \x01(\t\"O\n\x17\x43heckFileAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nhas_access\x18\x03 \x01(\x08\"\x90\x01\n\x1dSearchSimilarDocumentsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x12\n\nquery_text\x18\x02 \x01(\t\x12\r\n\x05top_k\x18\x03 \x01(\x05\x12\x10\n\x08\x61gent_id\x18\x04 \x01(\t\x12\x17\n\x0forganisation_id\x18\x05 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x06 \x03(\t\"\xc3\x01\n\x10SearchResultItem\x12\x0f\n\x07\x66ile_id\x18\x01 \x01(\t\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tmime_type\x18\x03 \x01(\t\x12\x15\n\rweb_view_link\x18\x04 \x01(\t\x12\x14\n\x0c\x63reated_time\x18\x05 \x01(\t\x12\x15\n\rmodified_time\x18\x06 \x01(\t\x12\r\n\x05score\x18\x07 \x01(\x02\x12\x11\n\tvector_id\x18\x08 \x01(\t\x12\x12\n\nchunk_text\x18\t \x01(\t\"s\n\x1eSearchSimilarDocumentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12/\n\x07results\x18\x03 \x03(\x0b\x32\x1e.google_drive.SearchResultItem\"\x96\x01\n\"BatchSearchSimilarDocumentsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x13\n\x0bquery_texts\x18\x02 \x03(\t\x12\r\n\x05top_k\x18\x03 \x01(\x05\x12\x10\n\x08\x61gent_id\x18\x04 \x01(\t\x12\x17\n\x0forganisation_id\x18\x05 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x06 \x03(\t\"z\n#BatchSearchSimilarDocumentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x31\n\rquery_results\x18\x03 \x03(\x0b\x32\x1a.google_drive.QueryResults\"S\n\x0cQueryResults\x12\x12\n\nquery_text\x18\x01 \x01(\t\x12/\n\x07results\x18\x02 \x03(\x0b\x32\x1e.google_drive.SearchResultItem\"e\n\x14SyncFileByUrlRequest\x12\x11\n\tdrive_url\x18\x01 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x17\n\x0forganisation_id\x18\x04 \x01(\t\"r\n\x15SyncFileByUrlResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07\x66ile_id\x18\x03 \x01(\t\x12\x11\n\tfile_name\x18\x04 \x01(\t\x12\x13\n\x0bsync_status\x18\x05 \x01(\t\"5\n\x1aListTopLevelFoldersRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"j\n\x1bListTopLevelFoldersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x18.google_drive.FolderInfo2\xdd\x08\n\x12GoogleDriveService\x12`\n\x0f\x64isconnectDrive\x12$.google_drive.DisconnectDriveRequest\x1a%.google_drive.DisconnectDriveResponse\"\x00\x12N\n\tsyncDrive\x12\x1e.google_drive.SyncDriveRequest\x1a\x1f.google_drive.SyncDriveResponse\"\x00\x12N\n\tlistFiles\x12\x1e.google_drive.ListFilesRequest\x1a\x1f.google_drive.ListFilesResponse\"\x00\x12]\n\x0egetFileDetails\x12#.google_drive.GetFileDetailsRequest\x1a$.google_drive.GetFileDetailsResponse\"\x00\x12Z\n\rgetFolderById\x12\".google_drive.GetFolderByIdRequest\x1a#.google_drive.GetFolderByIdResponse\"\x00\x12`\n\x0fsyncFolderByIds\x12$.google_drive.SyncFolderByIdsRequest\x1a%.google_drive.SyncFolderByIdsResponse\"\x00\x12`\n\x0f\x63heckFileAccess\x12$.google_drive.CheckFileAccessRequest\x1a%.google_drive.CheckFileAccessResponse\"\x00\x12u\n\x16searchSimilarDocuments\x12+.google_drive.SearchSimilarDocumentsRequest\x1a,.google_drive.SearchSimilarDocumentsResponse\"\x00\x12\x84\x01\n\x1b\x62\x61tchSearchSimilarDocuments\x12\x30.google_drive.BatchSearchSimilarDocumentsRequest\x1a\x31.google_drive.BatchSearchSimilarDocumentsResponse\"\x00\x12Z\n\rsyncFileByUrl\x12\".google_drive.SyncFileByUrlRequest\x1a#.google_drive.SyncFileByUrlResponse\"\x00\x12l\n\x13listTopLevelFolders\x12(.google_drive.ListTopLevelFoldersRequest\x1a).google_drive.ListTopLevelFoldersResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'google_drive_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_FOLDERINFO']._serialized_start=36
  _globals['_FOLDERINFO']._serialized_end=74
  _globals['_DISCONNECTDRIVEREQUEST']._serialized_start=76
  _globals['_DISCONNECTDRIVEREQUEST']._serialized_end=125
  _globals['_DISCONNECTDRIVERESPONSE']._serialized_start=127
  _globals['_DISCONNECTDRIVERESPONSE']._serialized_end=186
  _globals['_SYNCDRIVEREQUEST']._serialized_start=188
  _globals['_SYNCDRIVEREQUEST']._serialized_end=267
  _globals['_SYNCDRIVERESPONSE']._serialized_start=269
  _globals['_SYNCDRIVERESPONSE']._serialized_end=389
  _globals['_LISTFILESREQUEST']._serialized_start=391
  _globals['_LISTFILESREQUEST']._serialized_end=478
  _globals['_DRIVEFILEMODEL']._serialized_start=481
  _globals['_DRIVEFILEMODEL']._serialized_end=711
  _globals['_LISTFILESRESPONSE']._serialized_start=714
  _globals['_LISTFILESRESPONSE']._serialized_end=866
  _globals['_GETFILEDETAILSREQUEST']._serialized_start=868
  _globals['_GETFILEDETAILSREQUEST']._serialized_end=925
  _globals['_GETFILEDETAILSRESPONSE']._serialized_start=927
  _globals['_GETFILEDETAILSRESPONSE']._serialized_end=1029
  _globals['_GETFOLDERBYIDREQUEST']._serialized_start=1031
  _globals['_GETFOLDERBYIDREQUEST']._serialized_end=1097
  _globals['_GETFOLDERBYIDRESPONSE']._serialized_start=1100
  _globals['_GETFOLDERBYIDRESPONSE']._serialized_end=1251
  _globals['_SYNCFOLDERBYIDSREQUEST']._serialized_start=1253
  _globals['_SYNCFOLDERBYIDSREQUEST']._serialized_end=1322
  _globals['_SYNCFOLDERBYIDSRESPONSE']._serialized_start=1325
  _globals['_SYNCFOLDERBYIDSRESPONSE']._serialized_end=1501
  _globals['_CHECKFILEACCESSREQUEST']._serialized_start=1503
  _globals['_CHECKFILEACCESSREQUEST']._serialized_end=1561
  _globals['_CHECKFILEACCESSRESPONSE']._serialized_start=1563
  _globals['_CHECKFILEACCESSRESPONSE']._serialized_end=1642
  _globals['_SEARCHSIMILARDOCUMENTSREQUEST']._serialized_start=1645
  _globals['_SEARCHSIMILARDOCUMENTSREQUEST']._serialized_end=1789
  _globals['_SEARCHRESULTITEM']._serialized_start=1792
  _globals['_SEARCHRESULTITEM']._serialized_end=1987
  _globals['_SEARCHSIMILARDOCUMENTSRESPONSE']._serialized_start=1989
  _globals['_SEARCHSIMILARDOCUMENTSRESPONSE']._serialized_end=2104
  _globals['_BATCHSEARCHSIMILARDOCUMENTSREQUEST']._serialized_start=2107
  _globals['_BATCHSEARCHSIMILARDOCUMENTSREQUEST']._serialized_end=2257
  _globals['_BATCHSEARCHSIMILARDOCUMENTSRESPONSE']._serialized_start=2259
  _globals['_BATCHSEARCHSIMILARDOCUMENTSRESPONSE']._serialized_end=2381
  _globals['_QUERYRESULTS']._serialized_start=2383
  _globals['_QUERYRESULTS']._serialized_end=2466
  _globals['_SYNCFILEBYURLREQUEST']._serialized_start=2468
  _globals['_SYNCFILEBYURLREQUEST']._serialized_end=2569
  _globals['_SYNCFILEBYURLRESPONSE']._serialized_start=2571
  _globals['_SYNCFILEBYURLRESPONSE']._serialized_end=2685
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_start=2687
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_end=2740
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_start=2742
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_end=2848
  _globals['_GOOGLEDRIVESERVICE']._serialized_start=2851
  _globals['_GOOGLEDRIVESERVICE']._serialized_end=3968
# @@protoc_insertion_point(module_scope)
