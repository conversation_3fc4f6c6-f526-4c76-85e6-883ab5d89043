import { UpdatePasswordForm } from "./_components/UpdatePasswordForm";

export default async function UpdatePasswordPage({
  params,
}: {
  params: { token: string };
}) {
  const { token } = params;

  return (
    <div className="min-h-screen flex items-center justify-center ">
      {/* Left Side - Reset Password Form */}
      <div className="min-w-[400px] sm:min-w-[500px] border-2 border-brand-stroke  p-8 md:p-12 lg:p-16 flex flex-col items-center justify-center gap-6 bg-brand-overlay border-r border-gray-200/90 dark:border-brand-card rounded-xl ">
        <div className="max-w-md w-full flex flex-col justify-center gap-6">
          <div className="flex flex-col justify-center gap-16">
            <div>
              <h1 className="text-2xl font-bold mb-2 text-brand-primary-font text-center font-primary">
                RESET PASSWORD
              </h1>
            </div>

            <UpdatePasswordForm token={token} />
          </div>
        </div>
      </div>
    </div>
  );
}
