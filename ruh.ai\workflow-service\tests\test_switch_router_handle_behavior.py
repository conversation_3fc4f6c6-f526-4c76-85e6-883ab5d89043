"""
Test cases for Switch Router component input handle behavior.

This module tests the specific scenarios for input handle visibility
based on configuration changes and source selection.
"""

import pytest
from unittest.mock import Mock

from app.components.control_flow.conditionalNode import ConditionalNode


class TestSwitchRouterHandleBehavior:
    """Test the Switch Router component input handle behavior."""

    def setup_method(self):
        """Set up test fixtures."""
        self.node = ConditionalNode()

    def get_visible_input_handles(self, config):
        """
        Simulate frontend handle filtering logic to get visible input handles.
        
        Args:
            config: Configuration dictionary simulating node.data.config
            
        Returns:
            List of visible input handle names
        """
        visible_handles = []
        
        # Always include primary input data handle
        primary_handle = next(
            (inp for inp in self.node.inputs if inp.name == "primary_input_data"),
            None
        )
        if primary_handle and primary_handle.is_handle:
            visible_handles.append("primary_input_data")
        
        # Check condition input handles
        for inp in self.node.inputs:
            if inp.name.endswith("_input_handle") and inp.name.startswith("condition_"):
                # Extract condition number
                match = inp.name.replace("condition_", "").replace("_input_handle", "")
                try:
                    condition_index = int(match)
                except ValueError:
                    continue
                
                # Apply frontend filtering logic
                num_additional_conditions = int(config.get("num_additional_conditions", 0))
                total_conditions = 2 + num_additional_conditions
                
                # Check if condition is within total conditions
                if condition_index <= total_conditions:
                    # Check source setting (default to "node_output" if not set)
                    source_value = config.get(f"condition_{condition_index}_source", "node_output")
                    if source_value == "node_output":
                        visible_handles.append(inp.name)
        
        return visible_handles

    def test_initial_state_default_configuration(self):
        """Test Initial State: Default configuration with 0 additional conditions."""
        config = {
            "num_additional_conditions": 0,
            # Sources default to "node_output" if not specified
        }
        
        visible_handles = self.get_visible_input_handles(config)
        
        # Should display exactly 3 input handles
        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle", 
            "condition_2_input_handle"
        ]
        
        assert len(visible_handles) == 3, f"Expected 3 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_adding_additional_conditions(self):
        """Test Adding Additional Conditions: 2 additional conditions (4 total)."""
        config = {
            "num_additional_conditions": 2,
            # All sources default to "node_output"
        }
        
        visible_handles = self.get_visible_input_handles(config)
        
        # Should display exactly 5 input handles total
        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle", 
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]
        
        assert len(visible_handles) == 5, f"Expected 5 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_source_selection_behavior_single_change(self):
        """Test Source Selection: Change one condition from Node Output to Global Context."""
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "global_context",  # Changed to global_context
            "condition_3_source": "node_output", 
            "condition_4_source": "node_output"
        }
        
        visible_handles = self.get_visible_input_handles(config)
        
        # Condition 2 handle should disappear, others should remain
        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            # condition_2_input_handle should be missing
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]
        
        assert len(visible_handles) == 4, f"Expected 4 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"
        assert "condition_2_input_handle" not in visible_handles, "condition_2_input_handle should be hidden when source is global_context"

    def test_source_selection_behavior_multiple_changes(self):
        """Test Source Selection: Change multiple conditions to Global Context."""
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "global_context",  # Changed to global_context
            "condition_3_source": "global_context",  # Changed to global_context
            "condition_4_source": "node_output"
        }
        
        visible_handles = self.get_visible_input_handles(config)
        
        # Only condition 1 and 4 handles should be visible
        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            # condition_2_input_handle should be missing
            # condition_3_input_handle should be missing
            "condition_4_input_handle"
        ]
        
        assert len(visible_handles) == 3, f"Expected 3 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"
        assert "condition_2_input_handle" not in visible_handles
        assert "condition_3_input_handle" not in visible_handles

    def test_source_selection_behavior_change_back(self):
        """Test Source Selection: Change back from Global Context to Node Output."""
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "node_output",  # Changed back to node_output
            "condition_3_source": "node_output",
            "condition_4_source": "node_output"
        }
        
        visible_handles = self.get_visible_input_handles(config)
        
        # All handles should be visible again
        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle",  # Should reappear
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]
        
        assert len(visible_handles) == 5, f"Expected 5 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_edge_case_all_global_context(self):
        """Test Edge Case: All conditions set to Global Context."""
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "global_context",
            "condition_2_source": "global_context",
            "condition_3_source": "global_context",
            "condition_4_source": "global_context"
        }
        
        visible_handles = self.get_visible_input_handles(config)
        
        # Only primary input data handle should be visible
        expected_handles = [
            "primary_input_data"
        ]
        
        assert len(visible_handles) == 1, f"Expected 1 handle, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"

    def test_dynamic_condition_count_changes(self):
        """Test Dynamic Condition Count: Changing from 0 to 2 to 0 additional conditions."""
        # Start with 0 additional conditions
        config_0 = {"num_additional_conditions": 0}
        visible_handles_0 = self.get_visible_input_handles(config_0)
        
        expected_handles_0 = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle"
        ]
        assert set(visible_handles_0) == set(expected_handles_0)
        
        # Change to 2 additional conditions
        config_2 = {"num_additional_conditions": 2}
        visible_handles_2 = self.get_visible_input_handles(config_2)
        
        expected_handles_2 = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle",
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]
        assert set(visible_handles_2) == set(expected_handles_2)
        
        # Change back to 0 additional conditions
        config_back_0 = {"num_additional_conditions": 0}
        visible_handles_back_0 = self.get_visible_input_handles(config_back_0)
        
        assert set(visible_handles_back_0) == set(expected_handles_0)

    def test_maximum_conditions(self):
        """Test Maximum Conditions: 8 additional conditions (10 total)."""
        config = {"num_additional_conditions": 8}
        visible_handles = self.get_visible_input_handles(config)
        
        # Should have 11 handles total (1 primary + 10 conditions)
        expected_handles = ["primary_input_data"] + [
            f"condition_{i}_input_handle" for i in range(1, 11)
        ]
        
        assert len(visible_handles) == 11, f"Expected 11 handles, got {len(visible_handles)}: {visible_handles}"
        assert set(visible_handles) == set(expected_handles), f"Expected {expected_handles}, got {visible_handles}"


if __name__ == "__main__":
    pytest.main([__file__])
