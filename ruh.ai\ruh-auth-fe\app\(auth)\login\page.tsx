"use client";

import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { LoginForm } from "@/app/(auth)/_components/LoginForm";
import { Logo } from "@/app/shared/Logo";
import { onboardingRoute, signupRoute } from "@/app/shared/routes";
import { SocialSignIn } from "../_components/SocialSignIn";
import { useEffect, useState } from "react";
import { ConfirmationScreen } from "../_components/ConfirmationScreen";
import { isAuthenticated } from "@/services/helper";
import { useRouter } from "next/navigation";
import { LoadingScreen } from "@/components/shared/LoadingScreen";
const LoginPage = () => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      const isUserAuthenticated = await isAuthenticated();
      if (isUserAuthenticated) {
        router.push(onboardingRoute);
      }
      setLoading(false);
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center ">
      {showConfirmation ? (
        <ConfirmationScreen
          title="Reset Password"
          message="We have sent a link to your email to reset your password."
          description="Kindly check your inbox. (and if you don't find it there, kindly check your spam folder)"
        />
      ) : (
        <div className="lg:border-2 border-brand-stroke w-full lg:w-fit  p-8 md:p-12 lg:p-16 flex flex-col items-center justify-center gap-6 bg-brand-overlay border-r border-gray-200/90 dark:border-brand-card rounded-xl ">
          <div>
            <Logo />
          </div>

          <div className="max-w-md w-full flex flex-col justify-center gap-6">
            <div className="flex flex-col  justify-center gap-16">
              <div>
                <h1 className=" text-2xl font-bold mb-2 text-brand-primary-font text-center font-primary">
                  Log In & Build Your AI Workforce
                </h1>
                <p className="text-brand-secondary-font text-sm text-center ">
                  Join AI Workforce — Automate Your Tasks with Smart AI Agents
                </p>
              </div>

              <LoginForm setShowConfirmation={setShowConfirmation} />
            </div>

            <div className="mt-6 text-center text-sm font-semibold">
              Don&apos;t have an account?
              <Link
                href={signupRoute}
                className="text-brand-primary hover:text-brand-light font-medium"
              >
                {" "}
                Sign Up
              </Link>
            </div>

            {/* Separator with text overlay */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator />
              </div>
              <div className="relative flex justify-center text-xs ">
                <span className="bg-background px-2 text-muted-foreground">
                  or
                </span>
              </div>
            </div>

            <SocialSignIn />
          </div>
        </div>
      )}
    </div>
  );
};

export default LoginPage;
