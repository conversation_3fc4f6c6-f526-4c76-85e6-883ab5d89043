"""
Test conditional node schema generation to verify architectural compliance.

This test ensures that conditional nodes:
1. Do NOT appear as separate nodes in the transition schema's 'nodes' array
2. DO generate conditional_routing logic in the previous node's transition
3. Follow the established architecture where orchestration engine handles conditional routing
"""

import pytest
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    is_conditional_node
)


class TestConditionalNodeSchemaGeneration:
    """Test conditional node schema generation compliance."""

    def test_conditional_node_excluded_from_nodes_array(self):
        """Test that conditional nodes are excluded from transition schema nodes array."""
        # Sample workflow with a conditional node
        workflow_data = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "type": "StartNode",
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "text-1",
                    "data": {
                        "type": "TextComponent",
                        "definition": {"name": "TextComponent"}
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "type": "ConditionalNode",
                        "definition": {
                            "name": "ConditionalNode",
                            "num_conditions": 2,
                            "condition_1_source": "node_output",
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "test"
                        }
                    }
                },
                {
                    "id": "output-1",
                    "data": {
                        "type": "OutputNode",
                        "originalType": "OutputNode",
                        "definition": {"name": "OutputNode"}
                    }
                }
            ],
            "edges": [
                {"id": "e1", "source": "start-1", "target": "text-1"},
                {"id": "e2", "source": "text-1", "target": "conditional-1"},
                {"id": "e3", "source": "conditional-1", "target": "output-1"}
            ],
            "mcp_configs": []
        }

        # Convert to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)

        # Verify conditional node is NOT in nodes array
        node_ids = [node["id"] for node in transition_schema["nodes"]]
        assert "ConditionalNode" not in node_ids, "Conditional nodes should not appear in transition schema nodes array"

        # Verify other nodes are present
        assert "TextComponent" in node_ids, "Regular components should appear in nodes array"

    def test_conditional_routing_generated_in_transition(self):
        """Test that conditional routing logic is generated in the previous node's transition."""
        workflow_data = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "type": "StartNode",
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "text-1",
                    "data": {
                        "type": "TextComponent",
                        "definition": {"name": "TextComponent"}
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "type": "ConditionalNode",
                        "definition": {
                            "name": "ConditionalNode",
                            "num_conditions": 2,
                            "condition_1_source": "node_output",
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "success"
                        }
                    }
                },
                {
                    "id": "output-1",
                    "data": {
                        "type": "OutputNode",
                        "originalType": "OutputNode",
                        "definition": {"name": "OutputNode"}
                    }
                }
            ],
            "edges": [
                {"id": "e1", "source": "start-1", "target": "text-1"},
                {"id": "e2", "source": "text-1", "target": "conditional-1"},
                {"id": "e3", "source": "conditional-1", "target": "output-1"}
            ],
            "mcp_configs": []
        }

        # Convert to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)

        # Find the text component's transition (should have conditional routing)
        text_transition = None
        for transition in transition_schema["transitions"]:
            if transition["id"] == "transition-text-1":
                text_transition = transition
                break

        assert text_transition is not None, "Text component transition should exist"
        assert "conditional_routing" in text_transition, "Text component transition should have conditional routing"
        assert "cases" in text_transition["conditional_routing"], "Conditional routing should have cases"

    def test_is_conditional_node_detection(self):
        """Test the is_conditional_node function correctly identifies conditional nodes."""
        # Test conditional node detection
        conditional_node = {
            "data": {
                "type": "ConditionalNode"
            }
        }
        assert is_conditional_node(conditional_node) == True

        # Test non-conditional node
        regular_node = {
            "data": {
                "type": "TextComponent"
            }
        }
        assert is_conditional_node(regular_node) == False

        # Test node with "Conditional" in type name
        conditional_variant = {
            "data": {
                "type": "SomeConditionalComponent"
            }
        }
        assert is_conditional_node(conditional_variant) == True

    def test_multiple_conditional_nodes_excluded(self):
        """Test that multiple conditional nodes are all excluded from nodes array."""
        workflow_data = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "type": "StartNode",
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "type": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"}
                    }
                },
                {
                    "id": "conditional-2",
                    "data": {
                        "type": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"}
                    }
                },
                {
                    "id": "text-1",
                    "data": {
                        "type": "TextComponent",
                        "definition": {"name": "TextComponent"}
                    }
                }
            ],
            "edges": [],
            "mcp_configs": []
        }

        # Convert to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)

        # Verify no conditional nodes in nodes array
        node_ids = [node["id"] for node in transition_schema["nodes"]]
        conditional_nodes_in_schema = [node_id for node_id in node_ids if "Conditional" in node_id]

        assert len(conditional_nodes_in_schema) == 0, f"No conditional nodes should appear in schema, found: {conditional_nodes_in_schema}"
        assert "TextComponent" in node_ids, "Regular components should still appear"

    def test_no_separate_conditional_transitions_created(self):
        """CRITICAL TEST: Verify conditional nodes do NOT create separate transitions."""
        workflow_data = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "type": "StartNode",
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "text-1",
                    "data": {
                        "type": "TextComponent",
                        "definition": {"name": "TextComponent"}
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "type": "ConditionalNode",
                        "definition": {
                            "name": "ConditionalNode",
                            "num_conditions": 2,
                            "condition_1_source": "node_output",
                            "condition_1_operator": "equals",
                            "condition_1_expected_value": "success"
                        }
                    }
                },
                {
                    "id": "output-1",
                    "data": {
                        "type": "OutputNode",
                        "originalType": "OutputNode",
                        "definition": {"name": "OutputNode"}
                    }
                },
                {
                    "id": "output-2",
                    "data": {
                        "type": "OutputNode",
                        "originalType": "OutputNode",
                        "definition": {"name": "OutputNode"}
                    }
                }
            ],
            "edges": [
                {"id": "e1", "source": "start-1", "target": "text-1"},
                {"id": "e2", "source": "text-1", "target": "conditional-1"},
                {"id": "e3", "source": "conditional-1", "target": "output-1"},
                {"id": "e4", "source": "conditional-1", "target": "output-2"}
            ],
            "mcp_configs": []
        }

        # Convert to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)

        # CRITICAL CHECK: No transition should exist with conditional node ID
        transition_ids = [t["id"] for t in transition_schema["transitions"]]
        conditional_transitions = [tid for tid in transition_ids if "conditional-1" in tid]

        assert len(conditional_transitions) == 0, f"❌ CRITICAL: Conditional nodes should NOT create separate transitions. Found: {conditional_transitions}"

        # Verify expected transitions exist
        expected_transitions = ["transition-text-1", "transition-output-1", "transition-output-2"]
        for expected in expected_transitions:
            assert expected in transition_ids, f"Expected transition {expected} should exist"

        # Verify text-1 transition has conditional routing
        text_transition = next((t for t in transition_schema["transitions"] if t["id"] == "transition-text-1"), None)
        assert text_transition is not None, "Text transition should exist"
        assert "conditional_routing" in text_transition, "Text transition should have conditional routing"

    def test_conditional_node_not_processed_as_regular_node(self):
        """Test that conditional nodes are completely skipped in regular node processing."""
        workflow_data = {
            "nodes": [
                {
                    "id": "start-1",
                    "data": {
                        "type": "StartNode",
                        "originalType": "StartNode",
                        "definition": {"name": "StartNode"}
                    }
                },
                {
                    "id": "conditional-1",
                    "data": {
                        "type": "ConditionalNode",
                        "definition": {"name": "ConditionalNode"}
                    }
                }
            ],
            "edges": [],
            "mcp_configs": []
        }

        # Convert to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)

        # Should have NO transitions since conditional node doesn't create transitions
        # and start node is excluded
        assert len(transition_schema["transitions"]) == 0, "Conditional-only workflow should create no transitions"
        assert len(transition_schema["nodes"]) == 0, "Conditional-only workflow should create no nodes"


if __name__ == "__main__":
    pytest.main([__file__])
