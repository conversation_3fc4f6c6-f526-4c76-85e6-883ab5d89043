# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import provider_pb2 as provider__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in provider_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ProviderServiceStub(object):
    """Provider Service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateProvider = channel.unary_unary(
                '/provider.ProviderService/CreateProvider',
                request_serializer=provider__pb2.CreateProviderRequest.SerializeToString,
                response_deserializer=provider__pb2.ProviderResponse.FromString,
                _registered_method=True)
        self.GetProvider = channel.unary_unary(
                '/provider.ProviderService/GetProvider',
                request_serializer=provider__pb2.GetByIdRequest.SerializeToString,
                response_deserializer=provider__pb2.ProviderResponse.FromString,
                _registered_method=True)
        self.UpdateProvider = channel.unary_unary(
                '/provider.ProviderService/UpdateProvider',
                request_serializer=provider__pb2.UpdateProviderRequest.SerializeToString,
                response_deserializer=provider__pb2.ProviderResponse.FromString,
                _registered_method=True)
        self.DeleteProvider = channel.unary_unary(
                '/provider.ProviderService/DeleteProvider',
                request_serializer=provider__pb2.DeleteRequest.SerializeToString,
                response_deserializer=provider__pb2.DeleteResponse.FromString,
                _registered_method=True)
        self.ListProviders = channel.unary_unary(
                '/provider.ProviderService/ListProviders',
                request_serializer=provider__pb2.ListRequest.SerializeToString,
                response_deserializer=provider__pb2.ListProvidersResponse.FromString,
                _registered_method=True)


class ProviderServiceServicer(object):
    """Provider Service
    """

    def CreateProvider(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProvider(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateProvider(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProvider(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListProviders(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ProviderServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateProvider': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateProvider,
                    request_deserializer=provider__pb2.CreateProviderRequest.FromString,
                    response_serializer=provider__pb2.ProviderResponse.SerializeToString,
            ),
            'GetProvider': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProvider,
                    request_deserializer=provider__pb2.GetByIdRequest.FromString,
                    response_serializer=provider__pb2.ProviderResponse.SerializeToString,
            ),
            'UpdateProvider': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateProvider,
                    request_deserializer=provider__pb2.UpdateProviderRequest.FromString,
                    response_serializer=provider__pb2.ProviderResponse.SerializeToString,
            ),
            'DeleteProvider': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProvider,
                    request_deserializer=provider__pb2.DeleteRequest.FromString,
                    response_serializer=provider__pb2.DeleteResponse.SerializeToString,
            ),
            'ListProviders': grpc.unary_unary_rpc_method_handler(
                    servicer.ListProviders,
                    request_deserializer=provider__pb2.ListRequest.FromString,
                    response_serializer=provider__pb2.ListProvidersResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'provider.ProviderService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('provider.ProviderService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ProviderService(object):
    """Provider Service
    """

    @staticmethod
    def CreateProvider(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ProviderService/CreateProvider',
            provider__pb2.CreateProviderRequest.SerializeToString,
            provider__pb2.ProviderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetProvider(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ProviderService/GetProvider',
            provider__pb2.GetByIdRequest.SerializeToString,
            provider__pb2.ProviderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateProvider(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ProviderService/UpdateProvider',
            provider__pb2.UpdateProviderRequest.SerializeToString,
            provider__pb2.ProviderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteProvider(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ProviderService/DeleteProvider',
            provider__pb2.DeleteRequest.SerializeToString,
            provider__pb2.DeleteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListProviders(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ProviderService/ListProviders',
            provider__pb2.ListRequest.SerializeToString,
            provider__pb2.ListProvidersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class ModelServiceStub(object):
    """Model Service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateModel = channel.unary_unary(
                '/provider.ModelService/CreateModel',
                request_serializer=provider__pb2.CreateModelRequest.SerializeToString,
                response_deserializer=provider__pb2.ModelResponse.FromString,
                _registered_method=True)
        self.GetModel = channel.unary_unary(
                '/provider.ModelService/GetModel',
                request_serializer=provider__pb2.GetByIdRequest.SerializeToString,
                response_deserializer=provider__pb2.ModelResponse.FromString,
                _registered_method=True)
        self.UpdateModel = channel.unary_unary(
                '/provider.ModelService/UpdateModel',
                request_serializer=provider__pb2.UpdateModelRequest.SerializeToString,
                response_deserializer=provider__pb2.ModelResponse.FromString,
                _registered_method=True)
        self.DeleteModel = channel.unary_unary(
                '/provider.ModelService/DeleteModel',
                request_serializer=provider__pb2.DeleteRequest.SerializeToString,
                response_deserializer=provider__pb2.DeleteResponse.FromString,
                _registered_method=True)
        self.ListModels = channel.unary_unary(
                '/provider.ModelService/ListModels',
                request_serializer=provider__pb2.ListRequest.SerializeToString,
                response_deserializer=provider__pb2.ListModelsResponse.FromString,
                _registered_method=True)
        self.ListModelsByProvider = channel.unary_unary(
                '/provider.ModelService/ListModelsByProvider',
                request_serializer=provider__pb2.GetByProviderRequest.SerializeToString,
                response_deserializer=provider__pb2.ListModelsResponse.FromString,
                _registered_method=True)


class ModelServiceServicer(object):
    """Model Service
    """

    def CreateModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListModels(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListModelsByProvider(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModelServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateModel': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateModel,
                    request_deserializer=provider__pb2.CreateModelRequest.FromString,
                    response_serializer=provider__pb2.ModelResponse.SerializeToString,
            ),
            'GetModel': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModel,
                    request_deserializer=provider__pb2.GetByIdRequest.FromString,
                    response_serializer=provider__pb2.ModelResponse.SerializeToString,
            ),
            'UpdateModel': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateModel,
                    request_deserializer=provider__pb2.UpdateModelRequest.FromString,
                    response_serializer=provider__pb2.ModelResponse.SerializeToString,
            ),
            'DeleteModel': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteModel,
                    request_deserializer=provider__pb2.DeleteRequest.FromString,
                    response_serializer=provider__pb2.DeleteResponse.SerializeToString,
            ),
            'ListModels': grpc.unary_unary_rpc_method_handler(
                    servicer.ListModels,
                    request_deserializer=provider__pb2.ListRequest.FromString,
                    response_serializer=provider__pb2.ListModelsResponse.SerializeToString,
            ),
            'ListModelsByProvider': grpc.unary_unary_rpc_method_handler(
                    servicer.ListModelsByProvider,
                    request_deserializer=provider__pb2.GetByProviderRequest.FromString,
                    response_serializer=provider__pb2.ListModelsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'provider.ModelService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('provider.ModelService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ModelService(object):
    """Model Service
    """

    @staticmethod
    def CreateModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ModelService/CreateModel',
            provider__pb2.CreateModelRequest.SerializeToString,
            provider__pb2.ModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ModelService/GetModel',
            provider__pb2.GetByIdRequest.SerializeToString,
            provider__pb2.ModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ModelService/UpdateModel',
            provider__pb2.UpdateModelRequest.SerializeToString,
            provider__pb2.ModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ModelService/DeleteModel',
            provider__pb2.DeleteRequest.SerializeToString,
            provider__pb2.DeleteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListModels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ModelService/ListModels',
            provider__pb2.ListRequest.SerializeToString,
            provider__pb2.ListModelsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListModelsByProvider(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/provider.ModelService/ListModelsByProvider',
            provider__pb2.GetByProviderRequest.SerializeToString,
            provider__pb2.ListModelsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
