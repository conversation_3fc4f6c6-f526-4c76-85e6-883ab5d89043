"""
Simple test to check imports and basic functionality.
"""

import sys
import os
import json
from pathlib import Path

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.workflow_builder.workflow_schema_converter import (
        convert_workflow_to_transition_schema,
        determine_mapping_strategy,
        create_enhanced_field_mapping
    )
    print("✓ Successfully imported workflow_schema_converter functions")
except ImportError as e:
    print(f"✗ Failed to import workflow_schema_converter: {e}")
    sys.exit(1)

# Test basic functionality
try:
    # Load sample workflow
    sample_workflow_path = Path("testing/sample_workflow.json")
    if sample_workflow_path.exists():
        with open(sample_workflow_path, "r") as f:
            sample_workflow = json.load(f)
        print("✓ Successfully loaded sample workflow")
        
        # Test conversion
        result = convert_workflow_to_transition_schema(sample_workflow)
        print("✓ Successfully converted workflow to transition schema")
        print(f"  - Found {len(result.get('transitions', []))} transitions")
        print(f"  - Found {len(result.get('nodes', []))} nodes")
        
    else:
        print(f"✗ Sample workflow not found at {sample_workflow_path}")
        
except Exception as e:
    print(f"✗ Error during conversion: {e}")
    import traceback
    traceback.print_exc()

# Test mapping strategy function
try:
    source_node = {"data": {"type": "mcp"}}
    strategy = determine_mapping_strategy(source_node, "output", {}, "input")
    print("✓ Successfully tested determine_mapping_strategy")
    print(f"  - Strategy: {strategy}")
except Exception as e:
    print(f"✗ Error testing mapping strategy: {e}")

# Test enhanced field mapping
try:
    mapping = create_enhanced_field_mapping("output", "input", source_node, {}, "test_edge")
    print("✓ Successfully tested create_enhanced_field_mapping")
    print(f"  - Mapping: {mapping}")
except Exception as e:
    print(f"✗ Error testing enhanced field mapping: {e}")

print("\nTest completed!")
