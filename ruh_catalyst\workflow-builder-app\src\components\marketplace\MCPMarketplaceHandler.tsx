import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData, InputDefinition } from "@/types";
import { useComponentStateStore } from "@/store/mcpToolsStore";

/**
 * Helper function to get a value from the component state store for MCP Marketplace components
 */
export const getMCPMarketplaceValue = (nodeId: string, key: string, defaultValue?: any) => {
  return useComponentStateStore.getState().getValue(nodeId, key, defaultValue);
};

/**
 * Helper function to set a value in the component state store for MCP Marketplace components
 */
export const setMCPMarketplaceValue = (nodeId: string, key: string, value: any) => {
  useComponentStateStore.getState().setValue(nodeId, key, value);
};

/**
 * Helper function to clear state for an MCP Marketplace component
 */
export const clearMCPMarketplaceState = (nodeId: string) => {
  useComponentStateStore.getState().clearNodeState(nodeId);
};

/**
 * Check if a node is an MCP Marketplace component
 */
export const isMCPMarketplaceComponent = (node: Node<WorkflowNodeData> | null): boolean => {
  if (!node) return false;
  return node.data.type === "MCPMarketplaceComponent";
};

/**
 * Get the value for an input in an MCP Marketplace component
 */
export const getMCPMarketplaceInputValue = (
  node: Node<WorkflowNodeData>,
  inputName: string,
  defaultValue: any,
) => {
  // First try to get from the component state store
  const storeValue = getMCPMarketplaceValue(node.id, inputName, undefined);
  if (storeValue !== undefined) {
    return storeValue;
  }

  // Then try to get from the node config
  if (node.data.config && node.data.config[inputName] !== undefined) {
    return node.data.config[inputName];
  }

  // Finally, return the default value
  return defaultValue;
};

/**
 * Update the config for an MCP Marketplace component
 */
export const updateMCPMarketplaceConfig = (
  node: Node<WorkflowNodeData>,
  inputName: string,
  value: any,
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void,
) => {
  // Store in the component state store
  setMCPMarketplaceValue(node.id, inputName, value);

  // Update the node config
  const newConfig = {
    ...node.data.config,
    [inputName]: value,
  };

  // Update the node data
  const newData = {
    ...node.data,
    config: newConfig,
  };

  // Call the onNodeDataChange callback
  onNodeDataChange(node.id, newData);
};
