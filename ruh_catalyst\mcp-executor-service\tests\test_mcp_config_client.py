"""
Unit tests for MCP Configuration Client.
"""
import pytest
import asyncio
import aiohttp
from unittest.mock import AsyncMock, patch, MagicMock
import time

from app.services.mcp_config_client import MCPConfigClient, MCPConfigurationResult


class TestMCPConfigClient:
    """Test cases for MCPConfigClient."""
    
    @pytest.fixture
    def client(self):
        """Create a test client instance."""
        return MCPConfigClient()
    
    def test_parse_urls_image_stdio_priority(self, client):
        """Test that image_name + stdio gets highest priority."""
        urls = [
            {"url": "http://example.com/sse", "type": "sse"},
            {"image_name": "my-mcp:latest", "type": "stdio"},
            {"url": "http://example.com/http", "type": "http"}
        ]
        
        result = client.parse_urls(urls)
        
        assert result.execution_method == "container"
        assert result.config["image_name"] == "my-mcp:latest"
        assert result.config["type"] == "stdio"
    
    def test_parse_urls_image_other_priority(self, client):
        """Test that image_name + other type gets second priority."""
        urls = [
            {"url": "http://example.com/sse", "type": "sse"},
            {"image_name": "my-mcp:latest", "type": "http"}
        ]
        
        result = client.parse_urls(urls)
        
        assert result.execution_method == "container"
        assert result.config["image_name"] == "my-mcp:latest"
        assert result.config["type"] == "http"
    
    def test_parse_urls_url_sse_priority(self, client):
        """Test that url + sse gets third priority."""
        urls = [
            {"url": "http://example.com/sse", "type": "sse"},
            {"url": "http://example.com/http", "type": "http"}
        ]
        
        result = client.parse_urls(urls)
        
        assert result.execution_method == "url"
        assert result.config["url"] == "http://example.com/sse"
        assert result.config["type"] == "sse"
    
    def test_parse_urls_url_other_priority(self, client):
        """Test that url + other type gets fourth priority."""
        urls = [
            {"url": "http://example.com/http", "type": "http"}
        ]
        
        result = client.parse_urls(urls)
        
        assert result.execution_method == "url"
        assert result.config["url"] == "http://example.com/http"
        assert result.config["type"] == "http"
    
    def test_parse_urls_empty_array(self, client):
        """Test that empty URLs array raises ValueError."""
        with pytest.raises(ValueError, match="URLs array is empty"):
            client.parse_urls([])
    
    def test_parse_urls_invalid_entries(self, client):
        """Test that invalid URL entries are skipped."""
        urls = [
            "invalid_entry",  # Not a dict
            {"invalid": "entry"},  # No url or image_name
            {"url": "http://example.com/sse", "type": "sse"}  # Valid entry
        ]
        
        result = client.parse_urls(urls)
        
        assert result.execution_method == "url"
        assert result.config["url"] == "http://example.com/sse"
    
    def test_parse_urls_no_valid_entries(self, client):
        """Test that no valid entries raises ValueError."""
        urls = [
            "invalid_entry",
            {"invalid": "entry"}
        ]
        
        with pytest.raises(ValueError, match="No valid URL entries found"):
            client.parse_urls(urls)
    
    @pytest.mark.asyncio
    async def test_get_mcp_config_success(self, client):
        """Test successful MCP config retrieval."""
        mock_config = {
            "id": "test-mcp",
            "name": "Test MCP",
            "urls": [{"url": "http://example.com", "type": "sse"}]
        }
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_config)
            mock_response.text = AsyncMock(return_value='{"success": true}')
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            result = await client.get_mcp_config("test-mcp")
            
            assert result == mock_config
            assert "test-mcp" in client._cache
    
    @pytest.mark.asyncio
    async def test_get_mcp_config_404(self, client):
        """Test MCP config not found."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 404
            mock_response.text = AsyncMock(return_value='Not found')
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(Exception, match="MCP configuration not found"):
                await client.get_mcp_config("nonexistent-mcp")
    
    @pytest.mark.asyncio
    async def test_get_mcp_config_invalid_response(self, client):
        """Test invalid MCP config response."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value="not a dict")
            mock_response.text = AsyncMock(return_value='"not a dict"')
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(Exception, match="MCP config response is not a dictionary"):
                await client.get_mcp_config("test-mcp")
    
    @pytest.mark.asyncio
    async def test_get_mcp_config_missing_urls(self, client):
        """Test MCP config missing URLs field."""
        mock_config = {"id": "test-mcp", "name": "Test MCP"}  # Missing urls
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_config)
            mock_response.text = AsyncMock(return_value='{"id": "test-mcp"}')
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(Exception, match="MCP config missing 'urls' field"):
                await client.get_mcp_config("test-mcp")
    
    def test_cache_functionality(self, client):
        """Test caching functionality."""
        # Store in cache
        config = {"id": "test", "urls": []}
        client._store_in_cache("test-mcp", config)
        
        # Retrieve from cache
        cached = client._get_from_cache("test-mcp")
        assert cached == config
        
        # Test cache expiration
        client._cache["test-mcp"]["timestamp"] = time.time() - 400  # Expired
        expired = client._get_from_cache("test-mcp")
        assert expired is None
        assert "test-mcp" not in client._cache
    
    def test_clear_cache(self, client):
        """Test cache clearing."""
        client._store_in_cache("test1", {"urls": []})
        client._store_in_cache("test2", {"urls": []})
        
        assert len(client._cache) == 2
        
        client.clear_cache()
        
        assert len(client._cache) == 0
    
    @pytest.mark.asyncio
    async def test_get_execution_config_integration(self, client):
        """Test full execution config retrieval."""
        mock_config = {
            "id": "test-mcp",
            "name": "Test MCP",
            "urls": [
                {"image_name": "test:latest", "type": "stdio"},
                {"url": "http://example.com", "type": "sse"}
            ]
        }
        
        with patch.object(client, 'get_mcp_config', return_value=mock_config):
            result = await client.get_execution_config("test-mcp")
            
            assert isinstance(result, MCPConfigurationResult)
            assert result.execution_method == "container"
            assert result.config["image_name"] == "test:latest"
            assert result.mcp_config == mock_config


if __name__ == "__main__":
    pytest.main([__file__])
