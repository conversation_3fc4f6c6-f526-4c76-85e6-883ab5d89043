import { z } from "zod";

export const onboardingSchema = z
  .object({
    company: z.string().optional(),
    department: z.string().min(1, { message: "Please select a department." }),
    customDepartment: z.string().optional(),
    // Role is optional initially, its requirement depends on the department
    role: z.string().optional(),
    customRole: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    // Case 1: Department is 'Other'
    if (data.department === "Other") {
      // Custom department name is required
      if (!data.customDepartment || data.customDepartment.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please specify your department.",
          path: ["customDepartment"],
        });
      }
      // Custom role name is required (role dropdown is hidden)
      if (!data.customRole || data.customRole.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please specify your role.",
          path: ["customRole"], // Error on the custom role input
        });
      }
      // Clear role value as it's not used when department is 'Other'
      data.role = undefined;
    }
    // Case 2: Department is selected (not 'Other')
    else if (data.department) {
      // Role selection is required
      if (!data.role) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select a role.",
          path: ["role"], // Error on the role select
        });
      }
      // If Role selected is 'Other'
      else if (data.role === "Other") {
        // Custom role name is required
        if (!data.customRole || data.customRole.trim() === "") {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please specify your role.",
            path: ["customRole"], // Error on the custom role input
          });
        }
      }
      // Clear custom department value if a predefined one is selected
      data.customDepartment = undefined;
    }
  });

export type OnboardingType = z.infer<typeof onboardingSchema>;
