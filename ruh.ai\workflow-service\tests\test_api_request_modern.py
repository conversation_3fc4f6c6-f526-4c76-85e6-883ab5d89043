#!/usr/bin/env python3
"""
Test script to verify that the modern execute method of ApiRequestNode
properly handles dual-purpose inputs without wrapping them in a "value" key.
"""
import asyncio
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.components.data_interaction.api_request import ApiRequestNode
from app.models.workflow_builder.context import WorkflowContext


async def test_api_request_modern_execute():
    """Test that the modern execute method properly handles dual-purpose inputs."""
    print("Testing ApiRequestNode modern execute method...")
    
    # Create a component instance
    component = ApiRequestNode()
    
    # Create a workflow context with test data
    context = WorkflowContext(
        workflow_id="test-workflow",
        execution_id="test-execution"
    )
    
    # Set the current node ID
    context.current_node_id = "test-api-node"
    
    # Simulate input data that would come from dual-purpose inputs
    # This simulates what the orchestration engine would provide
    test_body_data = {
        "candidate_name": "<PERSON>",
        "candidate_email": "<EMAIL>",
        "skill_set": "Python",
        "job_role": "Developer"
    }
    
    # Set up node inputs in the context (this is how the orchestration engine provides data)
    context.node_outputs[context.current_node_id] = {
        "url": "https://httpbin.org/post",
        "method": "POST",
        "headers": {"Content-Type": "application/json"},
        "body": test_body_data,  # This should NOT be wrapped in {"value": ...}
        "timeout": 10
    }
    
    print(f"Input body data: {test_body_data}")
    print(f"Input body type: {type(test_body_data)}")
    
    # Test the get_input_value method
    retrieved_body = component.get_input_value("body", context, {})
    print(f"Retrieved body: {retrieved_body}")
    print(f"Retrieved body type: {type(retrieved_body)}")
    
    # Verify that the body is not wrapped
    if retrieved_body == test_body_data:
        print("✓ Body data retrieved correctly without value wrapper")
    else:
        print("✗ Body data was modified or wrapped")
        return False
    
    # Test that the component can access all inputs
    url = component.get_input_value("url", context, "")
    method = component.get_input_value("method", context, "GET")
    headers = component.get_input_value("headers", context, {})
    
    print(f"URL: {url}")
    print(f"Method: {method}")
    print(f"Headers: {headers}")
    
    if url and method and isinstance(headers, dict) and isinstance(retrieved_body, dict):
        print("✓ All inputs retrieved successfully")
        return True
    else:
        print("✗ Some inputs were not retrieved correctly")
        return False


async def main():
    """Main test function."""
    try:
        success = await test_api_request_modern_execute()
        if success:
            print("\n✓ All tests passed! The modern execute method properly handles dual-purpose inputs.")
        else:
            print("\n✗ Tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n✗ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
