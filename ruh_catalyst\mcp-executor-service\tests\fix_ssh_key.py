#!/usr/bin/env python3
"""
Fix SSH key formatting manually.
"""

import os
import sys
import base64
import subprocess

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.config.config import settings


def fix_ssh_key():
    """Fix SSH key formatting manually."""
    
    if not settings.ssh_key_content:
        print("❌ No SSH key content found")
        return False
    
    print(f"SSH key content length: {len(settings.ssh_key_content)} characters")
    
    # Decode SSH key
    try:
        decoded_content = base64.b64decode(settings.ssh_key_content).decode("utf-8")
        print("✅ SSH key decoded successfully")
    except Exception as e:
        print(f"❌ Failed to decode SSH key: {e}")
        return False
    
    # Clean and format the SSH key
    cleaned = decoded_content.replace('\n', '').replace('\r', '').replace(' ', '')
    
    # Find markers
    begin_idx = cleaned.find("-----BEGIN")
    end_idx = cleaned.find("-----END")
    
    if begin_idx == -1 or end_idx == -1:
        print("❌ SSH key markers not found")
        return False
    
    # Extract parts
    header_end = cleaned.find("-----", begin_idx + 5) + 5
    header = cleaned[begin_idx:header_end]
    
    footer_start = end_idx
    footer_end = cleaned.find("-----", footer_start + 5) + 5
    footer = cleaned[footer_start:footer_end]
    
    key_content = cleaned[header_end:footer_start]
    
    # Format with proper line breaks
    lines = [header]
    for i in range(0, len(key_content), 64):
        lines.append(key_content[i:i+64])
    lines.append(footer)
    
    formatted_key = '\n'.join(lines)
    
    print(f"Formatted SSH key into {len(lines)} lines")
    print("First few lines:")
    for i, line in enumerate(lines[:3]):
        print(f"  {i+1}: {line}")
    
    # Write to file
    ssh_key_path = "mcp_ssh_key.pem"
    try:
        with open(ssh_key_path, 'w') as f:
            f.write(formatted_key)
        print(f"✅ SSH key written to: {ssh_key_path}")
    except Exception as e:
        print(f"❌ Failed to write SSH key: {e}")
        return False
    
    # Set permissions on Windows
    try:
        username = os.environ.get("USERNAME", "Administrator")
        
        # Remove inheritance and set permissions
        subprocess.run(["icacls", ssh_key_path, "/inheritance:r"], 
                      capture_output=True, check=True)
        subprocess.run(["icacls", ssh_key_path, "/grant:r", f"{username}:F"], 
                      capture_output=True, check=True)
        
        print("✅ SSH key permissions set")
    except Exception as e:
        print(f"⚠️ Failed to set permissions: {e}")
    
    return True


if __name__ == "__main__":
    success = fix_ssh_key()
    if success:
        print("✅ SSH key fixed successfully")
    else:
        print("❌ Failed to fix SSH key")
