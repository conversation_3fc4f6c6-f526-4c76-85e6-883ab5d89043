import asyncio
import logging
import json
import uuid
from typing import Dict, Any
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")
KAFKA_AGENT_MESSAGE_TOPIC = os.getenv(
    "KAFKA_AGENT_MESSAGE_TOPIC", "agent_message_requests"
)
KAFKA_AGENT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_AGENT_RESPONSE_TOPIC", "agent_chat_responses"
)


class AgentMessageTestClient:
    def __init__(self):
        self.producer = None
        self.consumer = None
        self._initialized = False

    async def initialize(self):
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            max_request_size=524288000,
        )
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"test-message-group-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )

        await self.producer.start()
        await self.consumer.start()
        self._initialized = True
        logger.info("Agent Message test client initialized successfully")

    async def cleanup(self):
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_message(
        self, topic: str, message: Dict[str, Any], headers: list = None
    ) -> None:
        try:
            value = json.dumps(message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic}: {message}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def listen_for_responses(self, correlation_id: str = None, timeout: int = 30):
        """Listen for responses with improved debugging and error handling"""
        try:
            start_time = asyncio.get_event_loop().time()
            responses = []
            logger.info(
                f"Starting to listen for responses with "
                f"correlation_id: {correlation_id}"
            )

            while True:
                try:
                    # Set a timeout for each message poll
                    msg = await asyncio.wait_for(
                        self.consumer.getone(), timeout=timeout
                    )

                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > timeout:
                        logger.warning("Response listening timeout reached")
                        break

                    # Log raw message for debugging
                    logger.debug(f"Received raw message: {msg}")

                    # Process message headers
                    msg_correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k: v.decode("utf-8") if isinstance(v, bytes) else v
                            for k, v in msg.headers
                        }
                        msg_correlation_id = headers_dict.get("correlationId")
                        logger.debug(f"Message headers: {headers_dict}")

                    # Decode and process message value
                    try:
                        response = json.loads(msg.value.decode("utf-8"))
                        logger.debug(f"Decoded response: {response}")

                        # Check correlation ID match
                        if correlation_id and msg_correlation_id == correlation_id:
                            logger.info(
                                f"Matched response for correlation_id "
                                f"{correlation_id}"
                            )
                            responses.append(response)

                            # Check if this is a final response
                            if response.get("final", True):
                                logger.info("Received final response, breaking loop")
                                break

                        else:
                            logger.debug(
                                f"Skipping message with different "
                                f"correlation_id: {msg_correlation_id}"
                            )

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message value: {e}")
                        continue

                except asyncio.TimeoutError:
                    logger.warning("Timeout waiting for message")
                    break
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    continue

            logger.info(f"Finished listening. Collected {len(responses)} responses")
            return responses

        except Exception as e:
            logger.error(f"Error in listen_for_responses: {e}")
            raise


def get_sample_agent_config():
    """Get a sample agent configuration for testing"""
    return {
        "id": "test-agent-001",
        "name": "Marketing",
        "description": "A helpful test assistant",
        "system_message": (
            "You are a helpful assistant. Respond to user queries "
            "in a friendly and informative manner."
        ),
        "model_name": "gpt-4o",
        "model_config": {
            "model": "gpt-4o-mini",
            "temperature": 0.7,
            "max_tokens": 1000,
        },
        "tools": [],
        "capabilities": ["general_assistance", "question_answering"],
    }


async def test_agent_message():
    """Test agent message functionality with provided agent config"""
    client = AgentMessageTestClient()
    await client.initialize()

    try:
        print("\nAgent Message Test")
        print("=" * 50)

        # Get user input for the message
        query = input("Enter your message for the agent: ").strip()
        if not query:
            query = "Hello, can you introduce yourself?"

        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Get sample agent configuration
        agent_config = get_sample_agent_config()

        # Prepare agent message request
        message_request = {
            "agent_config": agent_config,
            "run_id": run_id,
            "query": query,
            "user_id": "test_user",
            "variables": {},
            "organization_id": None,
            "use_knowledge": False,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        logger.info(f"Sending agent message with run_id: {run_id}")
        logger.info(f"Agent: {agent_config['name']}")
        logger.info(f"Query: {query}")

        # Send message request
        await client.send_message(KAFKA_AGENT_MESSAGE_TOPIC, message_request, headers)

        # Wait for response
        responses = await client.listen_for_responses(correlation_id=run_id, timeout=60)

        if not responses:
            print("❌ No response received for agent message")
            return

        print(f"\n✅ Received {len(responses)} response(s)")
        print("-" * 50)

        for i, response in enumerate(responses, 1):
            print(f"\nResponse {i}:")
            print(f"Success: {response.get('success', False)}")
            print(f"Message: {response.get('message', 'No message')}")

            agent_response = response.get("agent_response", {})
            if agent_response:
                content = agent_response.get("content", "No content")
                print(f"Agent Response: {content}")

            print(f"Final: {response.get('final', False)}")

    except Exception as e:
        logger.error(f"Agent message test error: {e}")
        print(f"❌ Test failed: {e}")
    finally:
        await client.cleanup()


async def interactive_agent_message():
    """Interactive agent message session"""
    client = AgentMessageTestClient()
    await client.initialize()

    try:
        print("\nInteractive Agent Message Session")
        print("Type 'quit' to exit")
        print("=" * 50)

        # Get agent configuration once
        agent_config = get_sample_agent_config()
        print(f"Using agent: {agent_config['name']}")
        print(f"Description: {agent_config['description']}")

        while True:
            # Get user input
            query = input("\nYour message: ").strip()
            if query.lower() in ["quit", "exit", "bye"]:
                break

            if not query:
                continue

            # Generate unique run ID for each message
            run_id = str(uuid.uuid4())

            # Prepare agent message request
            message_request = {
                "agent_config": agent_config,
                "run_id": run_id,
                "query": query,
                "user_id": "test_user",
                "variables": {},
                "organization_id": None,
                "use_knowledge": False,
            }

            headers = [
                ("correlationId", run_id.encode("utf-8")),
                ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
            ]

            # Send message request
            await client.send_message(
                KAFKA_AGENT_MESSAGE_TOPIC, message_request, headers
            )

            # Wait for and process responses
            responses = await client.listen_for_responses(
                correlation_id=run_id, timeout=30
            )

            if responses:
                for response in responses:
                    if response.get("success", False):
                        agent_response = response.get("agent_response", {})
                        content = agent_response.get("content", "No content available")
                        print(f"\n🤖 {agent_config['name']}: {content}")
                    else:
                        error_msg = response.get("message", "Unknown error")
                        print(f"\n❌ Error: {error_msg}")
            else:
                print("\n❌ No response received from agent")

    except Exception as e:
        logger.error(f"Interactive message error: {e}")
        print(f"❌ Session error: {e}")
    finally:
        await client.cleanup()


async def test_custom_agent_config():
    """Test with a custom agent configuration"""
    client = AgentMessageTestClient()
    await client.initialize()

    try:
        print("\nCustom Agent Configuration Test")
        print("=" * 50)

        # Custom agent config for a specialized assistant
        custom_agent_config = {
            "id": "math-tutor-001",
            "name": "Math Tutor",
            "description": "A specialized mathematics tutor",
            "system_message": (
                "You are an expert mathematics tutor. Help students "
                "understand mathematical concepts by providing clear "
                "explanations, step-by-step solutions, and examples. "
                "Always encourage learning and ask if they need "
                "further clarification."
            ),
            "model_config": {
                "model": "gpt-4o-mini",
                "temperature": 0.3,  # Lower temperature for more precise answers
                "max_tokens": 1500,
            },
            "tools": [],
            "capabilities": ["mathematics", "tutoring", "problem_solving"],
        }

        # Get user input for the math question
        query = input("Ask a math question: ").strip()
        if not query:
            query = "Can you explain how to solve quadratic equations?"

        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare agent message request
        message_request = {
            "agent_config": custom_agent_config,
            "run_id": run_id,
            "query": query,
            "user_id": "test_student",
            "variables": {},
            "organization_id": None,
            "use_knowledge": False,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        logger.info(f"Sending message to {custom_agent_config['name']}")
        logger.info(f"Question: {query}")

        # Send message request
        await client.send_message(KAFKA_AGENT_MESSAGE_TOPIC, message_request, headers)

        # Wait for response
        responses = await client.listen_for_responses(correlation_id=run_id, timeout=60)

        if not responses:
            print("❌ No response received from math tutor")
            return

        print(f"\n✅ Received response from {custom_agent_config['name']}")
        print("-" * 50)

        for response in responses:
            if response.get("success", False):
                agent_response = response.get("agent_response", {})
                content = agent_response.get("content", "No content")
                print(f"\n📚 {custom_agent_config['name']}: {content}")
            else:
                error_msg = response.get("message", "Unknown error")
                print(f"\n❌ Error: {error_msg}")

    except Exception as e:
        logger.error(f"Custom agent test error: {e}")
        print(f"❌ Test failed: {e}")
    finally:
        await client.cleanup()


async def main():
    try:
        print("Agent Message Test Options:")
        print("1. Single message test")
        print("2. Interactive message session")
        print("3. Custom agent configuration test")

        choice = input("\nSelect option (1, 2, or 3): ").strip()

        if choice == "1":
            await test_agent_message()
        elif choice == "2":
            await interactive_agent_message()
        elif choice == "3":
            await test_custom_agent_config()
        else:
            print("Invalid choice. Running single message test...")
            await test_agent_message()

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
