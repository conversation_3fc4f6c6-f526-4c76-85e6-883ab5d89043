/**
 * Tests for AgenticAI Tool Panel component
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { AgenticAIToolPanel } from "../AgenticAIToolPanel";

// Mock tool connection utilities
jest.mock("@/utils/toolConnectionUtils", () => ({
  calculateToolConnectionState: jest.fn(),
}));

import {
  calculateToolConnectionState
} from "@/utils/toolConnectionUtils";

const mockCalculateToolConnectionState = calculateToolConnectionState as jest.MockedFunction<typeof calculateToolConnectionState>;

// Helper function to create test nodes
const createTestNode = (id: string, originalType: string): Node<WorkflowNodeData> => ({
  id,
  type: "WorkflowNode",
  position: { x: 0, y: 0 },
  data: {
    label: `${originalType} Node`,
    type: "component",
    originalType,
    definition: { 
      name: originalType,
      inputs: [],
      outputs: []
    },
    config: {},
  },
});

// Helper function to create test edges
const createTestEdge = (source: string, target: string, targetHandle?: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target,
  targetHandle,
});

describe("AgenticAIToolPanel", () => {
  const mockOnConfigChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockCalculateToolConnectionState.mockReturnValue({
      connectedTools: [],
      toolCount: 0,
      hasToolConnections: false,
    });
  });

  describe("Rendering", () => {
    test("renders tool management section for AgenticAI node", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const nodes = [agenticAINode];
      const edges: Edge[] = [];

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByText("🔧 Agent Tools")).toBeInTheDocument();
      expect(screen.getByText("Connected Components")).toBeInTheDocument();
      expect(screen.getByText("Tool Management")).toBeInTheDocument();
    });

    test("displays tool count badge when tools are connected", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const toolNode = createTestNode("tool1", "DataProcessor");
      const nodes = [agenticAINode, toolNode];
      const edges = [createTestEdge("tool1", "agent", "tool_1")];

      mockCalculateToolConnectionState.mockReturnValue({
        connectedTools: [
          {
            nodeId: "tool1",
            handleId: "tool_1",
            componentType: "DataProcessor",
            label: "DataProcessor Node",
          }
        ],
        toolCount: 1,
        hasToolConnections: true,
      });

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByText("1")).toBeInTheDocument(); // Tool count badge
      expect(screen.getByText("DataProcessor Node")).toBeInTheDocument();
    });

    test("shows empty state when no tools are connected", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const nodes = [agenticAINode];
      const edges: Edge[] = [];

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByText("No tools connected")).toBeInTheDocument();
      expect(screen.getByText("Connect components to tool handles to use them as agent tools")).toBeInTheDocument();
    });
  });

  describe("Tool Connection Cards", () => {
    test("displays tool connection cards with correct information", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const toolNode1 = createTestNode("tool1", "DataProcessor");
      const toolNode2 = createTestNode("tool2", "MCPMarketplace");
      const nodes = [agenticAINode, toolNode1, toolNode2];
      const edges = [
        createTestEdge("tool1", "agent", "tool_1"),
        createTestEdge("tool2", "agent", "tool_2"),
      ];

      mockCalculateToolConnectionState.mockReturnValue({
        connectedTools: [
          {
            nodeId: "tool1",
            handleId: "tool_1",
            componentType: "DataProcessor",
            label: "DataProcessor Node",
          },
          {
            nodeId: "tool2",
            handleId: "tool_2",
            componentType: "MCPMarketplace",
            label: "MCPMarketplace Node",
          }
        ],
        toolCount: 2,
        hasToolConnections: true,
      });

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByText("DataProcessor Node")).toBeInTheDocument();
      expect(screen.getByText("MCPMarketplace Node")).toBeInTheDocument();
      expect(screen.getByText("2")).toBeInTheDocument(); // Tool count badge
    });

    test("shows MCP badge for MCP marketplace components", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const mcpNode = createTestNode("mcp1", "MCPMarketplace");
      const nodes = [agenticAINode, mcpNode];
      const edges = [createTestEdge("mcp1", "agent", "tool_1")];

      mockCalculateToolConnectionState.mockReturnValue({
        connectedTools: [
          {
            nodeId: "mcp1",
            handleId: "tool_1",
            componentType: "MCPMarketplace",
            label: "MCPMarketplace Node",
          }
        ],
        toolCount: 1,
        hasToolConnections: true,
      });

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByText("MCP")).toBeInTheDocument(); // MCP badge
    });
  });

  // Tool Slot Management tests removed - no longer applicable with single handle approach

  describe("Real-time Updates", () => {
    test("updates tool count when connections change", async () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const toolNode = createTestNode("tool1", "DataProcessor");
      const nodes = [agenticAINode, toolNode];
      
      // Initially no connections
      let edges: Edge[] = [];
      mockCalculateToolConnectionState.mockReturnValue({
        connectedTools: [],
        toolCount: 0,
        hasToolConnections: false,
      });

      const { rerender } = render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByText("No tools connected")).toBeInTheDocument();

      // Add connection
      edges = [createTestEdge("tool1", "agent", "tool_1")];
      mockCalculateToolConnectionState.mockReturnValue({
        connectedTools: [
          {
            nodeId: "tool1",
            handleId: "tool_1",
            componentType: "DataProcessor",
            label: "DataProcessor Node",
          }
        ],
        toolCount: 1,
        hasToolConnections: true,
      });

      rerender(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText("DataProcessor Node")).toBeInTheDocument();
        expect(screen.getByText("1")).toBeInTheDocument(); // Tool count badge
      });
    });
  });

  describe("Accessibility", () => {
    test("has proper ARIA labels and roles", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const nodes = [agenticAINode];
      const edges: Edge[] = [];

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      expect(screen.getByRole("region", { name: "Agent Tools" })).toBeInTheDocument();
    });

    test("provides proper keyboard navigation", () => {
      const agenticAINode = createTestNode("agent", "AgenticAI");
      const nodes = [agenticAINode];
      const edges: Edge[] = [];

      render(
        <AgenticAIToolPanel
          node={agenticAINode}
          nodes={nodes}
          edges={edges}
          onConfigChange={mockOnConfigChange}
        />
      );

      // Test that the component is keyboard accessible
      const toolSection = screen.getByRole("region", { name: "Agent Tools" });
      expect(toolSection).toBeInTheDocument();
    });
  });
});
