import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Input } from "@/components/ui/input";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { cn } from "@/lib/utils";
import { getHtmlInputType } from "@/utils/valueFormatting";

interface StringInputProps {
  inputDef: InputDefinition;
  value: string;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering string inputs
 */
export function StringInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: StringInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  
  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="relative">
        <Input
          id={inputId}
          type={getHtmlInputType(inputDef.input_type)}
          value={value ?? ""}
          onChange={(e) => onChange(inputDef.name, e.target.value)}
          placeholder={inputDef.display_name}
          className={cn(
            "bg-background/50 mt-1 h-8 text-xs",
            isDisabled && "opacity-50"
          )}
          disabled={isDisabled}
        />
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
