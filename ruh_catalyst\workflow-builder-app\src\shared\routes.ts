/**
 * Shared route definitions for the application
 * Matches the implementation in ruh-app-fe for consistency.
 */

// Authentication routes
export const loginRoute = "/login";
export const signupRoute = "/signup";
export const verifyEmailRoute = "/verify-email";
export const updatePasswordRoute = "/reset-password";
export const authRoute = `${process.env.NEXT_PUBLIC_AUTHENTICATION_URL}?redirect_url=${process.env.NEXT_PUBLIC_APP_URL}`;

// Dashboard routes
export const homeRoute = "/workflows"; // Updated to point to workflows instead of /home
export const workflowsRoute = "/workflows";
export const settingsRoute = "/settings";
export const credentialsRoute = "/credentials";

// Public routes
export const aboutRoute = "/about";
export const contactRoute = "/contact";

// Define public routes that don't require authentication
export const publicRoutes = [
  loginRoute,
  signupRoute,
  verifyEmailRoute,
  updatePasswordRoute,
  aboutRoute,
  contactRoute,
];

// Define protected routes that require authentication
export const protectedRoutes = [workflowsRoute, workflowsRoute, settingsRoute, credentialsRoute];
