import os
import base64
import tempfile
import logging
from typing import Optional

from app.core_.exceptions import (
    SSHConnectionError,
    ErrorCategory,
    ErrorCode,
    MCPExecutorError,
)

logger = logging.getLogger(__name__)


class SecureSSHKeyManager:
    """
    Manages SSH keys securely with temporary file handling and proper cleanup.
    Supports both file paths and base64-encoded key content.
    """

    def __init__(self):
        self.temp_key_file: Optional[str] = None

    def _create_temp_key_file(self, ssh_key_content: str) -> str:
        """
        Create temporary file with SSH key content.

        Args:
            ssh_key_content: SSH private key content as string

        Returns:
            Path to temporary key file
        """
        if self.temp_key_file is None:
            # Create temporary file for SSH key
            temp_fd, temp_path = tempfile.mkstemp(prefix="mcp_ssh_key_", suffix=".pem")

            final_ssh_key_content = base64.b64decode(ssh_key_content).decode("utf-8")

            # Write key content to temporary file
            with os.fdopen(temp_fd, "w") as f:
                f.write(final_ssh_key_content)
                logger.debug(f"final_ssh_key_content: {final_ssh_key_content}")
            # Set proper permissions (600) - owner read/write only
            os.chmod(temp_path, 0o600)

            self.temp_key_file = temp_path
            logger.debug(f"Created temporary SSH key file: {temp_path}")

        return self.temp_key_file

    def cleanup_temp_key_file(self):
        """Clean up temporary SSH key file securely."""
        if self.temp_key_file and os.path.exists(self.temp_key_file):
            try:
                # Overwrite file content with zeros before deletion (security)
                with open(self.temp_key_file, "r+b") as f:
                    length = f.seek(0, 2)  # Get file size
                    f.seek(0)
                    f.write(b"\x00" * length)  # Overwrite with zeros
                    f.flush()
                    os.fsync(f.fileno())  # Force write to disk

                # Remove the file
                os.unlink(self.temp_key_file)
                logger.debug(f"Cleaned up temporary SSH key file: {self.temp_key_file}")
            except Exception as e:
                logger.warning(f"Failed to clean up SSH key file: {e}")
            finally:
                self.temp_key_file = None

    def get_ssh_key_path(
        self, ssh_key_config: str, raise_on_error: bool = False
    ) -> str:
        """
        Get SSH key path, handling both file paths and base64-encoded content.

        Args:
            ssh_key_config: Either a file path or base64-encoded key content
            raise_on_error: If True, raise SSHConnectionError on failure

        Returns:
            Path to SSH key file (either original or temporary)

        Raises:
            SSHConnectionError: If raise_on_error=True and key processing fails
        """
        if not ssh_key_config:
            error_msg = "SSH key configuration is empty"
            if raise_on_error:
                raise SSHConnectionError(
                    "localhost", "unknown", error_msg, {"operation": "key_validation"}
                )
            raise ValueError(error_msg)

        # Check if it's a file path
        if os.path.isfile(ssh_key_config):
            logger.debug("Using SSH key from file path")
            return ssh_key_config

        # Check if it's base64-encoded content
        try:
            # Try to decode as base64
            decoded_content = base64.b64decode(ssh_key_config).decode("utf-8")

            # Validate it looks like an SSH key
            if (
                "BEGIN" in decoded_content
                and "PRIVATE KEY" in decoded_content
                and "END" in decoded_content
            ):
                logger.debug("Using base64-encoded SSH key content")
                return self._create_temp_key_file(ssh_key_config)
            else:
                # Treat as raw key content
                logger.debug("Using raw SSH key content")
                return self._create_temp_key_file(ssh_key_config)

        except Exception as e:
            # If base64 decoding fails, treat as raw key content
            logger.debug("Using raw SSH key content (base64 decode failed)")
            try:
                return self._create_temp_key_file(ssh_key_config)
            except Exception as create_error:
                error_msg = f"Failed to process SSH key: {create_error}"
                if raise_on_error:
                    raise SSHConnectionError(
                        "localhost",
                        "unknown",
                        error_msg,
                        {"operation": "key_processing", "original_error": str(e)},
                    ) from create_error
                raise create_error

    def get_ssh_key_path_with_exceptions(self, ssh_key_config: str) -> str:
        """
        Get SSH key path, raising SSHConnectionError on any failure.

        Args:
            ssh_key_config: Either a file path or base64-encoded key content

        Returns:
            Path to SSH key file (either original or temporary)

        Raises:
            SSHConnectionError: If key processing fails
        """
        return self.get_ssh_key_path(ssh_key_config, raise_on_error=True)

    def __del__(self):
        """Ensure cleanup on object destruction."""
        self.cleanup_temp_key_file()


class GlobalSSHKeyManager:
    """
    Global singleton SSH key manager that creates SSH key files once during startup
    and reuses them for all SSH connections throughout the application lifecycle.
    """

    _instance: Optional["GlobalSSHKeyManager"] = None
    _initialized: bool = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.temp_key_file: Optional[str] = None
            self._ssh_key_content: Optional[str] = None
            self._ssh_host: Optional[str] = None
            self._ssh_user: Optional[str] = None
            GlobalSSHKeyManager._initialized = True
            logger.debug("Global SSH key manager initialized")

    def _format_ssh_key(self, ssh_key_content: str) -> str:
        """
        Format SSH key content with proper line breaks.

        Args:
            ssh_key_content: Raw SSH key content

        Returns:
            Properly formatted SSH key content
        """
        # Remove any existing line breaks and spaces
        cleaned_content = (
            ssh_key_content.replace("\n", "").replace("\r", "").replace(" ", "")
        )

        # Find the header and footer
        begin_marker = "-----BEGIN"
        end_marker = "-----END"

        begin_idx = cleaned_content.find(begin_marker)
        end_idx = cleaned_content.find(end_marker)

        if begin_idx == -1 or end_idx == -1:
            logger.warning("SSH key markers not found, returning content as-is")
            return ssh_key_content

        # Extract header and footer with proper spacing
        header_end = cleaned_content.find("-----", begin_idx + 5) + 5
        raw_header = cleaned_content[begin_idx:header_end]

        footer_start = end_idx
        footer_end = cleaned_content.find("-----", footer_start + 5) + 5
        raw_footer = cleaned_content[footer_start:footer_end]

        # Fix header spacing
        if "BEGINRSA" in raw_header:
            header = "-----BEGIN RSA PRIVATE KEY-----"
            logger.debug("Fixed header spacing: -----BEGIN RSA PRIVATE KEY-----")
        elif "BEGINOPENSSH" in raw_header:
            header = "-----BEGIN OPENSSH PRIVATE KEY-----"
            logger.debug("Fixed header spacing: -----BEGIN OPENSSH PRIVATE KEY-----")
        elif "BEGINPRIVATE" in raw_header:
            header = "-----BEGIN PRIVATE KEY-----"
            logger.debug("Fixed header spacing: -----BEGIN PRIVATE KEY-----")
        else:
            header = raw_header
            logger.debug(f"Using original header: {header}")

        # Fix footer spacing
        if "ENDRSA" in raw_footer:
            footer = "-----END RSA PRIVATE KEY-----"
            logger.debug("Fixed footer spacing: -----END RSA PRIVATE KEY-----")
        elif "ENDOPENSSH" in raw_footer:
            footer = "-----END OPENSSH PRIVATE KEY-----"
            logger.debug("Fixed footer spacing: -----END OPENSSH PRIVATE KEY-----")
        elif "ENDPRIVATE" in raw_footer:
            footer = "-----END PRIVATE KEY-----"
            logger.debug("Fixed footer spacing: -----END PRIVATE KEY-----")
        else:
            footer = raw_footer
            logger.debug(f"Using original footer: {footer}")

        # Extract key content (between header and footer)
        key_content = cleaned_content[header_end:footer_start]

        # Format key content with 64 characters per line
        formatted_lines = [header]
        for i in range(0, len(key_content), 64):
            formatted_lines.append(key_content[i : i + 64])
        formatted_lines.append(footer)

        # Join with newlines
        formatted_key = "\n".join(formatted_lines)

        logger.debug(f"Formatted SSH key: {len(formatted_lines)} lines")
        return formatted_key

    async def setup_host_key_verification(self, ssh_host: str, ssh_user: str) -> None:
        """
        Setup host key verification to avoid SSH connection issues.

        Args:
            ssh_host: The SSH hostname/IP to add to known_hosts
            ssh_user: The SSH username for connection

        Raises:
            RuntimeError: If host key setup fails
        """
        if not self.temp_key_file:
            raise RuntimeError(
                "SSH key file not initialized. Call initialize_ssh_key() first."
            )

        logger.info(f"Setting up host key verification for {ssh_user}@{ssh_host}")

        try:
            import subprocess
            import os

            # Step 1: Set proper permissions on SSH key file (chmod 400)
            if os.name == "nt":  # Windows
                try:
                    # Use icacls for Windows
                    subprocess.run(
                        [
                            "icacls",
                            self.temp_key_file,
                            "/inheritance:r",
                            "/grant:r",
                            f'{os.environ.get("USERNAME", "Administrator")}:R',
                        ],
                        check=True,
                        capture_output=True,
                        text=True,
                    )
                    logger.info(
                        f"Set Windows permissions (read-only) for SSH key: {self.temp_key_file}"
                    )
                except subprocess.CalledProcessError:
                    # Fallback to chmod
                    os.chmod(self.temp_key_file, 0o400)
                    logger.info(
                        f"Set fallback permissions (400) for SSH key: {self.temp_key_file}"
                    )
            else:
                # Unix/Linux - set 400 permissions
                os.chmod(self.temp_key_file, 0o400)
                logger.info(
                    f"Set SSH key file permissions to 400: {self.temp_key_file}"
                )

            # Step 2: Add host key to known_hosts using ssh-keyscan
            # First, ensure .ssh directory exists
            ssh_dir = os.path.expanduser("~/.ssh")
            os.makedirs(ssh_dir, exist_ok=True)

            # Use ssh-keyscan to get host key and add to known_hosts
            keyscan_command = ["ssh-keyscan", "-H", ssh_host]

            logger.info(
                f"Running ssh-keyscan to get host key: {' '.join(keyscan_command)}"
            )
            result = subprocess.run(
                keyscan_command, check=True, text=True, capture_output=True, timeout=30
            )

            if result.stdout:
                known_hosts_path = os.path.join(ssh_dir, "known_hosts")
                with open(known_hosts_path, "a") as f:
                    f.write(result.stdout)
                logger.info(
                    f"Host key added to known_hosts successfully for {ssh_host}: {known_hosts_path}"
                )
            else:
                logger.warning(f"ssh-keyscan returned no output for {ssh_host}")

        except subprocess.CalledProcessError as e:
            error_msg = f"Failed to setup host key verification: {e.stderr if e.stderr else str(e)}"
            logger.error(f"Host key setup failed for {ssh_host}: {error_msg}")
            raise RuntimeError(error_msg)
        except subprocess.TimeoutExpired:
            error_msg = "Host key setup timed out"
            logger.error(f"Host key setup timed out for {ssh_host}")
            raise RuntimeError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during host key setup: {str(e)}"
            logger.error(
                f"Unexpected error during host key setup for {ssh_host}: {error_msg}"
            )
            raise RuntimeError(error_msg)

    def initialize_ssh_key(
        self, ssh_key_content: str, raise_on_error: bool = False
    ) -> None:
        """
        Initialize the SSH key file once during application startup.
        Creates the SSH key file in the local repository directory.

        Args:
            ssh_key_content: SSH private key content (base64 encoded or raw)
            raise_on_error: If True, raise SSHConnectionError on failure

        Raises:
            SSHConnectionError: If raise_on_error=True and initialization fails
        """
        if self.temp_key_file is not None:
            logger.debug("SSH key file already initialized, skipping")
            return

        if not ssh_key_content:
            logger.warning("No SSH key content provided for initialization")
            return

        self._ssh_key_content = ssh_key_content

        # Check if it's a file path
        if os.path.isfile(ssh_key_content):
            logger.debug("Using SSH key from file path")
            self.temp_key_file = ssh_key_content
            return

        # Create SSH key file in the local repository
        try:
            # Try to decode as base64 first
            try:
                decoded_content = base64.b64decode(ssh_key_content).decode("utf-8")
                # Validate it looks like an SSH key
                if (
                    "BEGIN" in decoded_content
                    and "PRIVATE KEY" in decoded_content
                    and "END" in decoded_content
                ):
                    final_ssh_key_content = self._format_ssh_key(decoded_content)
                    logger.debug("Using base64-encoded SSH key content")
                else:
                    # Treat as raw key content
                    final_ssh_key_content = self._format_ssh_key(decoded_content)
                    logger.debug("Using raw SSH key content")
            except Exception:
                # If base64 decoding fails, treat as raw key content
                final_ssh_key_content = self._format_ssh_key(ssh_key_content)
                logger.debug("Using raw SSH key content (base64 decode failed)")

            # Create SSH key file in the local repository directory
            # Get the current working directory (should be the repo root)
            repo_root = os.getcwd()
            ssh_key_path = os.path.join(repo_root, "mcp_ssh_key.pem")

            # Check if file already exists and try to remove it first
            if os.path.exists(ssh_key_path):
                try:
                    os.remove(ssh_key_path)
                    logger.debug(f"Removed existing SSH key file: {ssh_key_path}")
                except Exception as remove_error:
                    logger.warning(
                        f"Failed to remove existing SSH key file: {remove_error}"
                    )
                    # Try using a temporary file instead
                    import tempfile

                    temp_fd, ssh_key_path = tempfile.mkstemp(
                        prefix="mcp_ssh_key_", suffix=".pem"
                    )
                    os.close(
                        temp_fd
                    )  # Close the file descriptor, we'll open it normally
                    logger.info(f"Using temporary SSH key file: {ssh_key_path}")

            # Write key content to file
            try:
                with open(ssh_key_path, "w") as f:
                    f.write(final_ssh_key_content)
            except PermissionError as perm_error:
                logger.warning(
                    f"Permission denied writing to {ssh_key_path}, using temporary file"
                )
                # Fallback to temporary file
                import tempfile

                temp_fd, ssh_key_path = tempfile.mkstemp(
                    prefix="mcp_ssh_key_", suffix=".pem"
                )
                with os.fdopen(temp_fd, "w") as f:
                    f.write(final_ssh_key_content)
                logger.info(f"Created temporary SSH key file: {ssh_key_path}")

            # Set proper permissions - handle Windows vs Unix
            try:
                if os.name == "nt":  # Windows
                    # On Windows, use icacls to set proper permissions
                    import subprocess

                    # Remove all permissions except for current user
                    subprocess.run(
                        [
                            "icacls",
                            ssh_key_path,
                            "/inheritance:r",
                            "/grant:r",
                            f'{os.environ.get("USERNAME", "Administrator")}:F',
                        ],
                        check=True,
                        capture_output=True,
                    )
                    logger.info(f"Set Windows permissions for SSH key: {ssh_key_path}")
                else:
                    # Unix/Linux - set 600 permissions
                    os.chmod(ssh_key_path, 0o600)
                    logger.info(
                        f"Set Unix permissions (600) for SSH key: {ssh_key_path}"
                    )
            except Exception as perm_error:
                logger.warning(f"Failed to set SSH key permissions: {perm_error}")
                # Try alternative Windows method
                if os.name == "nt":
                    try:
                        # Alternative: just set read-only for owner
                        os.chmod(ssh_key_path, 0o400)
                        logger.info(
                            f"Set alternative permissions for SSH key: {ssh_key_path}"
                        )
                    except Exception as alt_error:
                        logger.warning(
                            f"Failed to set alternative permissions: {alt_error}"
                        )

            self.temp_key_file = ssh_key_path
            logger.info(f"Global SSH key file created in repository: {ssh_key_path}")

        except Exception as e:
            error_msg = f"Failed to create global SSH key file: {e}"
            logger.error(error_msg)
            if raise_on_error:
                raise SSHConnectionError(
                    "localhost",
                    "system",
                    error_msg,
                    {
                        "operation": "ssh_key_initialization",
                        "exception_type": type(e).__name__,
                    },
                ) from e
            raise

    def get_ssh_key_path(self) -> Optional[str]:
        """
        Get the path to the SSH key file.

        Returns:
            Path to SSH key file or None if not initialized
        """
        if self.temp_key_file is None:
            logger.warning(
                "SSH key file not initialized. Call initialize_ssh_key() first."
            )
            return None

        if not os.path.exists(self.temp_key_file):
            logger.error(f"SSH key file no longer exists: {self.temp_key_file}")
            return None

        return self.temp_key_file

    def get_ssh_key_path_with_exceptions(self) -> str:
        """
        Get the path to the SSH key file, raising exception if not available.

        Returns:
            Path to SSH key file

        Raises:
            SSHConnectionError: If SSH key is not initialized or not available
        """
        key_path = self.get_ssh_key_path()
        if key_path is None:
            raise SSHConnectionError(
                "localhost",
                "system",
                "SSH key not initialized or not available",
                {"operation": "get_ssh_key_path"},
            )
        return key_path

    def initialize_ssh_key_with_exceptions(self, ssh_key_content: str) -> None:
        """
        Initialize SSH key, raising exception on failure.

        Args:
            ssh_key_content: SSH private key content (base64 encoded or raw)

        Raises:
            SSHConnectionError: If initialization fails
        """
        self.initialize_ssh_key(ssh_key_content, raise_on_error=True)

    def set_ssh_connection_details(self, ssh_host: str, ssh_user: str) -> None:
        """
        Store SSH connection details for host key verification.

        Args:
            ssh_host: The SSH hostname/IP
            ssh_user: The SSH username
        """
        self._ssh_host = ssh_host
        self._ssh_user = ssh_user
        logger.debug(f"SSH connection details stored: {ssh_user}@{ssh_host}")

    async def setup_host_key_verification_if_needed(self) -> None:
        """
        Setup host key verification if SSH connection details are available.
        This is a convenience method that can be called before SSH connections.
        """
        if self._ssh_host and self._ssh_user and self.temp_key_file:
            try:
                await self.setup_host_key_verification(self._ssh_host, self._ssh_user)
                logger.info(
                    f"Host key verification setup completed for {self._ssh_user}@{self._ssh_host}"
                )
            except Exception as e:
                logger.warning(
                    f"Host key verification setup failed for {self._ssh_host}, continuing anyway: {str(e)}"
                )
        else:
            logger.debug(
                "Skipping host key verification setup - missing details or SSH key"
            )

    def cleanup_ssh_key(self):
        """Clean up the global SSH key file securely."""
        if self.temp_key_file and os.path.exists(self.temp_key_file):
            # Check if it's our repository SSH key file (keep it)
            if self.temp_key_file.endswith(
                "mcp_ssh_key.pem"
            ) and not self.temp_key_file.startswith(tempfile.gettempdir()):
                logger.info(f"Keeping SSH key file in repository: {self.temp_key_file}")
                # Don't delete the repository SSH key file, just clear the reference
                self.temp_key_file = None
                return

            # Clean up temporary files
            if self.temp_key_file.startswith(
                tempfile.gettempdir()
            ) or "mcp_ssh_key_" in os.path.basename(self.temp_key_file):
                try:
                    # Overwrite file content with zeros before deletion (security)
                    with open(self.temp_key_file, "r+b") as f:
                        length = f.seek(0, 2)  # Get file size
                        f.seek(0)
                        f.write(b"\x00" * length)  # Overwrite with zeros
                        f.flush()
                        os.fsync(f.fileno())  # Force write to disk

                    # Remove the file
                    os.unlink(self.temp_key_file)
                    logger.info(
                        f"Cleaned up temporary SSH key file: {self.temp_key_file}"
                    )
                except Exception as e:
                    logger.warning(f"Failed to clean up SSH key file: {e}")
                finally:
                    self.temp_key_file = None
            else:
                logger.debug("SSH key is an existing file, not cleaning up")
                self.temp_key_file = None

    def __del__(self):
        """Ensure cleanup on object destruction."""
        self.cleanup_ssh_key()


# Global instance
_global_ssh_manager: Optional[GlobalSSHKeyManager] = None


def get_global_ssh_manager() -> GlobalSSHKeyManager:
    """
    Get the global SSH key manager instance.

    Returns:
        GlobalSSHKeyManager instance
    """
    global _global_ssh_manager
    if _global_ssh_manager is None:
        _global_ssh_manager = GlobalSSHKeyManager()
    return _global_ssh_manager


def initialize_global_ssh_key(ssh_key_content: str) -> None:
    """
    Initialize the global SSH key during application startup.

    Args:
        ssh_key_content: SSH private key content (base64 encoded or raw)
    """
    manager = get_global_ssh_manager()
    manager.initialize_ssh_key(ssh_key_content)


def setup_global_ssh_host_verification(ssh_host: str, ssh_user: str) -> None:
    """
    Setup host key verification for the global SSH manager.

    Args:
        ssh_host: The SSH hostname/IP
        ssh_user: The SSH username
    """
    manager = get_global_ssh_manager()
    manager.set_ssh_connection_details(ssh_host, ssh_user)


async def setup_global_ssh_host_verification_async(
    ssh_host: str, ssh_user: str
) -> None:
    """
    Setup host key verification for the global SSH manager (async version).

    Args:
        ssh_host: The SSH hostname/IP
        ssh_user: The SSH username
    """
    manager = get_global_ssh_manager()
    manager.set_ssh_connection_details(ssh_host, ssh_user)
    await manager.setup_host_key_verification_if_needed()


def cleanup_global_ssh_key() -> None:
    """
    Clean up the global SSH key during application shutdown.
    """
    global _global_ssh_manager
    if _global_ssh_manager is not None:
        _global_ssh_manager.cleanup_ssh_key()
        _global_ssh_manager = None
