# Google Calendar MCP Server

A Model Context Protocol (MCP) server for Google Calendar integration that supports both environment-based and header-based authentication for multi-user scenarios.

## Features

- **Multi-user support**: Per-request authentication via HTTP headers
- **Backward compatibility**: Fallback to environment variables
- **Google Calendar API**: Full CRUD operations for calendar events
- **Streamable HTTP**: Standard HTTP MCP server implementation

## Available Tools

1. **`list_events`** - List upcoming calendar events
2. **`create_event`** - Create a new calendar event
3. **`update_event`** - Update an existing calendar event
4. **`delete_event`** - Delete a calendar event

## Authentication

### Project-Level Credentials (Required at Startup)

Set Google OAuth project credentials via environment variables:

```env
CLIENT_ID=your-google-project-client-id
CLIENT_SECRET=your-google-project-client-secret
PORT=8081
```

### User-Level Authentication (Per Request)

#### Option 1: Headers (Recommended for Multi-User)

Send user-specific OAuth credentials with each request:

```http
x-google-refresh-token: user-specific-refresh-token
```

Or alternatively:

```http
x-google-access-token: user-specific-access-token
```

#### Option 2: Environment Variable (Single User Development)

For single-user development, you can set:

```env
REFRESH_TOKEN=your-user-refresh-token
```

## Running the Server

### Install Dependencies

```bash
npm install
```

### Start Server

```bash
npm start
# or
node index.js
```

### Docker

```bash
docker build -t google-calendar-mcp .
docker run -p 8081:8081 google-calendar-mcp
```

The server will run on `http://localhost:8081` by default.

## Integration

This server is designed to work with MCP execution clients that can:

- Send HTTP requests to MCP servers
- Include custom headers for authentication
- Handle standard MCP protocol responses

Perfect for integration with generalized MCP execution systems that support multiple users and OAuth providers.
