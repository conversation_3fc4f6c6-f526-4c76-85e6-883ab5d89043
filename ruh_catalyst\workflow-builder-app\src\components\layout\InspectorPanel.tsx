// This file is now a wrapper around the refactored InspectorPanel component
// It maintains backward compatibility with existing code that imports from this location
// The actual implementation has been moved to src/components/inspector/InspectorPanel.tsx

import { InspectorPanel as RefactoredInspectorPanel } from "@/components/inspector";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

interface InspectorPanelProps {
  selectedNode: Node<WorkflowNodeData> | null;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
  onClose: () => void;
  onDeleteNode: (nodeId: string) => void;
  edges: Edge[]; // Add edges to track connections
  nodes: Node<WorkflowNodeData>[]; // Add nodes to get source node information
}

export function InspectorPanel(props: InspectorPanelProps) {
  // Simply pass all props to the refactored component
  return <RefactoredInspectorPanel {...props} />;
}
