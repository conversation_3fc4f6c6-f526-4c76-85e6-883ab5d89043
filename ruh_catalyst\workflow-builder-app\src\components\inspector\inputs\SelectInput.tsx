import React from "react";
import { InputDefinition } from "@/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { cn } from "@/lib/utils";

interface SelectInputProps {
  inputDef: InputDefinition;
  value: string;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering select/dropdown inputs
 */
export function SelectInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: SelectInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;

  // Process options from input definition
  const options = (inputDef.options || []).map((option) => {
    if (typeof option === "string") {
      return { value: option, label: option };
    }
    return option;
  });

  return (
    <div className="relative">
      <Select
        value={value ?? ""}
        onValueChange={(value) => onChange(inputDef.name, value)}
        disabled={isDisabled}
      >
        <SelectTrigger
          id={inputId}
          className={cn(
            "bg-background/50 h-8 text-xs",
            isDisabled && "opacity-50"
          )}
        >
          <SelectValue placeholder={`Select ${inputDef.display_name}...`} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value} className="text-xs">
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {isDisabled && isConnected && <ConnectedIndicator />}
    </div>
  );
}
