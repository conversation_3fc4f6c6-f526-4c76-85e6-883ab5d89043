"use server";

import { cookies } from "next/headers";

export const getAccessToken = async () => {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get("accessToken");
  if (accessToken) {
    return accessToken.value;
  } else {
    return "";
  }
};

export const getRefreshToken = async () => {
  const cookieStore = await cookies();
  const refreshToken = cookieStore.get("refreshToken");
  return refreshToken?.value || null;
};

export const checkAccessToken = async () => {
  const cookieStore = await cookies();
  const tokenCookie = cookieStore.get("accessToken");
  return Boolean(tokenCookie?.value);
};

export const setAuthCookies = async (
  accessToken: string,
  refreshToken: string | null,
  accessTokenAge: number,
  refreshTokenAge: number | null,
) => {
  const cookieStore = await cookies();

  // Set access token as a non-HttpOnly cookie (accessible to JavaScript)
  // This is for client-side access to the token for API calls
  cookieStore.set("accessToken", accessToken, {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    httpOnly: false, // Accessible to JavaScript
    sameSite: "lax", // Changed from "none" to "lax" for better CSRF protection
    secure: true,
    maxAge: accessTokenAge,
  });

  // Set a secure HTTP-only cookie for the refresh token
  // This provides better security as it's not accessible to JavaScript
  if (refreshToken && refreshTokenAge) {
    cookieStore.set("refreshToken", refreshToken, {
      path: "/api/auth/refresh", // Restrict to refresh endpoint only
      domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
      httpOnly: true, // Not accessible to JavaScript
      sameSite: "lax", // Changed from "none" to "lax" for better CSRF protection
      secure: true,
      maxAge: refreshTokenAge,
    });
  }
};

export const clearAuthCookies = async () => {
  "use server";

  const cookieStore = await cookies();

  // Clear access token cookie
  cookieStore.set("accessToken", "", {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    httpOnly: false, // Match the setting used when creating the cookie
    secure: true,
    sameSite: "lax", // Match the setting used when creating the cookie
    maxAge: 0,
    expires: new Date(0),
  });

  // Clear refresh token cookie with the specific path used when setting it
  cookieStore.set("refreshToken", "", {
    path: "/api/auth/refresh", // Match the path restriction used when creating the cookie
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    httpOnly: true, // Match the setting used when creating the cookie
    secure: true,
    sameSite: "lax", // Match the setting used when creating the cookie
    maxAge: 0,
    expires: new Date(0),
  });

  // Also clear refresh token with root path as a fallback
  cookieStore.set("refreshToken", "", {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    httpOnly: true,
    secure: true,
    sameSite: "lax",
    maxAge: 0,
    expires: new Date(0),
  });

  console.log("Server-side auth cookies cleared with multiple paths");
};

export const setRefreshingTokenCookie = async () => {
  const cookieStore = await cookies();
  cookieStore.set("refreshingToken", "true", {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    httpOnly: false, // Must be false to be accessible from JavaScript
    sameSite: "none",
    maxAge: 60, // 1 minute should be enough to handle the refresh
  });
};

export const clearRefreshingTokenCookie = async () => {
  const cookieStore = await cookies();
  cookieStore.set("refreshingToken", "", {
    path: "/",
    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
    maxAge: 0,
  });
};
