/**
 * User store for authentication state management
 * Matches the implementation in ruh-app-fe for consistency.
 */

import { create } from "zustand";
import { persist } from "zustand/middleware";

// Define the shape of the user data
export interface User {
  fullName?: string | null;
  email?: string | null;
  company?: string | null;
  department?: string | null;
  jobRole?: string | null;
  accessToken?: string | null;
}

// Define the state structure
interface UserState {
  user: User | null;
  isAuthenticated: boolean;
}

// Define the actions available on the store
interface UserActions {
  setUser: (userData: User | null) => void;
  clearUser: () => void;
  logout: () => Promise<void>;
}

// Create the Zustand store
export const useUserStore = create<UserState & UserActions>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,

      // Actions
      setUser: (userData) =>
        set((state) => ({
          // Merge existing user data with new data if user exists
          user: state.user ? { ...state.user, ...userData } : userData,
          // Update isAuthenticated based on presence of accessToken
          isAuthenticated: userData?.accessToken ? true : false,
        })),

      clearUser: () =>
        set({
          user: null,
          isAuthenticated: false,
        }),

      // Logout action that calls the authApi logout method
      logout: async () => {
        try {
          // Import dynamically to avoid circular dependencies
          const { authApi } = await import("@/lib/authApi");
          await authApi.logout();
        } catch (error) {
          console.error("Error during logout:", error);
          // Clear user state even if logout API fails
          get().clearUser();
        }
      },
    }),
    {
      name: "user-storage", // Unique name for the storage key
      // You can also specify storage: () => sessionStorage or other storage APIs
    },
  ),
);

export default useUserStore;
