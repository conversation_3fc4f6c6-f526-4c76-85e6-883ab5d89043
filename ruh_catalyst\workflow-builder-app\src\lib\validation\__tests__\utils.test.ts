import { describe, it, expect } from "jest";
import { isStartNode, findStartNode, isNodeConnected } from "../utils";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

describe("Validation Utils", () => {
  describe("isStartNode", () => {
    it("should identify a StartNode by originalType", () => {
      const node = {
        id: "node-1",
        data: {
          originalType: "StartNode",
        },
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it("should identify a StartNode by definition name", () => {
      const node = {
        id: "node-2",
        data: {
          definition: {
            name: "StartNode",
          },
        },
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(true);
    });

    it("should return false for non-StartNode", () => {
      const node = {
        id: "node-3",
        data: {
          originalType: "TextNode",
          definition: {
            name: "TextNode",
          },
        },
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(false);
    });

    it("should handle undefined data", () => {
      const node = {
        id: "node-4",
      } as Node<WorkflowNodeData>;
      
      expect(isStartNode(node)).toBe(false);
    });
  });

  describe("findStartNode", () => {
    it("should find the StartNode in an array of nodes", () => {
      const nodes = [
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
      ] as Node<WorkflowNodeData>[];
      
      const startNode = findStartNode(nodes);
      expect(startNode).toBeDefined();
      expect(startNode?.id).toBe("start-node");
    });

    it("should return undefined if no StartNode is found", () => {
      const nodes = [
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
        {
          id: "node-2",
          data: {
            originalType: "NumberNode",
          },
        },
      ] as Node<WorkflowNodeData>[];
      
      const startNode = findStartNode(nodes);
      expect(startNode).toBeUndefined();
    });
  });

  describe("isNodeConnected", () => {
    it("should return true for nodes connected to the start node", () => {
      const nodes = [
        { id: "start-node" },
        { id: "node-1" },
        { id: "node-2" },
      ] as Node<WorkflowNodeData>[];
      
      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
        { id: "edge-2", source: "node-1", target: "node-2" },
      ] as Edge[];
      
      const connectedNodes = new Set(["start-node", "node-1", "node-2"]);
      
      expect(isNodeConnected("node-1", connectedNodes)).toBe(true);
      expect(isNodeConnected("node-2", connectedNodes)).toBe(true);
    });

    it("should return false for nodes not connected to the start node", () => {
      const nodes = [
        { id: "start-node" },
        { id: "node-1" },
        { id: "node-2" },
        { id: "node-3" },
      ] as Node<WorkflowNodeData>[];
      
      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
        { id: "edge-2", source: "node-1", target: "node-2" },
        // node-3 is not connected
      ] as Edge[];
      
      const connectedNodes = new Set(["start-node", "node-1", "node-2"]);
      
      expect(isNodeConnected("node-3", connectedNodes)).toBe(false);
    });
  });
});
