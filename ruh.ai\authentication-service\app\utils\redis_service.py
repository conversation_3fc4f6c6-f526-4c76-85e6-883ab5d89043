"""
Redis Service for OAuth State Management

This module provides Redis integration for managing OAuth state tokens
and temporary data storage in the authentication service.
"""

import json
import redis
from typing import Optional, Dict, Any, Union
from redis.connection import ConnectionPool
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisService:
    """Redis service for OAuth state management and caching."""
    
    def __init__(self):
        """Initialize Redis connection with connection pooling."""
        self._pool = None
        self._redis_client = None
        self._initialize_connection()
    
    def _initialize_connection(self):
        """Initialize Redis connection pool and client."""
        try:
            # Create connection pool
            self._pool = ConnectionPool(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD.get_secret_value() if settings.REDIS_PASSWORD else None,
                max_connections=settings.REDIS_POOL_SIZE,
                decode_responses=True,  # Automatically decode responses to strings
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
            )
            
            # Create Redis client
            self._redis_client = redis.Redis(connection_pool=self._pool)
            
            # Test connection
            self._redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
        except Exception as e:
            logger.error(f"Error initializing Redis service: {e}")
            raise
    
    def set_data_in_redis_with_ttl(
        self, 
        hash_key: str, 
        ttl: int, 
        data: Union[str, Dict[str, Any]]
    ) -> bool:
        """
        Store data in Redis with TTL (Time To Live).
        
        Args:
            hash_key: Redis key
            ttl: Time to live in seconds
            data: Data to store (string or dictionary)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert dict to JSON string if needed
            if isinstance(data, dict):
                data = json.dumps(data)
            
            # Set data with TTL
            result = self._redis_client.setex(hash_key, ttl, data)
            
            if result:
                logger.debug(f"Stored data in Redis with key: {hash_key}, TTL: {ttl}s")
                return True
            else:
                logger.warning(f"Failed to store data in Redis with key: {hash_key}")
                return False
                
        except redis.RedisError as e:
            logger.error(f"Redis error storing data with key {hash_key}: {e}")
            return False
        except json.JSONEncodeError as e:
            logger.error(f"JSON encoding error for key {hash_key}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error storing data in Redis: {e}")
            return False
    
    def get_data_from_redis(self, hash_key: str) -> Optional[str]:
        """
        Retrieve data from Redis.
        
        Args:
            hash_key: Redis key
            
        Returns:
            Data as string if found, None otherwise
        """
        try:
            data = self._redis_client.get(hash_key)
            
            if data is not None:
                logger.debug(f"Retrieved data from Redis with key: {hash_key}")
                return data
            else:
                logger.debug(f"No data found in Redis for key: {hash_key}")
                return None
                
        except redis.RedisError as e:
            logger.error(f"Redis error retrieving data with key {hash_key}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving data from Redis: {e}")
            return None
    
    def get_json_data_from_redis(self, hash_key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve JSON data from Redis and parse it.
        
        Args:
            hash_key: Redis key
            
        Returns:
            Parsed JSON data as dictionary if found, None otherwise
        """
        try:
            data = self.get_data_from_redis(hash_key)
            
            if data is not None:
                return json.loads(data)
            else:
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON decoding error for key {hash_key}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error parsing JSON data from Redis: {e}")
            return None
    
    def delete_data_from_redis(self, hash_key: str) -> bool:
        """
        Delete data from Redis.
        
        Args:
            hash_key: Redis key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self._redis_client.delete(hash_key)
            
            if result > 0:
                logger.debug(f"Deleted data from Redis with key: {hash_key}")
                return True
            else:
                logger.debug(f"No data found to delete in Redis for key: {hash_key}")
                return False
                
        except redis.RedisError as e:
            logger.error(f"Redis error deleting data with key {hash_key}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting data from Redis: {e}")
            return False
    
    def exists(self, hash_key: str) -> bool:
        """
        Check if a key exists in Redis.
        
        Args:
            hash_key: Redis key
            
        Returns:
            True if key exists, False otherwise
        """
        try:
            return bool(self._redis_client.exists(hash_key))
        except redis.RedisError as e:
            logger.error(f"Redis error checking existence of key {hash_key}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking key existence: {e}")
            return False
    
    def get_ttl(self, hash_key: str) -> int:
        """
        Get the TTL (time to live) of a key.
        
        Args:
            hash_key: Redis key
            
        Returns:
            TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
        """
        try:
            return self._redis_client.ttl(hash_key)
        except redis.RedisError as e:
            logger.error(f"Redis error getting TTL for key {hash_key}: {e}")
            return -2
        except Exception as e:
            logger.error(f"Unexpected error getting TTL: {e}")
            return -2
    
    def health_check(self) -> bool:
        """
        Perform a health check on the Redis connection.
        
        Returns:
            True if Redis is healthy, False otherwise
        """
        try:
            response = self._redis_client.ping()
            return response is True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    def close(self):
        """Close Redis connection pool."""
        try:
            if self._pool:
                self._pool.disconnect()
                logger.info("Redis connection pool closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")


# Global Redis service instance
redis_service = RedisService()
