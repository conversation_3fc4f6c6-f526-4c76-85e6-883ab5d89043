#!/usr/bin/env python3
"""
Test the container command detection functionality.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

from app.core_.mcp_executor import MCPExecutor
from app.services.ssh_manager import initialize_global_ssh_key
from app.config.config import settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_container_command_detection():
    """Test the container command detection."""
    print("🧪 Testing Container Command Detection...")
    
    # Initialize global SSH manager
    print("Initializing global SSH manager...")
    initialize_global_ssh_key(settings.ssh_key_content)
    
    # Create MCP executor
    executor = MCPExecutor(producer=None, logger=logger)
    
    # Test container ID
    container_id = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    
    try:
        print(f"🔍 Detecting command for container: {container_id}")
        command = await executor._get_container_command(container_id)
        print(f"✅ Detected command: {command}")
        
        # Verify it's not the default Python command
        if command == "python server.py":
            print("⚠️ Got default command - container command detection may have failed")
        else:
            print("🎉 Successfully detected non-default container command!")
            
        return command
        
    except Exception as e:
        print(f"❌ Error detecting container command: {e}")
        return None


async def main():
    """Main test function."""
    print("🚀 Container Command Detection Test")
    print("=" * 50)
    
    command = await test_container_command_detection()
    
    print("=" * 50)
    print("📊 Test Results:")
    if command and command != "python server.py":
        print("✅ Container command detection working correctly!")
        print(f"   Detected command: {command}")
    else:
        print("❌ Container command detection needs debugging")
        print("   Check SSH connection and container existence")


if __name__ == "__main__":
    asyncio.run(main())
