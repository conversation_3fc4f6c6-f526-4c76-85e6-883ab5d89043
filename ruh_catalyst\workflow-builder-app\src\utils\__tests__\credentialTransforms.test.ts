/**
 * Tests for credential transformation utilities
 * Following TDD methodology - tests written first
 */

import {
  transformCredentialForBackend,
  transformCredentialFromBackend,
  transformCredentialListFromBackend,
  transformCredentialUpdateForBackend,
} from '../credentialTransforms';
import {
  CredentialCreate,
  CredentialUpdate,
  Credential,
  BackendCredentialCreate,
  BackendCredentialUpdate,
  BackendCredentialInfo,
  BackendCredentialListResponse,
} from '../../types/credentials';

describe('credentialTransforms', () => {
  describe('transformCredentialForBackend', () => {
    it('should transform frontend CredentialCreate to backend format', () => {
      const frontendCredential: CredentialCreate = {
        name: 'My API Key',
        value: 'sk-test-123',
        description: 'Test API key for development',
      };

      const result = transformCredentialForBackend(frontendCredential);

      expect(result).toEqual({
        key_name: 'My API Key',
        value: 'sk-test-123',
        description: 'Test API key for development',
      });
    });

    it('should handle credential without description', () => {
      const frontendCredential: CredentialCreate = {
        name: 'Simple Key',
        value: 'simple-value',
      };

      const result = transformCredentialForBackend(frontendCredential);

      expect(result).toEqual({
        key_name: 'Simple Key',
        value: 'simple-value',
        description: undefined,
      });
    });

    it('should handle empty description', () => {
      const frontendCredential: CredentialCreate = {
        name: 'Key with empty desc',
        value: 'value-123',
        description: '',
      };

      const result = transformCredentialForBackend(frontendCredential);

      expect(result).toEqual({
        key_name: 'Key with empty desc',
        value: 'value-123',
        description: '',
      });
    });
  });

  describe('transformCredentialUpdateForBackend', () => {
    it('should transform frontend CredentialUpdate to backend format', () => {
      const frontendUpdate: CredentialUpdate = {
        name: 'Updated Name',
        value: 'updated-value',
        description: 'Updated description',
      };

      const result = transformCredentialUpdateForBackend(frontendUpdate);

      expect(result).toEqual({
        key_name: 'Updated Name',
        value: 'updated-value',
        description: 'Updated description',
      });
    });

    it('should handle partial updates', () => {
      const frontendUpdate: CredentialUpdate = {
        name: 'Only Name Updated',
      };

      const result = transformCredentialUpdateForBackend(frontendUpdate);

      expect(result).toEqual({
        key_name: 'Only Name Updated',
        value: undefined,
        description: undefined,
      });
    });

    it('should handle empty update object', () => {
      const frontendUpdate: CredentialUpdate = {};

      const result = transformCredentialUpdateForBackend(frontendUpdate);

      expect(result).toEqual({
        key_name: undefined,
        value: undefined,
        description: undefined,
      });
    });
  });

  describe('transformCredentialFromBackend', () => {
    it('should transform backend CredentialInfo to frontend format', () => {
      const backendCredential: BackendCredentialInfo = {
        id: 'cred-123',
        key_name: 'Backend Key',
        description: 'Backend description',
        value: 'backend-value',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        last_used_at: '2024-01-03T00:00:00Z',
      };

      const result = transformCredentialFromBackend(backendCredential);

      expect(result).toEqual({
        id: 'cred-123',
        name: 'Backend Key',
        description: 'Backend description',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
        lastUsedAt: '2024-01-03T00:00:00Z',
      });
    });

    it('should handle credential without description', () => {
      const backendCredential: BackendCredentialInfo = {
        id: 'cred-456',
        key_name: 'No Description Key',
        value: 'some-value',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        last_used_at: '2024-01-03T00:00:00Z',
      };

      const result = transformCredentialFromBackend(backendCredential);

      expect(result).toEqual({
        id: 'cred-456',
        name: 'No Description Key',
        description: undefined,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
        lastUsedAt: '2024-01-03T00:00:00Z',
      });
    });

    it('should exclude value from frontend credential', () => {
      const backendCredential: BackendCredentialInfo = {
        id: 'cred-789',
        key_name: 'Secure Key',
        description: 'Should not include value',
        value: 'secret-value-should-not-appear',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        last_used_at: '2024-01-03T00:00:00Z',
      };

      const result = transformCredentialFromBackend(backendCredential);

      expect(result).not.toHaveProperty('value');
      expect(result.name).toBe('Secure Key');
    });
  });

  describe('transformCredentialListFromBackend', () => {
    it('should transform backend credential list response to frontend format', () => {
      const backendResponse: BackendCredentialListResponse = {
        success: true,
        message: 'Credentials retrieved successfully',
        credentials: [
          {
            id: 'cred-1',
            key_name: 'First Key',
            description: 'First description',
            value: 'first-value',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-02T00:00:00Z',
            last_used_at: '2024-01-03T00:00:00Z',
          },
          {
            id: 'cred-2',
            key_name: 'Second Key',
            value: 'second-value',
            created_at: '2024-01-04T00:00:00Z',
            updated_at: '2024-01-05T00:00:00Z',
            last_used_at: '2024-01-06T00:00:00Z',
          },
        ],
      };

      const result = transformCredentialListFromBackend(backendResponse);

      expect(result).toEqual({
        credentials: [
          {
            id: 'cred-1',
            name: 'First Key',
            description: 'First description',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z',
            lastUsedAt: '2024-01-03T00:00:00Z',
          },
          {
            id: 'cred-2',
            name: 'Second Key',
            description: undefined,
            createdAt: '2024-01-04T00:00:00Z',
            updatedAt: '2024-01-05T00:00:00Z',
            lastUsedAt: '2024-01-06T00:00:00Z',
          },
        ],
      });
    });

    it('should handle empty credential list', () => {
      const backendResponse: BackendCredentialListResponse = {
        success: true,
        message: 'No credentials found',
        credentials: [],
      };

      const result = transformCredentialListFromBackend(backendResponse);

      expect(result).toEqual({
        credentials: [],
      });
    });

    it('should exclude values from all credentials in list', () => {
      const backendResponse: BackendCredentialListResponse = {
        success: true,
        message: 'Credentials with values',
        credentials: [
          {
            id: 'cred-1',
            key_name: 'Key 1',
            description: 'Description 1',
            value: 'secret-1',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-02T00:00:00Z',
            last_used_at: '2024-01-03T00:00:00Z',
          },
          {
            id: 'cred-2',
            key_name: 'Key 2',
            description: 'Description 2',
            value: 'secret-2',
            created_at: '2024-01-04T00:00:00Z',
            updated_at: '2024-01-05T00:00:00Z',
            last_used_at: '2024-01-06T00:00:00Z',
          },
        ],
      };

      const result = transformCredentialListFromBackend(backendResponse);

      result.credentials.forEach(credential => {
        expect(credential).not.toHaveProperty('value');
      });
    });
  });
});
