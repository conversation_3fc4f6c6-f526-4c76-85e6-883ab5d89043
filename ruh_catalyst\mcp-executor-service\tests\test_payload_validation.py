#!/usr/bin/env python3
"""
Test script for Task 1.1: Payload Parameter Extraction and Validation

This script tests the new payload validation logic implemented in kafka_service.py
to ensure all required parameters are properly validated and extracted.
"""

import asyncio
import json
import logging
import sys
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Test payloads
VALID_PAYLOAD = {
    "mcp_id": "test-mcp-123",
    "user_id": "user-456",
    "tool_name": "test_tool",
    "tool_parameters": {"param1": "value1", "param2": "value2"},
    "correlation_id": "corr-789",
    "retries": 3,
    "request_id": "req-123",
}

INVALID_PAYLOADS = [
    # Missing mcp_id
    {
        "user_id": "user-456",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
        "request_id": "req-123",
    },
    # Missing user_id
    {
        "mcp_id": "test-mcp-123",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
        "request_id": "req-123",
    },
    # Missing tool_name
    {
        "mcp_id": "test-mcp-123",
        "user_id": "user-456",
        "tool_parameters": {"param1": "value1"},
        "request_id": "req-123",
    },
    # Missing tool_parameters
    {
        "mcp_id": "test-mcp-123",
        "user_id": "user-456",
        "tool_name": "test_tool",
        "request_id": "req-123",
    },
    # Invalid tool_parameters type
    {
        "mcp_id": "test-mcp-123",
        "user_id": "user-456",
        "tool_name": "test_tool",
        "tool_parameters": "invalid_string",
        "request_id": "req-123",
    },
    # Empty string values
    {
        "mcp_id": "",
        "user_id": "user-456",
        "tool_name": "test_tool",
        "tool_parameters": {"param1": "value1"},
        "request_id": "req-123",
    },
]

LEGACY_PAYLOAD_WITH_TOOL_PARAMS = {
    "mcp_id": "test-mcp-123",
    "user_id": "user-456",
    "tool_name": "test_tool",
    "tool_params": {"param1": "value1", "param2": "value2"},  # Legacy field name
    "request_id": "req-123",
}


class MockKafkaMessage:
    """Mock Kafka message for testing"""

    def __init__(
        self,
        payload: Dict[str, Any],
        topic: str = "test-topic",
        partition: int = 0,
        offset: int = 0,
    ):
        self.value = json.dumps(payload).encode("utf-8")
        self.topic = topic
        self.partition = partition
        self.offset = offset


class MockCredentialService:
    """Mock credential service for testing"""

    async def get_oauth_credentials(self, user_id: str, tool_name: str):
        return {"success": True, "access_token": "mock_token", "token_type": "Bearer"}


class MockMCPExecutor:
    """Mock MCP executor for testing"""

    async def execute_tool(self, **kwargs):
        return ["Mock execution result"]


async def test_payload_validation():
    """Test the payload validation logic"""
    logger.info("🧪 Starting payload validation tests...")

    # Import the KafkaMCPService class
    try:
        from app.core_.kafka_service import KafkaMCPService
    except ImportError as e:
        logger.error(f"Failed to import KafkaMCPService: {e}")
        return False

    # Create a mock service instance
    with patch("app.core_.kafka_service.settings") as mock_settings:
        mock_settings.kafka_bootstrap_servers = "localhost:9092"
        mock_settings.kafka_consumer_topic = "test-topic"
        mock_settings.kafka_consumer_group_id = "test-group"
        mock_settings.kafka_results_topic = "test-results"
        mock_settings.max_concurrent_tasks = 10
        mock_settings.default_mcp_retries = 3

        # Create service instance
        service = KafkaMCPService()

        # Mock dependencies
        service.credential_service = MockCredentialService()
        service.mcp_executor = MockMCPExecutor()
        service.producer = AsyncMock()
        service.consumer = AsyncMock()

        # Mock the _commit_offset method
        service._commit_offset = AsyncMock()
        service.send_error_response = AsyncMock()

        # Test 1: Valid payload should succeed
        logger.info("📋 Test 1: Valid payload")
        try:
            msg = MockKafkaMessage(VALID_PAYLOAD)
            await service.process_message(msg, AsyncMock())
            logger.info("✅ Valid payload test passed")
        except Exception as e:
            logger.error(f"❌ Valid payload test failed: {e}")
            return False

        # Test 2: Invalid payloads should fail
        logger.info("📋 Test 2: Invalid payloads")
        for i, invalid_payload in enumerate(INVALID_PAYLOADS):
            try:
                msg = MockKafkaMessage(invalid_payload)
                await service.process_message(msg, AsyncMock())

                # Check if error response was sent
                if service.send_error_response.called:
                    logger.info(f"✅ Invalid payload {i+1} correctly rejected")
                else:
                    logger.error(f"❌ Invalid payload {i+1} was not rejected")
                    return False

                # Reset mock
                service.send_error_response.reset_mock()

            except Exception as e:
                logger.info(f"✅ Invalid payload {i+1} correctly failed with: {e}")

        # Test 3: Legacy tool_params field
        logger.info("📋 Test 3: Legacy tool_params field")
        try:
            msg = MockKafkaMessage(LEGACY_PAYLOAD_WITH_TOOL_PARAMS)
            await service.process_message(msg, AsyncMock())
            logger.info("✅ Legacy tool_params field test passed")
        except Exception as e:
            logger.error(f"❌ Legacy tool_params field test failed: {e}")
            return False

    logger.info("🎉 All payload validation tests completed successfully!")
    return True


async def main():
    """Main test function"""
    logger.info("🚀 Starting Task 1.1 validation tests...")

    success = await test_payload_validation()

    if success:
        logger.info(
            "✅ All tests passed! Task 1.1 implementation is working correctly."
        )
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed! Please check the implementation.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
