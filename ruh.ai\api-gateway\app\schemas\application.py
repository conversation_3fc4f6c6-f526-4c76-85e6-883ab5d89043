from enum import Enum
from typing import Optional, List, Union
from pydantic import BaseModel, Field, field_validator


# Enums matching proto definitions
class ApplicationStatusEnum(str, Enum):
    UNSPECIFIED = "unspecified"
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


# Base models
class BaseResponse(BaseModel):
    success: bool
    message: str


# Application models matching proto definitions
class CreateApplicationRequest(BaseModel):
    user_id: str
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., max_length=1000)
    workflow_ids: List[str] = Field(default_factory=list)
    agent_ids: List[str] = Field(default_factory=list)
    api_keys: List[str] = Field(default_factory=list)


class UpdateApplicationRequest(BaseModel):
    application_id: str
    user_id: str
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    workflow_ids: Optional[List[str]] = None
    agent_ids: Optional[List[str]] = None
    status: Optional[ApplicationStatusEnum] = None
    api_keys: Optional[List[str]] = None


class GetApplicationsRequest(BaseModel):
    user_id: str
    status: Optional[ApplicationStatusEnum] = None
    limit: int = 50
    offset: int = 0


class GetApplicationRequest(BaseModel):
    application_id: str
    user_id: str


class DeleteApplicationRequest(BaseModel):
    application_id: str
    user_id: str


class AttachImageToApplicationRequest(BaseModel):
    application_id: str
    user_id: str
    image_name: str
    image_type: str
    image_data: bytes
    description: Optional[str] = None


class TimeSeriesDataPoint(BaseModel):
    date: str
    count: int


class ApplicationMetrics(BaseModel):
    application_id: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    credits_used: float
    last_request_at: str
    usage_trend: List[TimeSeriesDataPoint] = Field(default_factory=list)


class Application(BaseModel):
    id: str
    user_id: str
    name: str
    description: str
    workflow_ids: List[str] = Field(default_factory=list)
    agent_ids: List[str] = Field(default_factory=list)
    status: ApplicationStatusEnum
    created_at: str
    updated_at: str
    api_keys: List[str] = Field(default_factory=list)
    is_deleted: bool = False

    @field_validator("status", mode="before")
    @classmethod
    def convert_status(cls, v):
        """Convert integer status values from gRPC to string enum values."""
        if isinstance(v, int):
            # Map integer values to enum string values based on proto definition
            status_map = {0: "unspecified", 1: "active", 2: "inactive", 3: "suspended"}
            return status_map.get(v, "unspecified")
        return v

    class Config:
        from_attributes = True


# Response models matching proto definitions
class CreateApplicationResponse(BaseResponse):
    application: Optional[Application] = None


class GetApplicationsResponse(BaseResponse):
    applications: List[Application] = Field(default_factory=list)
    total_count: int = 0


class GetApplicationResponse(BaseResponse):
    application: Optional[Application] = None
    metrics: Optional[ApplicationMetrics] = None


class UpdateApplicationResponse(BaseResponse):
    application: Optional[Application] = None


class DeleteApplicationResponse(BaseResponse):
    pass


class AttachImageToApplicationResponse(BaseResponse):
    image_id: Optional[str] = None
    image_url: Optional[str] = None


# ===== LEGACY MODELS FOR BACKWARD COMPATIBILITY =====


class ApplicationCreate(BaseModel):
    """Legacy model for backward compatibility."""

    user_id: str
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., max_length=1000)
    workflow_ids: List[str] = Field(default_factory=list)
    agent_ids: List[str] = Field(default_factory=list)


class ApplicationUpdate(BaseModel):
    """Legacy model for backward compatibility."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    workflow_ids: Optional[List[str]] = None
    agent_ids: Optional[List[str]] = None
    status: Optional[ApplicationStatusEnum] = None


class ApplicationResponse(BaseResponse):
    """Legacy response model for backward compatibility."""

    application: Optional[Application] = None


class ApplicationWithMetricsResponse(BaseResponse):
    """Legacy response model for backward compatibility."""

    application: Optional[Application] = None
    metrics: Optional[ApplicationMetrics] = None


# Pagination models
class PaginationMetadata(BaseModel):
    total: int
    total_pages: int
    current_page: int
    page_size: int
    has_next_page: bool
    has_previous_page: bool


class PaginatedApplicationResponse(BaseModel):
    data: List[Application]
    metadata: PaginationMetadata


# Image attachment models
class AttachImageRequest(BaseModel):
    image_name: str = Field(..., min_length=1)
    image_type: str = Field(..., pattern=r"^image/(jpeg|jpg|png|gif|webp)$")
    description: Optional[str] = Field(None, max_length=500)


# Query parameter models for filtering
class ApplicationFilters(BaseModel):
    status: Optional[ApplicationStatusEnum] = None
    limit: int = Field(50, ge=1, le=100)
    offset: int = Field(0, ge=0)


# Bulk operations
class BulkDeleteRequest(BaseModel):
    application_ids: List[str] = Field(..., min_items=1, max_items=50)


class BulkDeleteResponse(BaseResponse):
    deleted_count: int = 0
    failed_deletions: List[str] = Field(default_factory=list)


class BulkUpdateStatusRequest(BaseModel):
    application_ids: List[str] = Field(..., min_items=1, max_items=50)
    status: ApplicationStatusEnum


class BulkUpdateStatusResponse(BaseResponse):
    updated_count: int = 0
    failed_updates: List[str] = Field(default_factory=list)


# Statistics models
class ApplicationStats(BaseModel):
    total_applications: int
    active_applications: int
    inactive_applications: int
    suspended_applications: int
    total_requests_today: int
    total_credits_used_today: float
    average_requests_per_app: float


class ApplicationStatsResponse(BaseResponse):
    stats: Optional[ApplicationStats] = None


# Search models
class ApplicationSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=100)
    status: Optional[ApplicationStatusEnum] = None
    limit: int = Field(20, ge=1, le=100)
    offset: int = Field(0, ge=0)


class ApplicationSearchResponse(BaseResponse):
    applications: List[Application] = Field(default_factory=list)
    total_count: int = 0
    search_query: str


# Export models
class ExportApplicationsRequest(BaseModel):
    status: Optional[ApplicationStatusEnum] = None
    format: str = Field("csv", pattern=r"^(csv|json|xlsx)$")
    include_metrics: bool = False


class ExportApplicationsResponse(BaseResponse):
    download_url: Optional[str] = None
    file_name: Optional[str] = None
    expires_at: Optional[str] = None


# Application template models (for future use)
class ApplicationTemplate(BaseModel):
    id: str
    name: str
    description: str
    category: str
    default_workflow_ids: List[str] = Field(default_factory=list)
    default_agent_ids: List[str] = Field(default_factory=list)
    created_at: str
    updated_at: str


class CreateFromTemplateRequest(BaseModel):
    template_id: str
    name: str
    description: Optional[str] = None
    customize_workflows: bool = False
    customize_agents: bool = False


class CreateFromTemplateResponse(BaseResponse):
    application: Optional[Application] = None


# Health check models
class ApplicationHealthCheck(BaseModel):
    application_id: str
    is_healthy: bool
    last_check: str
    error_message: Optional[str] = None
    response_time_ms: Optional[int] = None


class ApplicationHealthResponse(BaseResponse):
    health_check: Optional[ApplicationHealthCheck] = None


class BulkHealthCheckResponse(BaseResponse):
    health_checks: List[ApplicationHealthCheck] = Field(default_factory=list)
    healthy_count: int = 0
    unhealthy_count: int = 0
