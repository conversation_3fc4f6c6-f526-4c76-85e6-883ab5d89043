import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";

interface CustomScrollAreaProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  children: React.ReactNode;
}

const CustomScrollArea = forwardRef<HTMLDivElement, CustomScrollAreaProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("custom-scrollbar relative overflow-auto", className)}
        {...props}
      >
        {children}
      </div>
    );
  },
);

CustomScrollArea.displayName = "CustomScrollArea";

export { CustomScrollArea };
