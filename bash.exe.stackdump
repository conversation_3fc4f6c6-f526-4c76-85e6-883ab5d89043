Stack trace:
Frame         Function      Args
0007FFFFBE40  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBE40, 0007FFFFAD40) msys-2.0.dll+0x1FE8E
0007FFFFBE40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC118) msys-2.0.dll+0x67F9
0007FFFFBE40  000210046832 (000210286019, 0007FFFFBCF8, 0007FFFFBE40, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE40  000210068E24 (0007FFFFBE50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC120  00021006A225 (0007FFFFBE50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEF2820000 ntdll.dll
7FFEF1B00000 KERNEL32.DLL
7FFEEFE80000 KERNELBASE.dll
7FFEF1930000 USER32.dll
7FFEEFCD0000 win32u.dll
7FFEF0DB0000 GDI32.dll
7FFEEF970000 gdi32full.dll
7FFEF0510000 msvcp_win.dll
7FFEF03C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEF1D80000 advapi32.dll
7FFEF1130000 msvcrt.dll
7FFEF0A40000 sechost.dll
7FFEF1C60000 RPCRT4.dll
7FFEEEE80000 CRYPTBASE.DLL
7FFEEFC30000 bcryptPrimitives.dll
7FFEF2710000 IMM32.DLL
