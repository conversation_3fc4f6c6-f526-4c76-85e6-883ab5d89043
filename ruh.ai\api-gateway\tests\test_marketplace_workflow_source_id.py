import pytest
from unittest.mock import AsyncMock, MagicMock
from app.api.routers.marketplace_routes import get_marketplace_workflow_detail
from app.schemas.marketplace import MarketplaceWorkflowDetailResponse, MarketplaceWorkflowDetail


@pytest.mark.asyncio
async def test_get_marketplace_workflow_detail_includes_source_workflow_id():
    """Test that get_marketplace_workflow_detail includes source_workflow_id in response"""
    
    # Mock the workflow service response
    mock_response = MagicMock()
    mock_response.success = True
    mock_response.template = MagicMock()
    
    # Mock template data with source_workflow_id
    template_data = {
        "id": "test-workflow-123",
        "name": "Test Workflow",
        "description": "A test workflow",
        "workflow_url": "https://example.com/workflow.json",
        "builder_url": "https://example.com/builder.json",
        "owner_id": "user-123",
        "category": "automation",
        "version": "1.0.0",
        "status": "ACTIVE",
        "visibility": "PUBLIC",
        "source_workflow_id": "original-workflow-456",  # This is the new field
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "use_count": 5,
        "execution_count": 10,
        "tags": ["automation", "test"],
        "start_nodes": [],
        "workflow_definition": {},
        "workflow_steps": [],
        "available_nodes": []
    }
    
    # Mock the MessageToDict function to return our test data
    def mock_message_to_dict(message, preserving_proto_field_name=True):
        return template_data
    
    # Mock the workflow service
    mock_workflow_service = AsyncMock()
    mock_workflow_service.get_template.return_value = mock_response
    
    # Mock the user service
    mock_user_service = AsyncMock()
    mock_user_service.get_users_by_ids.return_value = {
        "user-123": {"full_name": "Test User"}
    }
    
    # Mock the prepare_workflow_dict function
    def mock_prepare_workflow_dict(data):
        return data
    
    # Patch the dependencies
    import app.api.routers.marketplace_routes as marketplace_routes
    original_workflow_service = marketplace_routes.workflow_service
    original_user_service = marketplace_routes.user_service
    original_message_to_dict = marketplace_routes.MessageToDict
    original_prepare_workflow_dict = marketplace_routes.prepare_workflow_dict
    
    try:
        marketplace_routes.workflow_service = mock_workflow_service
        marketplace_routes.user_service = mock_user_service
        marketplace_routes.MessageToDict = mock_message_to_dict
        marketplace_routes.prepare_workflow_dict = mock_prepare_workflow_dict
        
        # Call the function
        result = await get_marketplace_workflow_detail("test-workflow-123", None)
        
        # Verify the response
        assert isinstance(result, MarketplaceWorkflowDetailResponse)
        assert result.success is True
        assert result.message == "Workflow details retrieved successfully"
        
        # Verify the workflow detail includes source_workflow_id
        workflow_detail = result.workflow
        assert isinstance(workflow_detail, MarketplaceWorkflowDetail)
        assert workflow_detail.id == "test-workflow-123"
        assert workflow_detail.name == "Test Workflow"
        assert hasattr(workflow_detail, 'source_workflow_id')
        assert workflow_detail.source_workflow_id == "original-workflow-456"
        
        # Verify other fields are present
        assert workflow_detail.owner_name == "Test User"
        assert workflow_detail.use_count == 5
        assert workflow_detail.execution_count == 10
        
    finally:
        # Restore original dependencies
        marketplace_routes.workflow_service = original_workflow_service
        marketplace_routes.user_service = original_user_service
        marketplace_routes.MessageToDict = original_message_to_dict
        marketplace_routes.prepare_workflow_dict = original_prepare_workflow_dict


@pytest.mark.asyncio
async def test_get_marketplace_workflow_detail_handles_missing_source_workflow_id():
    """Test that get_marketplace_workflow_detail handles missing source_workflow_id gracefully"""
    
    # Mock the workflow service response
    mock_response = MagicMock()
    mock_response.success = True
    mock_response.template = MagicMock()
    
    # Mock template data without source_workflow_id
    template_data = {
        "id": "test-workflow-123",
        "name": "Test Workflow",
        "description": "A test workflow",
        "workflow_url": "https://example.com/workflow.json",
        "builder_url": "https://example.com/builder.json",
        "owner_id": "user-123",
        "category": "automation",
        "version": "1.0.0",
        "status": "ACTIVE",
        "visibility": "PUBLIC",
        # source_workflow_id is missing
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "use_count": 5,
        "execution_count": 10,
        "tags": ["automation", "test"],
        "start_nodes": [],
        "workflow_definition": {},
        "workflow_steps": [],
        "available_nodes": []
    }
    
    # Mock the MessageToDict function to return our test data
    def mock_message_to_dict(message, preserving_proto_field_name=True):
        return template_data
    
    # Mock the workflow service
    mock_workflow_service = AsyncMock()
    mock_workflow_service.get_template.return_value = mock_response
    
    # Mock the user service
    mock_user_service = AsyncMock()
    mock_user_service.get_users_by_ids.return_value = {
        "user-123": {"full_name": "Test User"}
    }
    
    # Mock the prepare_workflow_dict function
    def mock_prepare_workflow_dict(data):
        return data
    
    # Patch the dependencies
    import app.api.routers.marketplace_routes as marketplace_routes
    original_workflow_service = marketplace_routes.workflow_service
    original_user_service = marketplace_routes.user_service
    original_message_to_dict = marketplace_routes.MessageToDict
    original_prepare_workflow_dict = marketplace_routes.prepare_workflow_dict
    
    try:
        marketplace_routes.workflow_service = mock_workflow_service
        marketplace_routes.user_service = mock_user_service
        marketplace_routes.MessageToDict = mock_message_to_dict
        marketplace_routes.prepare_workflow_dict = mock_prepare_workflow_dict
        
        # Call the function
        result = await get_marketplace_workflow_detail("test-workflow-123", None)
        
        # Verify the response
        assert isinstance(result, MarketplaceWorkflowDetailResponse)
        assert result.success is True
        
        # Verify the workflow detail handles missing source_workflow_id
        workflow_detail = result.workflow
        assert isinstance(workflow_detail, MarketplaceWorkflowDetail)
        assert workflow_detail.id == "test-workflow-123"
        assert hasattr(workflow_detail, 'source_workflow_id')
        assert workflow_detail.source_workflow_id is None  # Should be None when missing
        
    finally:
        # Restore original dependencies
        marketplace_routes.workflow_service = original_workflow_service
        marketplace_routes.user_service = original_user_service
        marketplace_routes.MessageToDict = original_message_to_dict
        marketplace_routes.prepare_workflow_dict = original_prepare_workflow_dict


def test_marketplace_workflow_detail_schema_has_source_workflow_id():
    """Test that MarketplaceWorkflowDetail schema includes source_workflow_id field"""
    
    # Test data with source_workflow_id
    workflow_data = {
        "id": "test-workflow-123",
        "name": "Test Workflow",
        "description": "A test workflow",
        "workflow_url": "https://example.com/workflow.json",
        "builder_url": "https://example.com/builder.json",
        "owner_id": "user-123",
        "category": "automation",
        "version": "1.0.0",
        "status": "ACTIVE",
        "visibility": "PUBLIC",
        "source_workflow_id": "original-workflow-456",
        "use_count": 5,
        "execution_count": 10,
        "tags": ["automation", "test"],
        "start_nodes": [],
        "workflow_definition": {},
        "workflow_steps": [],
        "available_nodes": []
    }
    
    # Create MarketplaceWorkflowDetail instance
    workflow_detail = MarketplaceWorkflowDetail(**workflow_data)
    
    # Verify the field exists and has the correct value
    assert hasattr(workflow_detail, 'source_workflow_id')
    assert workflow_detail.source_workflow_id == "original-workflow-456"
    assert workflow_detail.id == "test-workflow-123"
    assert workflow_detail.name == "Test Workflow"


def test_marketplace_workflow_detail_schema_optional_source_workflow_id():
    """Test that MarketplaceWorkflowDetail schema handles optional source_workflow_id"""
    
    # Test data without source_workflow_id
    workflow_data = {
        "id": "test-workflow-123",
        "name": "Test Workflow",
        "description": "A test workflow",
        "workflow_url": "https://example.com/workflow.json",
        "builder_url": "https://example.com/builder.json",
        "owner_id": "user-123",
        "category": "automation",
        "version": "1.0.0",
        "status": "ACTIVE",
        "visibility": "PUBLIC",
        # source_workflow_id is not provided
        "use_count": 5,
        "execution_count": 10,
        "tags": ["automation", "test"],
        "start_nodes": [],
        "workflow_definition": {},
        "workflow_steps": [],
        "available_nodes": []
    }
    
    # Create MarketplaceWorkflowDetail instance
    workflow_detail = MarketplaceWorkflowDetail(**workflow_data)
    
    # Verify the field exists and is None when not provided
    assert hasattr(workflow_detail, 'source_workflow_id')
    assert workflow_detail.source_workflow_id is None
    assert workflow_detail.id == "test-workflow-123"
    assert workflow_detail.name == "Test Workflow"