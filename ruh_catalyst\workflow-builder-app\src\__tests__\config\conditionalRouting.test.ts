/**
 * Test suite for conditional routing feature flags and configuration.
 * 
 * This module tests the frontend feature flag functionality:
 * - Environment variable detection for conditional routing mode
 * - Feature flag functions for component vs embedded mode
 * - Default fallback behavior
 * - Invalid value handling
 * 
 * Following TDD methodology - Phase 4 Cycle 1: Feature Flag and Environment Setup
 */

import { 
  getConditionalRoutingMode, 
  shouldUseComponentMode,
  CONDITIONAL_ROUTING_FEATURES 
} from '@/config/conditionalRouting';

describe('Conditional Routing Feature Flags', () => {
  // Store original environment variable
  const originalEnv = process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE;

  beforeEach(() => {
    // Clear environment variables before each test
    delete process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE;
    delete process.env.NEXT_PUBLIC_SHOW_CONDITIONAL_MODE_TOGGLE;
  });

  afterAll(() => {
    // Restore original environment variable
    if (originalEnv !== undefined) {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = originalEnv;
    }
  });

  describe('getConditionalRoutingMode', () => {
    test('should always return component mode (embedded mode removed)', () => {
      expect(getConditionalRoutingMode()).toBe('component');
    });

    test('should return component mode regardless of environment variable', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = 'embedded';
      expect(getConditionalRoutingMode()).toBe('component');
    });

    test('should return component mode for any environment variable value', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = 'invalid';
      expect(getConditionalRoutingMode()).toBe('component');
    });

    test('should return component mode for empty string environment variable', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = '';
      expect(getConditionalRoutingMode()).toBe('component');
    });

    test('should return component mode regardless of case', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = 'COMPONENT';
      expect(getConditionalRoutingMode()).toBe('component');
    });
  });

  describe('shouldUseComponentMode', () => {
    test('should always return true (component mode is the only supported mode)', () => {
      expect(shouldUseComponentMode()).toBe(true);
    });

    test('should return true regardless of environment variable', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = 'embedded';
      expect(shouldUseComponentMode()).toBe(true);
    });

    test('should return true for any environment variable value', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = 'invalid';
      expect(shouldUseComponentMode()).toBe(true);
    });
  });

  describe('CONDITIONAL_ROUTING_FEATURES', () => {
    test('should have correct values (component backend, legacy UI)', () => {
      // Re-import to get fresh values
      jest.resetModules();
      const { CONDITIONAL_ROUTING_FEATURES, CONDITIONAL_COMPONENT_UI_CONFIG } = require('@/config/conditionalRouting');

      expect(CONDITIONAL_ROUTING_FEATURES.MODE).toBe('component');
      expect(CONDITIONAL_ROUTING_FEATURES.COMPONENT_MODE_ENABLED).toBe(true);
      expect(CONDITIONAL_ROUTING_FEATURES.SHOW_MODE_TOGGLE).toBe(false);

      // Enhanced UI features should be disabled (using legacy inspector)
      expect(CONDITIONAL_COMPONENT_UI_CONFIG.ENHANCED_INSPECTOR).toBe(false);
      expect(CONDITIONAL_COMPONENT_UI_CONFIG.SHOW_MODE_INDICATORS).toBe(false);
      expect(CONDITIONAL_COMPONENT_UI_CONFIG.SHOW_HANDLE_INFO).toBe(false);
      expect(CONDITIONAL_COMPONENT_UI_CONFIG.SHOW_ROUTING_VISUALIZATION).toBe(false);
    });

    test('should always use component backend with legacy UI', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = 'embedded';

      // Re-import to get fresh values
      jest.resetModules();
      const { CONDITIONAL_ROUTING_FEATURES, CONDITIONAL_COMPONENT_UI_CONFIG } = require('@/config/conditionalRouting');

      // Backend should still be component mode
      expect(CONDITIONAL_ROUTING_FEATURES.MODE).toBe('component');
      expect(CONDITIONAL_ROUTING_FEATURES.COMPONENT_MODE_ENABLED).toBe(true);

      // UI should still be legacy (enhanced features disabled)
      expect(CONDITIONAL_COMPONENT_UI_CONFIG.ENHANCED_INSPECTOR).toBe(false);
    });
  });

  describe('Type Safety', () => {
    test('should return valid ConditionalRoutingMode type (component only)', () => {
      const mode = getConditionalRoutingMode();
      expect(mode).toBe('component');
    });

    test('should return boolean for shouldUseComponentMode', () => {
      const result = shouldUseComponentMode();
      expect(typeof result).toBe('boolean');
      expect(result).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    test('should handle undefined environment variable', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = undefined;
      expect(getConditionalRoutingMode()).toBe('component');
      expect(shouldUseComponentMode()).toBe(true);
    });

    test('should handle whitespace in environment variable', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = ' component ';
      expect(getConditionalRoutingMode()).toBe('component');
      expect(shouldUseComponentMode()).toBe(true);
    });

    test('should handle numeric values in environment variable', () => {
      process.env.NEXT_PUBLIC_CONDITIONAL_ROUTING_MODE = '1';
      expect(getConditionalRoutingMode()).toBe('component');
      expect(shouldUseComponentMode()).toBe(true);
    });
  });

  describe('Consistency', () => {
    test('shouldUseComponentMode should be consistent with getConditionalRoutingMode', () => {
      // Both should always return component mode values
      expect(shouldUseComponentMode()).toBe(getConditionalRoutingMode() === 'component');
      expect(shouldUseComponentMode()).toBe(true);
      expect(getConditionalRoutingMode()).toBe('component');
    });

    test('CONDITIONAL_ROUTING_FEATURES should be consistent with individual functions', () => {
      // Re-import to get fresh values
      jest.resetModules();
      const {
        getConditionalRoutingMode,
        shouldUseComponentMode,
        CONDITIONAL_ROUTING_FEATURES
      } = require('@/config/conditionalRouting');

      expect(CONDITIONAL_ROUTING_FEATURES.MODE).toBe(getConditionalRoutingMode());
      expect(CONDITIONAL_ROUTING_FEATURES.COMPONENT_MODE_ENABLED).toBe(shouldUseComponentMode());
      expect(CONDITIONAL_ROUTING_FEATURES.MODE).toBe('component');
      expect(CONDITIONAL_ROUTING_FEATURES.COMPONENT_MODE_ENABLED).toBe(true);
    });
  });
});

describe('Integration with Features Config', () => {
  test('should integrate with existing features configuration', () => {
    // This test ensures our conditional routing features don't conflict with existing features
    const { FEATURES } = require('@/config/features');
    const { CONDITIONAL_ROUTING_FEATURES } = require('@/config/conditionalRouting');
    
    // Should not have conflicting property names
    const featureKeys = Object.keys(FEATURES);
    const conditionalKeys = Object.keys(CONDITIONAL_ROUTING_FEATURES);
    
    const intersection = featureKeys.filter(key => conditionalKeys.includes(key));
    expect(intersection).toHaveLength(0);
  });
});
