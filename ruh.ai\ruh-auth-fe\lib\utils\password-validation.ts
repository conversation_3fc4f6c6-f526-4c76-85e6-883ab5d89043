import { signupSchema } from "@/lib/schemas/auth";
import { z } from "zod";

// Extract the password schema from the signup schema
export const passwordSchema = signupSchema.shape.password;

// Function to validate a password against our schema
export function validatePassword(password: string) {
  // Create a validation result object
  const validations = {
    length: false,
    hasNumber: false,
    hasSymbol: false,
    hasUppercase: false,
  };

  // Check length requirement (6-15 characters)
  validations.length = password.length >= 6 && password.length <= 15;

  // Check number requirement
  validations.hasNumber = /[0-9]/.test(password);

  // Check symbol requirement
  validations.hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  // Check uppercase requirement
  validations.hasUppercase = /[A-Z]/.test(password);

  return validations;
}

// Function to check if a password is fully valid
export function isPasswordValid(password: string) {
  try {
    passwordSchema.parse(password);
    return true;
  } catch (error) {
    return false;
  }
}
