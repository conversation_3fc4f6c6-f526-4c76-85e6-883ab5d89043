/**
 * Tests for CredentialService
 * Following TDD methodology - tests written first
 */

import { CredentialService } from '../credentialService';
import { createAxiosInstance } from '../../utils/axios';
import { API_ENDPOINTS } from '../../lib/apiConfig';
import {
  CredentialCreate,
  CredentialUpdate,
  Credential,
  CredentialListResponse,
  BackendCredentialListResponse,
  BackendCredentialInfo,
  BackendCredentialResponse,
  CredentialErrorType,
} from '../../types/credentials';

// Mock axios
jest.mock('../../utils/axios');
const mockCreateAxiosInstance = createAxiosInstance as jest.MockedFunction<typeof createAxiosInstance>;

describe('CredentialService', () => {
  let credentialService: CredentialService;
  let mockAxios: any;

  beforeEach(() => {
    mockAxios = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    };
    
    mockCreateAxiosInstance.mockReturnValue(mockAxios);
    credentialService = new CredentialService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchCredentials', () => {
    it('should fetch and transform credentials successfully', async () => {
      const mockBackendResponse: BackendCredentialListResponse = {
        success: true,
        message: 'Credentials retrieved successfully',
        credentials: [
          {
            id: 'cred-1',
            key_name: 'Test API Key',
            description: 'Test description',
            value: 'secret-value',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-02T00:00:00Z',
            last_used_at: '2024-01-03T00:00:00Z',
          },
        ],
      };

      mockAxios.get.mockResolvedValue({ data: mockBackendResponse });

      const result = await credentialService.fetchCredentials();

      expect(mockAxios.get).toHaveBeenCalledWith(API_ENDPOINTS.CREDENTIALS.LIST);
      expect(result).toEqual({
        credentials: [
          {
            id: 'cred-1',
            name: 'Test API Key',
            description: 'Test description',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z',
            lastUsedAt: '2024-01-03T00:00:00Z',
          },
        ],
      });
    });

    it('should handle empty credential list', async () => {
      const mockBackendResponse: BackendCredentialListResponse = {
        success: true,
        message: 'No credentials found',
        credentials: [],
      };

      mockAxios.get.mockResolvedValue({ data: mockBackendResponse });

      const result = await credentialService.fetchCredentials();

      expect(result).toEqual({ credentials: [] });
    });

    it('should handle API errors', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      };

      mockAxios.get.mockRejectedValue(mockError);

      await expect(credentialService.fetchCredentials()).rejects.toMatchObject({
        type: CredentialErrorType.AUTHENTICATION_ERROR,
        message: 'Authentication required. Please log in again.',
      });
    });
  });

  describe('createCredential', () => {
    it('should create credential successfully', async () => {
      const credentialData: CredentialCreate = {
        name: 'New API Key',
        value: 'new-secret-value',
        description: 'New description',
      };

      const mockBackendResponse: BackendCredentialResponse = {
        success: true,
        message: 'Credential created successfully',
        id: 'new-cred-id',
        key_name: 'New API Key',
      };

      const mockDetailResponse: BackendCredentialInfo = {
        id: 'new-cred-id',
        key_name: 'New API Key',
        description: 'New description',
        value: 'new-secret-value',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        last_used_at: '2024-01-01T00:00:00Z',
      };

      mockAxios.post.mockResolvedValue({ data: mockBackendResponse });
      mockAxios.get.mockResolvedValue({ 
        data: { 
          success: true, 
          message: 'Credential retrieved', 
          credential: mockDetailResponse 
        } 
      });

      const result = await credentialService.createCredential(credentialData);

      expect(mockAxios.post).toHaveBeenCalledWith(API_ENDPOINTS.CREDENTIALS.CREATE, {
        key_name: 'New API Key',
        value: 'new-secret-value',
        description: 'New description',
      });

      expect(result).toEqual({
        id: 'new-cred-id',
        name: 'New API Key',
        description: 'New description',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        lastUsedAt: '2024-01-01T00:00:00Z',
      });
    });

    it('should handle validation errors', async () => {
      const credentialData: CredentialCreate = {
        name: '',
        value: 'some-value',
      };

      await expect(credentialService.createCredential(credentialData)).rejects.toMatchObject({
        type: CredentialErrorType.VALIDATION_ERROR,
        message: 'Invalid credential data provided',
        field: 'name',
      });
    });
  });

  describe('getCredential', () => {
    it('should get credential by ID successfully', async () => {
      const credentialId = 'cred-123';
      const mockBackendCredential: BackendCredentialInfo = {
        id: credentialId,
        key_name: 'Retrieved Key',
        description: 'Retrieved description',
        value: 'retrieved-value',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        last_used_at: '2024-01-03T00:00:00Z',
      };

      mockAxios.get.mockResolvedValue({
        data: {
          success: true,
          message: 'Credential retrieved',
          credential: mockBackendCredential,
        },
      });

      const result = await credentialService.getCredential(credentialId);

      expect(mockAxios.get).toHaveBeenCalledWith(API_ENDPOINTS.CREDENTIALS.GET(credentialId));
      expect(result).toEqual({
        id: credentialId,
        name: 'Retrieved Key',
        description: 'Retrieved description',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
        lastUsedAt: '2024-01-03T00:00:00Z',
      });
    });

    it('should handle not found errors', async () => {
      const credentialId = 'non-existent-id';
      const mockError = {
        response: {
          status: 404,
          data: { message: 'Credential not found' },
        },
      };

      mockAxios.get.mockRejectedValue(mockError);

      await expect(credentialService.getCredential(credentialId)).rejects.toMatchObject({
        type: CredentialErrorType.NOT_FOUND_ERROR,
        message: 'Credential not found.',
      });
    });
  });

  describe('updateCredential', () => {
    it('should update credential successfully', async () => {
      const credentialId = 'cred-123';
      const updateData: CredentialUpdate = {
        name: 'Updated Name',
        description: 'Updated description',
      };

      const mockBackendResponse: BackendCredentialResponse = {
        success: true,
        message: 'Credential updated successfully',
        id: credentialId,
        key_name: 'Updated Name',
      };

      const mockDetailResponse: BackendCredentialInfo = {
        id: credentialId,
        key_name: 'Updated Name',
        description: 'Updated description',
        value: 'existing-value',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        last_used_at: '2024-01-03T00:00:00Z',
      };

      mockAxios.put.mockResolvedValue({ data: mockBackendResponse });
      mockAxios.get.mockResolvedValue({
        data: {
          success: true,
          message: 'Credential retrieved',
          credential: mockDetailResponse,
        },
      });

      const result = await credentialService.updateCredential(credentialId, updateData);

      expect(mockAxios.put).toHaveBeenCalledWith(
        API_ENDPOINTS.CREDENTIALS.UPDATE(credentialId),
        {
          key_name: 'Updated Name',
          value: undefined,
          description: 'Updated description',
        }
      );

      expect(result).toEqual({
        id: credentialId,
        name: 'Updated Name',
        description: 'Updated description',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
        lastUsedAt: '2024-01-03T00:00:00Z',
      });
    });
  });

  describe('deleteCredential', () => {
    it('should delete credential successfully', async () => {
      const credentialId = 'cred-123';

      mockAxios.delete.mockResolvedValue({
        data: {
          success: true,
          message: 'Credential deleted successfully',
        },
      });

      await credentialService.deleteCredential(credentialId);

      expect(mockAxios.delete).toHaveBeenCalledWith(API_ENDPOINTS.CREDENTIALS.DELETE(credentialId));
    });

    it('should handle delete errors', async () => {
      const credentialId = 'cred-123';
      const mockError = {
        response: {
          status: 404,
          data: { message: 'Credential not found' },
        },
      };

      mockAxios.delete.mockRejectedValue(mockError);

      await expect(credentialService.deleteCredential(credentialId)).rejects.toMatchObject({
        type: CredentialErrorType.NOT_FOUND_ERROR,
        message: 'Credential not found.',
      });
    });
  });

  describe('getCredentialValueForExecution', () => {
    it('should get credential value for execution', async () => {
      const credentialId = 'cred-123';
      const mockBackendCredential: BackendCredentialInfo = {
        id: credentialId,
        key_name: 'Execution Key',
        description: 'For execution',
        value: 'execution-secret-value',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        last_used_at: '2024-01-03T00:00:00Z',
      };

      mockAxios.get.mockResolvedValue({
        data: {
          success: true,
          message: 'Credential retrieved',
          credential: mockBackendCredential,
        },
      });

      const result = await credentialService.getCredentialValueForExecution(credentialId);

      expect(mockAxios.get).toHaveBeenCalledWith(API_ENDPOINTS.CREDENTIALS.GET(credentialId));
      expect(result).toBe('execution-secret-value');
    });

    it('should handle missing credential value', async () => {
      const credentialId = 'cred-123';

      mockAxios.get.mockResolvedValue({
        data: {
          success: true,
          message: 'Credential retrieved',
          credential: null,
        },
      });

      await expect(credentialService.getCredentialValueForExecution(credentialId)).rejects.toMatchObject({
        type: CredentialErrorType.NOT_FOUND_ERROR,
        message: 'Credential not found.',
      });
    });
  });
});
