"use client";

import { ConfirmationScreenProps } from "@/app/shared/interfaces";
import { Logo } from "@/app/shared/Logo";

export function ConfirmationScreen({
  title,
  message,
  description,
}: ConfirmationScreenProps) {
  return (
    <div className="flex flex-col items-center justify-center gap-10 w-fit h-fit p-10 bg-brand-card border-2 border-brand-stroke rounded-xl ">
      <Logo />

      <div className="flex flex-col items-center justify-center gap-14  text-center">
        <h2 className="text-2xl font-bold text-brand-primary-font font-primary">
          {title.toUpperCase()}
        </h2>
        <div className="flex flex-col gap-2">
          <p className="text-brand-primary-font font-medium">{message}</p>
          <p className="text-brand-secondary-font text-sm font-medium ">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}
