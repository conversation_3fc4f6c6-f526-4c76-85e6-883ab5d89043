import { useEffect, useState, useRef } from "react";
import { Node } from "reactflow";
import { WorkflowNodeData, InputDefinition, MCPSchemaInfo } from "@/types";
import { convertJsonSchemaToInputDefinitions } from "@/lib/dynamicInputTypeMapper";
import { useComponentStateStore } from "@/store/mcpToolsStore";

/**
 * Generate inputs for an MCP Marketplace component based on its definition
 *
 * @param node The node to generate inputs for
 * @returns An array of input definitions
 */
export function generateMCPMarketplaceInputs(node: Node<WorkflowNodeData>): InputDefinition[] {
  if (!node || !node.data || !node.data.definition) {
    return [];
  }

  // Get the inputs from the node definition
  const inputs = node.data.definition.inputs || [];

  // Get the schema from the node definition
  const mcp_info = (node.data.definition.mcp_info as MCPSchemaInfo) || {};
  const input_schema = mcp_info?.input_schema || { properties: {} };

  if (process.env.NODE_ENV === 'development') {
    console.log("MCP Marketplace component input schema:", input_schema);
  }

  // Process the inputs to ensure they have the correct structure
  const processedInputs = inputs
    .map((input: InputDefinition) => {
      // Make sure the input has a name
      if (!input.name) {
        console.warn("Input missing name:", input);
        return null;
      }

      // Make sure the input has a display name
      if (!input.display_name) {
        input.display_name = input.name.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
      }

      // Make sure the input has an input_type
      if (!input.input_type) {
        input.input_type = "string";
      }

      // Fix for handle inputs - ensure they have the correct properties
      if (input.is_handle) {
        // For MCP Marketplace components, we want to keep the original input_type
        // but still mark it as a handle so it can be connected
        if (!input.input_types) {
          input.input_types = [input.input_type, "Any"];
        } else if (!input.input_types.includes("Any")) {
          input.input_types.push("Any");
        }
      }

      // For explicit handle inputs (ending with _handle), set input_type to 'handle'
      if (input.name.endsWith("_handle")) {
        input.input_type = "handle";
        input.is_handle = true;
      }

      // Handle connection inputs (e.g., "topic" and "topic_connection")
      // If we have both a direct input and a connection input, convert the connection to a handle
      if (input.name.endsWith("_connection") && !input.is_handle) {
        // Check if there's a direct input with the same base name
        const baseName = input.name.replace("_connection", "");
        const hasDirectInput = inputs.some((i) => i.name === baseName);

        if (hasDirectInput) {
          // This is a connection field that should be converted to a handle
          input.name = `${baseName}_handle`;
          input.input_type = "handle";
          input.is_handle = true;
        }
      }

      // Make sure the input has input_types array
      if (!input.input_types) {
        if (input.input_type === "handle") {
          input.input_types = ["Any"];
        } else {
          input.input_types = [input.input_type];
        }
      }

      // Make sure the input has required frontend properties
      if (input.real_time_refresh === undefined) {
        input.real_time_refresh = false;
      }
      if (input.advanced === undefined) {
        input.advanced = false;
      }
      if (input.is_list === undefined) {
        input.is_list = input.input_type === "list";
      }
      if (input.visibility_logic === undefined) {
        input.visibility_logic = "OR";
      }

      // Make sure the input has a value
      if (input.value === undefined) {
        // Set default value based on input type
        switch (input.input_type) {
          case "string":
            input.value = "";
            break;
          case "int":
          case "number":
            input.value = 0;
            break;
          case "bool":
            input.value = false;
            break;
          case "list":
            input.value = [];
            break;
          case "dict":
          case "object":
            input.value = {};
            break;
          case "handle":
            input.value = null;
            break;
          default:
            input.value = "";
        }
      }

      // Add validation based on schema if available
      const inputName = input.name.replace("_handle", "");
      const schemaProperty = input_schema.properties ? input_schema.properties[inputName] : null;

      // Special handling for object-type inputs
      if (
        input.input_type === "dict" &&
        schemaProperty &&
        schemaProperty.type === "object" &&
        schemaProperty.properties
      ) {
        // Convert dict to object type for better UI rendering
        input.input_type = "object";
        input.properties = schemaProperty.properties;
        if (process.env.NODE_ENV === 'development') {
          console.log(
            `Converting ${input.name} from dict to object type with properties:`,
            input.properties,
          );
        }
      }

      if (schemaProperty) {
        // Add validation rules based on schema
        input.validation = input.validation || {};

        if (schemaProperty.type === "string" && schemaProperty.minLength) {
          input.validation.minLength = schemaProperty.minLength;
        }

        if (schemaProperty.type === "string" && schemaProperty.maxLength) {
          input.validation.maxLength = schemaProperty.maxLength;
        }

        if (
          (schemaProperty.type === "number" || schemaProperty.type === "integer") &&
          schemaProperty.minimum !== undefined
        ) {
          input.validation.min = schemaProperty.minimum;
        }

        if (
          (schemaProperty.type === "number" || schemaProperty.type === "integer") &&
          schemaProperty.maximum !== undefined
        ) {
          input.validation.max = schemaProperty.maximum;
        }

        // Add required validation based on schema
        if (input_schema.required && input_schema.required.includes(inputName)) {
          input.required = true;
          if (process.env.NODE_ENV === 'development') {
            console.log(`Marking input ${input.name} as required based on schema`);
          }
        }
      }

      // For MCP components, handle required fields intelligently
      if (input.required === undefined) {
        // Common optional fields
        const commonOptionalFields = ["link", "api_key", "base_url"];

        // Mark as required by default unless it's a handle or a known optional field
        if (!input.is_handle &&
            !input.name.endsWith("_handle") &&
            input.input_type !== "handle" &&
            !commonOptionalFields.includes(input.name)) {
          input.required = true;
          if (process.env.NODE_ENV === 'development') {
            console.log(`Marking input ${input.name} as required by default`);
          }
        } else {
          input.required = false;
          if (process.env.NODE_ENV === 'development') {
            console.log(`Marking input ${input.name} as optional by default`);
          }
        }
      }

      return input;
    })
    .filter(Boolean) as InputDefinition[];

  return processedInputs;
}

/**
 * Process the inputs for an MCP Marketplace component to ensure they have the correct structure
 * and add any missing inputs based on the mcp_server_configs.json file
 *
 * @param node The node to process inputs for
 * @param onNodeDataChange Callback to update the node data
 * @param componentState Optional component state store
 */
export function processMCPMarketplaceNode(
  node: Node<WorkflowNodeData>,
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void,
  componentState?: any,
): void {
  if (!node || !node.data || !node.data.definition) {
    return;
  }

  // Generate inputs for the node
  const inputs = generateMCPMarketplaceInputs(node);

  // Update the node definition with the processed inputs
  const newDefinition = {
    ...node.data.definition,
    inputs,
  };

  // Initialize config with input values
  const newConfig = { ...node.data.config };

  // Add default values for inputs
  inputs.forEach((input) => {
    if (!input.is_handle && newConfig[input.name] === undefined && input.value !== undefined) {
      newConfig[input.name] = input.value;
    }
  });

  // Update the node data
  const newData = {
    ...node.data,
    definition: newDefinition,
    config: newConfig,
  };

  // Update the node
  onNodeDataChange(node.id, newData);

  // Store the inputs in the component state if provided
  if (componentState) {
    componentState.setValue(node.id, "inputs", inputs);
  }
}

/**
 * Hook to process MCP Marketplace component inputs
 *
 * @param node The node to process inputs for
 * @param onNodeDataChange Callback to update the node data
 */
export function useMCPMarketplaceInputs(
  node: Node<WorkflowNodeData>,
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void,
): InputDefinition[] {
  const [inputs, setInputs] = useState<InputDefinition[]>([]);

  // Track the last processed node data to avoid circular updates
  const lastProcessedNodeRef = useRef<string>("");

  useEffect(() => {
    if (!node || !node.data || !node.data.definition) {
      return;
    }

    // Create a key to represent the current node state
    const nodeKey = `${node.id}-${JSON.stringify(node.data.definition)}`;

    // If we've already processed this exact node state, skip to avoid circular updates
    if (nodeKey === lastProcessedNodeRef.current) {
      return;
    }

    // Update our reference to the current node state
    lastProcessedNodeRef.current = nodeKey;

    // Generate inputs for the node
    const generatedInputs = generateMCPMarketplaceInputs(node);
    setInputs(generatedInputs);

    // Process the node with the generated inputs
    // We'll handle the component state separately to avoid hooks issues
    const stateStore = useComponentStateStore.getState();

    // Get the current config from both node data and component state
    const nodeConfig = node.data.config || {};
    const stateConfig = stateStore.getValue(node.id, "config", {});

    // Merge them, preferring node data for consistency
    const currentConfig = { ...stateConfig, ...nodeConfig };

    // Create a new config object based on the merged config
    const newConfig = { ...currentConfig };

    // First, filter out any connection inputs that have a direct input equivalent
    const filteredInputs = generatedInputs.filter((input) => {
      // Skip connection inputs if we have a direct input
      if (input.name.endsWith("_connection")) {
        const baseName = input.name.replace("_connection", "");
        const hasDirectInput = generatedInputs.some((i) => i.name === baseName);
        return !hasDirectInput;
      }
      return true;
    });

    // Then set default values for the remaining inputs
    filteredInputs.forEach((input) => {
      if (!input.is_handle && newConfig[input.name] === undefined) {
        newConfig[input.name] = input.value;
      }
    });

    // Initialize all inputs with appropriate default values
    generatedInputs.forEach((input) => {
      // Skip handle inputs
      if (input.is_handle || input.input_type === "handle" || input.name.endsWith("_handle")) {
        return;
      }

      // If the config already has a value for this input, keep it
      if (newConfig[input.name] !== undefined) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Keeping existing value for input ${input.name}:`, newConfig[input.name]);
        }
        return;
      }

      // For required inputs, always set a default value
      if (input.required) {
        // Set appropriate default values based on input type
        switch (input.input_type) {
          case "string":
          case "text":
            newConfig[input.name] = "";
            break;
          case "number":
          case "int":
          case "float":
            newConfig[input.name] = 0;
            break;
          case "boolean":
          case "bool":
            newConfig[input.name] = false;
            break;
          case "dropdown":
            // For dropdowns, use the first option or the default value
            if (input.options && input.options.length > 0) {
              newConfig[input.name] = input.options[0];
            } else if (input.value !== undefined) {
              newConfig[input.name] = input.value;
            } else {
              newConfig[input.name] = "";
            }
            break;
          case "object":
          case "dict":
          case "json":
            // Special case for keywords field
            if (input.name === "keywords" || input.name === "tool_arg_keywords") {
              newConfig[input.name] = {
                time: "",
                objective: "",
                audience: "",
                gender: "",
                tone: "",
                speakers: "",
              };
            } else {
              newConfig[input.name] = {};
            }
            break;
          case "array":
          case "list":
            newConfig[input.name] = [];
            break;
          case "credential":
            newConfig[input.name] = "";
            break;
          default:
            // For any other type, use an empty string as default
            newConfig[input.name] = "";
        }
        if (process.env.NODE_ENV === 'development') {
          console.log(`Setting default value for required input ${input.name}:`, newConfig[input.name]);
        }
      }
      // For non-required inputs with a defined value
      else if (input.value !== undefined) {
        newConfig[input.name] = input.value;
        if (process.env.NODE_ENV === 'development') {
          console.log(`Setting default value for optional input ${input.name}:`, input.value);
        }
      }
    });

    // Check if the config has actually changed
    if (JSON.stringify(newConfig) !== JSON.stringify(currentConfig)) {
      // Store the inputs in the component state
      stateStore.setValue(node.id, "inputs", generatedInputs);

      // Also store the config in the component state
      stateStore.setValue(node.id, "config", newConfig);

      if (process.env.NODE_ENV === 'development') {
        console.log(`Stored inputs and config in component state for node ${node.id}`);
        console.log(`Inputs:`, generatedInputs);
        console.log(`Config:`, newConfig);
      }

      // Only update the node data if the config has actually changed from what's in the node
      if (JSON.stringify(newConfig) !== JSON.stringify(nodeConfig)) {
        // Update the node data
        const newData = {
          ...node.data,
          config: newConfig,
        };

        // Update the node
        onNodeDataChange(node.id, newData);
      }
    }
  }, [node?.id, node?.data?.definition]);

  return inputs;
}

/**
 * Component to handle MCP Marketplace component inputs
 */
export function MCPMarketplaceInputHandler({
  node,
  onNodeDataChange,
}: {
  node: Node<WorkflowNodeData>;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
}) {
  // Process the inputs for the node
  useMCPMarketplaceInputs(node, onNodeDataChange);

  // This is a logic component, not a UI component
  return null;
}
