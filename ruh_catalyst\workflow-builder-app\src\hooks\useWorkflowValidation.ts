import { useCallback } from "react";
import { Node, Edge, useReactFlow, useStore } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { useValidationStore } from "@/store/validationStore";
import { WorkflowValidationOptions } from "@/lib/validation/types";
import { debounce } from "@/lib/utils";

/**
 * Hook for validating workflows
 */
export function useWorkflowValidation() {
  const {
    isValid,
    errors,
    warnings,
    infos,
    missingFields,
    hasValidated,
    isValidating,
    validateWorkflow,
    validateWorkflowSmart,
    validateBeforeSave,
    validateBeforeExecution,
    validateDuringEditing,
    clearValidation,
  } = useValidationStore();

  const { getNodes, getEdges } = useReactFlow<WorkflowNodeData>();

  /**
   * Validate the current workflow
   */
  const validateCurrentWorkflow = useCallback(
    async (options?: WorkflowValidationOptions) => {
      const nodes = getNodes();
      const edges = getEdges();
      return await validateWorkflowSmart(nodes, edges, options);
    },
    [getNodes, getEdges, validateWorkflowSmart]
  );

  /**
   * Validate the current workflow before saving
   */
  const validateCurrentWorkflowBeforeSave = useCallback(
    async () => {
      const nodes = getNodes();
      const edges = getEdges();
      return await validateBeforeSave(nodes, edges);
    },
    [getNodes, getEdges, validateBeforeSave]
  );

  /**
   * Validate the current workflow before execution
   *
   * @param providedNodes Optional nodes to use instead of getting from React Flow
   * @param providedEdges Optional edges to use instead of getting from React Flow
   */
  const validateCurrentWorkflowBeforeExecution = useCallback(
    async (providedNodes?: Node<WorkflowNodeData>[], providedEdges?: Edge[]) => {
      // Use provided nodes/edges if available, otherwise get from React Flow
      const nodes = providedNodes || getNodes();
      const edges = providedEdges || getEdges();

      console.log(`[useWorkflowValidation] Validating workflow with ${nodes.length} nodes and ${edges.length} edges`);

      // If nodes array is empty, log a warning
      if (nodes.length === 0) {
        console.warn('[useWorkflowValidation] WARNING: Empty nodes array for validation');
      }

      return await validateBeforeExecution(nodes, edges);
    },
    [getNodes, getEdges, validateBeforeExecution]
  );

  /**
   * Validate the current workflow during editing
   */
  const validateCurrentWorkflowDuringEditing = useCallback(
    async () => {
      const nodes = getNodes();
      const edges = getEdges();
      return await validateDuringEditing(nodes, edges);
    },
    [getNodes, getEdges, validateDuringEditing]
  );

  /**
   * Validate a workflow with debouncing
   */
  const debouncedValidate = useCallback(
    debounce(
      async (
        nodes: Node<WorkflowNodeData>[],
        edges: Edge[],
        options?: WorkflowValidationOptions
      ) => {
        await validateDuringEditing(nodes, edges);
      },
      500
    ),
    [validateDuringEditing]
  );

  /**
   * Get validation errors for a specific node
   */
  const getNodeErrors = useCallback(
    (nodeId: string) => {
      return errors.filter((error) => error.nodeId === nodeId);
    },
    [errors]
  );

  /**
   * Get validation errors for a specific field
   */
  const getFieldErrors = useCallback(
    (nodeId: string, fieldId: string) => {
      return errors.filter(
        (error) => error.nodeId === nodeId && error.fieldId === fieldId
      );
    },
    [errors]
  );

  /**
   * Check if a node has any validation errors
   */
  const hasNodeErrors = useCallback(
    (nodeId: string) => {
      return errors.some((error) => error.nodeId === nodeId);
    },
    [errors]
  );

  /**
   * Check if a field has any validation errors
   */
  const hasFieldErrors = useCallback(
    (nodeId: string, fieldId: string) => {
      return errors.some(
        (error) => error.nodeId === nodeId && error.fieldId === fieldId
      );
    },
    [errors]
  );

  /**
   * Get missing fields for a specific node
   */
  const getNodeMissingFields = useCallback(
    (nodeId: string) => {
      return missingFields.filter((field) => field.nodeId === nodeId);
    },
    [missingFields]
  );

  return {
    // State
    isValid,
    errors,
    warnings,
    infos,
    missingFields,
    hasValidated,
    isValidating,

    // Actions
    validateWorkflow,
    validateWorkflowSmart,
    validateCurrentWorkflow,
    validateBeforeSave,
    validateBeforeExecution,
    validateDuringEditing,
    validateCurrentWorkflowBeforeSave,
    validateCurrentWorkflowBeforeExecution,
    validateCurrentWorkflowDuringEditing,
    debouncedValidate,
    clearValidation,

    // Helpers
    getNodeErrors,
    getFieldErrors,
    hasNodeErrors,
    hasFieldErrors,
    getNodeMissingFields,
  };
}
