import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import { google } from "googleapis";
import * as dotenv from "dotenv";
import { join } from "path";
import { createStatelessServer } from "@smithery/sdk/server/stateless.js";



dotenv.config({ path: join(process.cwd(), ".env") });

// Project-level credentials from environment (required at startup)
const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;

// Validate project credentials at startup
if (!CLIENT_ID || !CLIENT_SECRET) {
  console.error('❌ Missing required Google OAuth project credentials');
  console.error('   Please set CLIENT_ID and CLIENT_SECRET in your .env file');
  console.error('   See .env.example for reference');
  process.exit(1);
}

function createMcpServer() {

  const server = new McpServer(
    {
      name: "google-calendar-server",
      version: "0.1.0",
    },
    {
      capabilities: {
        tools: {},
      },
    }
  );

  // Store the current request headers for access in tool handlers
  let currentRequestHeaders = {};

  // Helper function to create authenticated clients using project credentials + user tokens
  function createAuthenticatedClients(googleCredentials) {
    // Validate that credentials are provided
    if (!googleCredentials) {
      throw new Error('Missing Google OAuth credentials. Provide googleCredentials in request body.');
    }

    const { accessToken, refreshToken } = googleCredentials;

    // Validate that we have at least an access token
    if (!accessToken) {
      throw new Error('Missing required accessToken in googleCredentials. Provide accessToken in request body.');
    }

    // Debug logging for authentication
    console.log(`[${new Date().toISOString()}] createAuthenticatedClients called with body credentials`);
    console.log('Credentials provided:', {
      hasRefreshToken: !!refreshToken,
      hasAccessToken: !!accessToken,
      accessTokenLength: accessToken ? accessToken.length : 0
    });

    // Create OAuth2 client with project credentials
    const auth = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET);

    // Set user-specific credentials (prioritize access token)
    if (accessToken) {
      auth.setCredentials({ access_token: accessToken });
      if (refreshToken) {
        auth.setCredentials({
          access_token: accessToken,
          refresh_token: refreshToken
        });
      }
    }

    return {
      gmail: google.gmail({ version: "v1", auth: auth }),
      calendar: google.calendar({ version: "v3", auth: auth })
    };
  }

  // Error handling
  server.server.onerror = (error) => console.error("[MCP Error]", error);

  // Set up tool handlers
  server.server.setRequestHandler(ListToolsRequestSchema, async () => ({
    tools: [
      {
        name: "list_events",
        description: "List upcoming calendar events",
        inputSchema: {
          type: "object",
          properties: {
            maxResults: {
              type: "number",
              description: "Maximum number of events to return (default: 10)",
            },
            timeMin: {
              type: "string",
              description: "Start time in ISO format (default: now)",
            },
            timeMax: {
              type: "string",
              description: "End time in ISO format",
            },
            accessToken: {
              type: "string",
              description: "Google OAuth access token (required)",
            },
            refreshToken: {
              type: "string",
              description: "Google OAuth refresh token (optional)",
            },
          },
          required: ["accessToken"],
        },
      },
      {
        name: "create_event",
        description: "Create a new calendar event",
        inputSchema: {
          type: "object",
          properties: {
            summary: {
              type: "string",
              description: "Event title",
            },
            location: {
              type: "string",
              description: "Event location",
            },
            description: {
              type: "string",
              description: "Event description",
            },
            start: {
              type: "string",
              description: "Start time in ISO format",
            },
            end: {
              type: "string",
              description: "End time in ISO format",
            },
            attendees: {
              type: "array",
              items: { type: "string" },
              description: "List of attendee email addresses",
            },
            accessToken: {
              type: "string",
              description: "Google OAuth access token (required)",
            },
            refreshToken: {
              type: "string",
              description: "Google OAuth refresh token (optional)",
            },
          },
          required: ["summary", "start", "end", "accessToken"],
        },
      },
      {
        name: "update_event",
        description: "Update an existing calendar event",
        inputSchema: {
          type: "object",
          properties: {
            eventId: {
              type: "string",
              description: "Event ID to update",
            },
            summary: {
              type: "string",
              description: "New event title",
            },
            location: {
              type: "string",
              description: "New event location",
            },
            description: {
              type: "string",
              description: "New event description",
            },
            start: {
              type: "string",
              description: "New start time in ISO format",
            },
            end: {
              type: "string",
              description: "New end time in ISO format",
            },
            attendees: {
              type: "array",
              items: { type: "string" },
              description: "New list of attendee email addresses",
            },
            accessToken: {
              type: "string",
              description: "Google OAuth access token (required)",
            },
            refreshToken: {
              type: "string",
              description: "Google OAuth refresh token (optional)",
            },
          },
          required: ["eventId", "accessToken"],
        },
      },
      {
        name: "delete_event",
        description: "Delete a calendar event",
        inputSchema: {
          type: "object",
          properties: {
            eventId: {
              type: "string",
              description: "Event ID to delete",
            },
            accessToken: {
              type: "string",
              description: "Google OAuth access token (required)",
            },
            refreshToken: {
              type: "string",
              description: "Google OAuth refresh token (optional)",
            },
          },
          required: ["eventId", "accessToken"],
        },
      },
    ],
  }));
  server.server.setRequestHandler(CallToolRequestSchema, async (request) => {
    try {
      // Extract credentials from request body (now flattened)
      const { accessToken, refreshToken } = request.params.arguments || {};
      const googleCredentials = { accessToken, refreshToken };

      // Create authenticated clients with body credentials
      const { calendar } = createAuthenticatedClients(googleCredentials);

      // Remove credentials from arguments before passing to tool handlers
      const { accessToken: _, refreshToken: __, ...toolArguments } = request.params.arguments || {};

      switch (request.params.name) {
        case "list_events":
          return await handleListEvents(toolArguments, calendar);
        case "create_event":
          return await handleCreateEvent(toolArguments, calendar);
        case "update_event":
          return await handleUpdateEvent(toolArguments, calendar);
        case "delete_event":
          return await handleDeleteEvent(toolArguments, calendar);
        default:
          throw new McpError(
            ErrorCode.MethodNotFound,
            `Unknown tool: ${request.params.name}`
          );
      }
    } catch (error) {
      if (error.message.includes('Missing Google OAuth credentials') ||
        error.message.includes('Missing required accessToken')) {
        throw new McpError(
          ErrorCode.InvalidRequest,
          error.message
        );
      }
      throw error;
    }
  });
  return server;
}

async function handleCreateEvent(args, calendar) {
  try {
    const { summary, location, description, start, end, attendees = [] } = args;

    const event = {
      summary,
      location,
      description,
      start: {
        dateTime: start,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
      end: {
        dateTime: end,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
      attendees: attendees.map((email) => ({ email })),
    };

    const response = await calendar.events.insert({
      calendarId: "primary",
      requestBody: event,
    });

    return {
      content: [
        {
          type: "text",
          text: `Event created successfully. Event ID: ${response.data.id}`,
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: "text",
          text: `Error creating event: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
}

async function handleUpdateEvent(args, calendar) {
  try {
    const { eventId, summary, location, description, start, end, attendees } =
      args;

    const event = {};
    if (summary) event.summary = summary;
    if (location) event.location = location;
    if (description) event.description = description;
    if (start) {
      event.start = {
        dateTime: start,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    }
    if (end) {
      event.end = {
        dateTime: end,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    }
    if (attendees) {
      event.attendees = attendees.map((email) => ({ email }));
    }

    const response = await calendar.events.patch({
      calendarId: "primary",
      eventId,
      requestBody: event,
    });

    return {
      content: [
        {
          type: "text",
          text: `Event updated successfully. Event ID: ${response.data.id}`,
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: "text",
          text: `Error updating event: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
}

async function handleDeleteEvent(args, calendar) {
  try {
    const { eventId } = args;

    await calendar.events.delete({
      calendarId: "primary",
      eventId,
    });

    return {
      content: [
        {
          type: "text",
          text: `Event deleted successfully. Event ID: ${eventId}`,
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: "text",
          text: `Error deleting event: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
}

async function handleListEvents(args, calendar) {
  try {
    const maxResults = args?.maxResults || 10;
    const timeMin = args?.timeMin || new Date().toISOString();
    const timeMax = args?.timeMax;

    const response = await calendar.events.list({
      calendarId: "primary",
      timeMin,
      timeMax,
      maxResults,
      singleEvents: true,
      orderBy: "startTime",
    });

    const events = response.data.items?.map((event) => ({
      id: event.id,
      summary: event.summary,
      start: event.start,
      end: event.end,
      location: event.location,
    }));

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(events, null, 2),
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: "text",
          text: `Error fetching calendar events: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
}

// Import Express to create custom app with middleware
import express from 'express';

// Create custom Express app with middleware BEFORE mounting MCP routes
const app = express();



// Now create the MCP server and mount it on our custom app
const { app: mcpApp } = createStatelessServer(createMcpServer);

// Mount the MCP app as middleware on our custom app
app.use('/', mcpApp);

const port = process.env.PORT || 8081;
app.listen(port, () => {
  console.log(`🚀 MCP server running on port ${port} - Body-only authentication - Deploy ${new Date().toISOString()}`);
  console.log(`Project credentials loaded from environment:`);
  console.log(`  CLIENT_ID: ${CLIENT_ID ? '✓ Configured' : '✗ Missing'}`);
  console.log(`  CLIENT_SECRET: ${CLIENT_SECRET ? '✓ Configured' : '✗ Missing'}`);
  console.log(`Server requires authentication credentials in request body:`);
  console.log(`  accessToken: Required in all tool requests`);
  console.log(`  refreshToken: Optional in all tool requests`);
  console.log(`🔧 Body-only authentication is active!`);
});
