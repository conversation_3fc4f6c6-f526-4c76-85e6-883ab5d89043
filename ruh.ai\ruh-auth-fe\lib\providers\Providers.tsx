// This is a client component that is used to wrap the app in providers
"use client";

import React from "react";
import { ThemeProvider } from "./ThemeProvider";
import { Toaster } from "sonner";
import QueryProvider from "./QueryProvider";
import { FirebaseMessagingProvider } from "./FirebaseMessagingProvider";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      forcedTheme="light"
      disableTransitionOnChange
    >
      <QueryProvider>
        <Toaster />
        <FirebaseMessagingProvider />
        {children}
      </QueryProvider>
    </ThemeProvider>
  );
}
