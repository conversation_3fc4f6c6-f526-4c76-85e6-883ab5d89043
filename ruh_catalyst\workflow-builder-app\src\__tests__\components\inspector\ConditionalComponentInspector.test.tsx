/**
 * Test suite for ConditionalComponentInspector component.
 * 
 * This module tests the conditional component configuration UI:
 * - Component mode indicator display
 * - Condition configuration sections
 * - Handle management information
 * - Routing decision visualization
 * - Integration with node configuration
 * 
 * Following TDD methodology - Phase 4 Cycle 2: Conditional Component Inspector
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Node } from 'reactflow';
import { ConditionalComponentInspector } from '@/components/inspector/node-types/ConditionalComponentInspector';
import { WorkflowNodeData } from '@/types';

// Mock the conditional routing config
jest.mock('@/config/conditionalRouting', () => ({
  shouldUseComponentMode: () => true,
  CONDITIONAL_ROUTING_FEATURES: {
    MODE: 'component',
    COMPONENT_MODE_ENABLED: true,
    SHOW_MODE_TOGGLE: false,
  },
  CONDITIONAL_COMPONENT_UI_CONFIG: {
    ENHANCED_INSPECTOR: true,
    SHOW_MODE_INDICATORS: true,
    SHOW_HANDLE_INFO: true,
    SHOW_ROUTING_VISUALIZATION: true,
  },
}));

describe('ConditionalComponentInspector', () => {
  const mockNode: Node<WorkflowNodeData> = {
    id: 'conditional-1',
    type: 'ConditionalNode',
    position: { x: 0, y: 0 },
    data: {
      originalType: 'ConditionalNode',
      type: 'ConditionalNode',
      definition: {
        name: 'ConditionalNode',
        display_name: 'Switch-Case Router',
        description: 'Evaluates multiple conditions and routes data to matching outputs',
        category: 'Logic',
        inputs: [
          {
            name: 'num_conditions',
            display_name: 'Number of Conditions',
            input_type: 'number',
            value: 2,
            required: true,
            info: 'Number of conditions to evaluate (1-10)'
          },
          {
            name: 'condition_1_operator',
            display_name: 'Condition 1 - Operator',
            input_type: 'dropdown',
            options: ['equals', 'contains', 'starts_with', 'greater_than'],
            value: 'equals',
            required: false,
            info: 'Comparison operator to apply'
          },
          {
            name: 'condition_1_expected_value',
            display_name: 'Condition 1 - Expected Value',
            input_type: 'string',
            value: '',
            required: false,
            info: 'Value to compare against'
          },
          {
            name: 'condition_1_source',
            display_name: 'Condition 1 - Source',
            input_type: 'dropdown',
            options: ['node_output', 'global_context'],
            value: 'node_output',
            required: false,
            info: 'Source of data to evaluate'
          },
          {
            name: 'condition_2_operator',
            display_name: 'Condition 2 - Operator',
            input_type: 'dropdown',
            options: ['equals', 'contains', 'starts_with', 'greater_than'],
            value: 'contains',
            required: false,
            info: 'Comparison operator to apply'
          },
          {
            name: 'condition_2_expected_value',
            display_name: 'Condition 2 - Expected Value',
            input_type: 'string',
            value: '',
            required: false,
            info: 'Value to compare against'
          }
        ]
      },
      config: {
        num_conditions: 2,
        condition_1_operator: 'equals',
        condition_1_expected_value: 'success',
        condition_1_source: 'node_output',
        condition_2_operator: 'contains',
        condition_2_expected_value: 'error',
        condition_2_source: 'node_output'
      }
    }
  };

  const defaultProps = {
    node: mockNode,
    onConfigChange: jest.fn(),
    isInputConnected: jest.fn(() => false),
    shouldDisableInput: jest.fn(() => false),
    getConnectionInfo: jest.fn(() => ({ isConnected: false })),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Mode Indicator', () => {
    test('should render component mode indicator', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Component Mode')).toBeInTheDocument();
      expect(screen.getByText(/This conditional node will execute as a component/)).toBeInTheDocument();
    });

    test('should show component mode badge', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      const badge = screen.getByText('Component Mode');
      expect(badge).toBeInTheDocument();
      expect(badge.closest('.bg-blue-100')).toBeInTheDocument(); // Check for blue styling
    });

    test('should display enhanced capabilities description', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText(/enhanced routing capabilities/)).toBeInTheDocument();
    });
  });

  describe('Basic Configuration Section', () => {
    test('should render basic configuration section', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Basic Configuration')).toBeInTheDocument();
    });

    test('should render number of conditions input', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Number of Conditions')).toBeInTheDocument();
      expect(screen.getByDisplayValue('2')).toBeInTheDocument();
    });

    test('should handle number of conditions change', () => {
      const onConfigChange = jest.fn();
      render(<ConditionalComponentInspector {...defaultProps} onConfigChange={onConfigChange} />);
      
      const input = screen.getByDisplayValue('2');
      fireEvent.change(input, { target: { value: '3' } });
      
      expect(onConfigChange).toHaveBeenCalledWith('num_conditions', '3');
    });
  });

  describe('Conditions Configuration Section', () => {
    test('should render conditions section', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Conditions')).toBeInTheDocument();
    });

    test('should render condition cards based on num_conditions', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);

      // Use getAllByText to handle multiple instances of "Condition 1" text
      const condition1Elements = screen.getAllByText(/Condition 1/);
      const condition2Elements = screen.getAllByText(/Condition 2/);

      expect(condition1Elements.length).toBeGreaterThan(0);
      expect(condition2Elements.length).toBeGreaterThan(0);
      expect(screen.queryByText('Condition 3')).not.toBeInTheDocument();
    });

    test('should render condition configuration fields', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      // Check for condition 1 fields
      expect(screen.getByText('Condition 1 - Operator')).toBeInTheDocument();
      expect(screen.getByText('Condition 1 - Expected Value')).toBeInTheDocument();
      
      // Check for condition 2 fields
      expect(screen.getByText('Condition 2 - Operator')).toBeInTheDocument();
      expect(screen.getByText('Condition 2 - Expected Value')).toBeInTheDocument();
    });

    test('should update conditions when num_conditions changes', () => {
      const nodeWith3Conditions = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: {
            ...mockNode.data.config,
            num_conditions: 3
          }
        }
      };

      render(<ConditionalComponentInspector {...defaultProps} node={nodeWith3Conditions} />);

      // Use getAllByText to handle multiple instances
      const condition1Elements = screen.getAllByText(/Condition 1/);
      const condition2Elements = screen.getAllByText(/Condition 2/);
      const condition3Elements = screen.getAllByText(/Condition 3/);

      expect(condition1Elements.length).toBeGreaterThan(0);
      expect(condition2Elements.length).toBeGreaterThan(0);
      expect(condition3Elements.length).toBeGreaterThan(0);
    });
  });

  describe('Handle Management Section', () => {
    test('should render component handles section', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Component Handles')).toBeInTheDocument();
    });

    test('should show data flow information', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Data Flow')).toBeInTheDocument();
      expect(screen.getByText(/Data flows through the previous component/)).toBeInTheDocument();
    });

    test('should explain conditional component behavior', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText(/only determines the routing decision/)).toBeInTheDocument();
    });
  });

  describe('Routing Decision Section', () => {
    test('should render routing decision section', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);

      expect(screen.getByText('Routing Decision')).toBeInTheDocument();
    });

    test('should show condition-to-transition mapping', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);

      // Look for condition mappings in the routing section specifically
      const routingSection = screen.getByText('Routing Decision').closest('div');
      expect(routingSection).toBeInTheDocument();
    });

    test('should display transition arrows', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);

      // Look for arrow symbols in the routing decision section
      // Use a more flexible approach since arrows might not render in test environment
      const routingSection = screen.getByText('Routing Decision').closest('div');
      expect(routingSection).toBeInTheDocument();

      // Check for arrow symbols or routing indicators
      const arrowElements = screen.queryAllByText('→');
      const routingIndicators = screen.queryAllByText(/→|Target \d+/);

      // Should have either arrows or routing indicators
      expect(arrowElements.length + routingIndicators.length).toBeGreaterThan(0);
    });
  });

  describe('Default Transition Section', () => {
    test('should render default transition section', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText('Default Transition')).toBeInTheDocument();
    });

    test('should explain default transition behavior', () => {
      render(<ConditionalComponentInspector {...defaultProps} />);
      
      expect(screen.getByText(/when no conditions match/)).toBeInTheDocument();
    });
  });

  describe('Integration with Props', () => {
    test('should call onConfigChange when configuration changes', () => {
      const onConfigChange = jest.fn();
      render(<ConditionalComponentInspector {...defaultProps} onConfigChange={onConfigChange} />);

      // Find the number of conditions input and change it
      const numConditionsInput = screen.getByDisplayValue('2');
      fireEvent.change(numConditionsInput, { target: { value: '3' } });

      expect(onConfigChange).toHaveBeenCalledWith('num_conditions', '3');
    });

    test('should respect isInputConnected prop', () => {
      const isInputConnected = jest.fn(() => true);
      render(<ConditionalComponentInspector {...defaultProps} isInputConnected={isInputConnected} />);
      
      expect(isInputConnected).toHaveBeenCalled();
    });

    test('should respect shouldDisableInput prop', () => {
      const shouldDisableInput = jest.fn(() => true);
      render(<ConditionalComponentInspector {...defaultProps} shouldDisableInput={shouldDisableInput} />);
      
      expect(shouldDisableInput).toHaveBeenCalled();
    });

    test('should use getConnectionInfo prop', () => {
      const getConnectionInfo = jest.fn(() => ({ isConnected: true, sourceNodeId: 'test' }));
      render(<ConditionalComponentInspector {...defaultProps} getConnectionInfo={getConnectionInfo} />);
      
      expect(getConnectionInfo).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    test('should handle missing node data gracefully', () => {
      const nodeWithoutConfig = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: undefined
        }
      };

      expect(() => {
        render(<ConditionalComponentInspector {...defaultProps} node={nodeWithoutConfig} />);
      }).not.toThrow();
    });

    test('should handle missing definition gracefully', () => {
      const nodeWithoutDefinition = {
        ...mockNode,
        data: {
          ...mockNode.data,
          definition: undefined
        }
      };

      expect(() => {
        render(<ConditionalComponentInspector {...defaultProps} node={nodeWithoutDefinition} />);
      }).not.toThrow();
    });

    test('should handle zero conditions gracefully', () => {
      const nodeWithZeroConditions = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: {
            ...mockNode.data.config,
            num_conditions: 0
          }
        }
      };

      render(<ConditionalComponentInspector {...defaultProps} node={nodeWithZeroConditions} />);

      // Should not have condition cards when num_conditions is 0
      // Look specifically for condition card titles, not all text containing "Condition"
      const conditionCards = screen.queryAllByRole('heading', { name: /^Condition \d+$/ });
      expect(conditionCards.length).toBe(0);

      // Should still show the basic configuration and other sections
      expect(screen.getByText('Basic Configuration')).toBeInTheDocument();
      expect(screen.getByText('Conditions')).toBeInTheDocument();
    });
  });
});
