/**
 * Tool connection flow integration utilities
 */

import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { isToolHandle, calculateToolConnectionState } from "@/utils/toolConnectionUtils";
import { getConnectedNodes, findStartNode } from "./utils";

export interface ToolConnectionFlowResult {
  isValid: boolean;
  errors: Array<{ message: string; nodeId?: string }>;
  warnings: Array<{ message: string; nodeId?: string }>;
  connectedNodes?: Set<string>;
}

/**
 * Enhanced version of getConnectedNodes that includes tool-connected components
 * and handles missing tool nodes referenced in config
 * @param nodes - All nodes in the workflow
 * @param edges - All edges in the workflow
 * @param startNodeId - ID of the start node
 * @returns Set of node IDs connected to start node (including via tool connections)
 */
export function getConnectedNodesWithToolConnections(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  startNodeId: string
): Set<string> {
  // Start with regular connected nodes
  const connectedNodes = getConnectedNodes(nodes, edges, startNodeId);
  
  // Track nodes we've already processed to avoid infinite loops
  const processedNodes = new Set<string>();
  
  // Keep adding tool-connected nodes until no new ones are found
  let foundNewNodes = true;
  while (foundNewNodes) {
    foundNewNodes = false;
    const currentSize = connectedNodes.size;
    
    // For each connected node, find components connected to it via tool handles
    for (const nodeId of connectedNodes) {
      if (processedNodes.has(nodeId)) continue;
      processedNodes.add(nodeId);
      
      const node = nodes.find(n => n.id === nodeId);
      if (!node) continue;
      
      // Check for tool connections via edges
      const toolEdges = edges.filter(edge =>
        edge.target === nodeId &&
        edge.targetHandle &&
        isToolHandle(edge.targetHandle)
      );
      
      // Add source nodes of tool edges to connected set
      for (const edge of toolEdges) {
        if (!connectedNodes.has(edge.source)) {
          connectedNodes.add(edge.source);
          foundNewNodes = true;
        }
      }
      
      // Check for tool connections in node config (handles missing tool nodes)
      if (node.data.originalType === "AgenticAI") {
        try {
          const toolConnectionState = calculateToolConnectionState(nodeId, edges, nodes);
          
          // Add all tool nodes referenced in config to connected set
          // This includes missing tool nodes that are stored in config
          for (const tool of toolConnectionState.connectedTools) {
            if (!connectedNodes.has(tool.nodeId)) {
              connectedNodes.add(tool.nodeId);
              foundNewNodes = true;
            }
          }
        } catch (error) {
          console.warn(`[getConnectedNodesWithToolConnections] Error processing tool connections for node ${nodeId}:`, error);
        }
      }
    }
    
    // Safety check to prevent infinite loops
    if (connectedNodes.size > nodes.length * 2) { // Allow for missing nodes
      console.warn("[getConnectedNodesWithToolConnections] Detected potential infinite loop, breaking");
      break;
    }
  }
  
  return connectedNodes;
}

/**
 * Checks if a node is connected via tool handles
 * @param nodeId - ID of the node to check
 * @param nodes - All nodes in the workflow
 * @param edges - All edges in the workflow
 * @returns True if the node is connected as a tool
 */
export function isNodeConnectedViaTools(
  nodeId: string,
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): boolean {
  // Find edges where this node is the source and target handle is a tool handle
  const toolEdges = edges.filter(edge => 
    edge.source === nodeId && 
    edge.targetHandle && 
    isToolHandle(edge.targetHandle)
  );
  
  return toolEdges.length > 0;
}

/**
 * Gets all components that are connected as tools
 * @param nodes - All nodes in the workflow
 * @param edges - All edges in the workflow
 * @returns Set of node IDs that are connected as tools
 */
export function getToolConnectedComponents(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Set<string> {
  const toolConnectedComponents = new Set<string>();
  
  // Find all edges with tool handles
  const toolEdges = edges.filter(edge => 
    edge.targetHandle && 
    isToolHandle(edge.targetHandle)
  );
  
  // Add source nodes of tool edges
  for (const edge of toolEdges) {
    toolConnectedComponents.add(edge.source);
  }
  
  return toolConnectedComponents;
}

/**
 * Validates workflow flow including tool connections
 * @param nodes - All nodes in the workflow
 * @param edges - All edges in the workflow
 * @returns Validation result with tool connection analysis
 */
export function validateToolConnectionFlow(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ToolConnectionFlowResult {
  const errors: Array<{ message: string; nodeId?: string }> = [];
  const warnings: Array<{ message: string; nodeId?: string }> = [];
  
  try {
    // Find start node
    const startNode = findStartNode(nodes);
    if (!startNode) {
      errors.push({ message: "No start node found in workflow" });
      return { isValid: false, errors, warnings };
    }
    
    // Get all connected nodes including tool connections
    const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, startNode.id);
    
    // Check for disconnected nodes
    const disconnectedNodes = nodes.filter(node => !connectedNodes.has(node.id));
    
    // Add warnings for disconnected nodes (except start node)
    for (const node of disconnectedNodes) {
      if (node.id !== startNode.id) {
        warnings.push({
          message: `Node "${node.data.label || node.id}" is not connected to the workflow`,
          nodeId: node.id
        });
      }
    }
    
    // Validate tool connections
    const toolConnectedComponents = getToolConnectedComponents(nodes, edges);
    
    // Check for tool components that are not connected to the main flow
    for (const toolNodeId of toolConnectedComponents) {
      if (!connectedNodes.has(toolNodeId)) {
        warnings.push({
          message: `Tool component "${toolNodeId}" is connected as a tool but not part of the main workflow flow`,
          nodeId: toolNodeId
        });
      }
    }
    
    return {
      isValid: true,
      errors,
      warnings,
      connectedNodes
    };
    
  } catch (error) {
    console.error("[validateToolConnectionFlow] Error during validation:", error);
    errors.push({ 
      message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    });
    
    return { isValid: false, errors, warnings };
  }
}

/**
 * Updates the existing utils.ts getConnectedNodes to use tool connection logic
 * This is a wrapper that maintains backward compatibility
 * @param nodes - All nodes in the workflow
 * @param edges - All edges in the workflow
 * @param startNodeId - ID of the start node
 * @returns Set of node IDs connected to start node
 */
export function getConnectedNodesEnhanced(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  startNodeId: string
): Set<string> {
  return getConnectedNodesWithToolConnections(nodes, edges, startNodeId);
}

/**
 * Checks if a workflow has any tool connections
 * @param edges - All edges in the workflow
 * @returns True if there are tool connections in the workflow
 */
export function hasToolConnections(edges: Edge[]): boolean {
  return edges.some(edge => 
    edge.targetHandle && 
    isToolHandle(edge.targetHandle)
  );
}

/**
 * Gets statistics about tool connections in the workflow
 * @param nodes - All nodes in the workflow
 * @param edges - All edges in the workflow
 * @returns Statistics object
 */
export function getToolConnectionStats(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): {
  totalToolConnections: number;
  toolConnectedComponents: number;
  agenticAINodes: number;
  averageToolsPerAgent: number;
} {
  const toolEdges = edges.filter(edge => 
    edge.targetHandle && 
    isToolHandle(edge.targetHandle)
  );
  
  const toolConnectedComponents = getToolConnectedComponents(nodes, edges);
  const agenticAINodes = nodes.filter(node => 
    node.data.originalType === "AgenticAI"
  );
  
  const averageToolsPerAgent = agenticAINodes.length > 0 
    ? toolEdges.length / agenticAINodes.length 
    : 0;
  
  return {
    totalToolConnections: toolEdges.length,
    toolConnectedComponents: toolConnectedComponents.size,
    agenticAINodes: agenticAINodes.length,
    averageToolsPerAgent: Math.round(averageToolsPerAgent * 100) / 100
  };
}
