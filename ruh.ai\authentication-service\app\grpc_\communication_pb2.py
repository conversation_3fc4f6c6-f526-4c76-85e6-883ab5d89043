# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: communication.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'communication.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x63ommunication.proto\x12\rcommunication\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x19google/protobuf/any.proto\x1a\x1cgoogle/protobuf/struct.proto\"\x80\x02\n\x0c\x43onversation\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0e\n\x06userId\x18\x02 \x01(\t\x12\x14\n\x07\x61gentId\x18\x03 \x01(\tH\x00\x88\x01\x01\x12)\n\x08\x63hatType\x18\x04 \x01(\x0e\x32\x17.communication.ChatType\x12\x13\n\x0binputTokens\x18\x05 \x01(\x05\x12\x14\n\x0coutputTokens\x18\x06 \x01(\x05\x12-\n\tcreatedAt\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tupdatedAt\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\n\n\x08_agentId\"x\n\x19\x43reateConversationRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\x12\x14\n\x07\x61gentId\x18\x02 \x01(\tH\x00\x88\x01\x01\x12)\n\x08\x63hatType\x18\x03 \x01(\x0e\x32\x17.communication.ChatTypeB\n\n\x08_agentId\"@\n\x16GetConversationRequest\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12\x0e\n\x06userId\x18\x02 \x01(\t\"C\n\x19\x44\x65leteConversationRequest\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12\x0e\n\x06userId\x18\x02 \x01(\t\"\x94\x01\n\x18ListConversationsRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\x12)\n\x08\x63hatType\x18\x02 \x01(\x0e\x32\x17.communication.ChatType\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\r\n\x05limit\x18\x04 \x01(\x05\x12\x14\n\x07\x61gentId\x18\x05 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_agentId\"\x9f\x01\n\x1fUpdateConversationTokensRequest\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12\x18\n\x0binputTokens\x18\x02 \x01(\x05H\x00\x88\x01\x01\x12\x19\n\x0coutputTokens\x18\x03 \x01(\x05H\x01\x88\x01\x01\x12\x0e\n\x06userId\x18\x04 \x01(\tB\x0e\n\x0c_inputTokensB\x0f\n\r_outputTokens\"\x8c\x01\n\x12PaginationMetadata\x12\r\n\x05total\x18\x01 \x01(\x05\x12\x12\n\ntotalPages\x18\x02 \x01(\x05\x12\x13\n\x0b\x63urrentPage\x18\x03 \x01(\x05\x12\x10\n\x08pageSize\x18\x04 \x01(\x05\x12\x13\n\x0bhasNextPage\x18\x05 \x01(\x08\x12\x17\n\x0fhasPreviousPage\x18\x06 \x01(\x08\"{\n\x19ListConversationsResponse\x12)\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1b.communication.Conversation\x12\x33\n\x08metadata\x18\x02 \x01(\x0b\x32!.communication.PaginationMetadata\"\xae\x03\n\x07Message\x12\n\n\x02id\x18\x01 \x01(\t\x12\x16\n\x0e\x63onversationId\x18\x02 \x01(\t\x12-\n\nsenderType\x18\x03 \x01(\x0e\x32\x19.communication.SenderType\x12*\n\x04\x64\x61ta\x18\x04 \x01(\x0b\x32\x17.google.protobuf.StructH\x00\x88\x01\x01\x12\x17\n\nworkflowId\x18\x05 \x01(\tH\x01\x88\x01\x01\x12\x46\n\x10workflowResponse\x18\x06 \x03(\x0b\x32,.communication.Message.WorkflowResponseEntry\x12-\n\tcreatedAt\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tupdatedAt\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x1aM\n\x15WorkflowResponseEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any:\x02\x38\x01\x42\x07\n\x05_dataB\r\n\x0b_workflowId\"\xee\x02\n\x14\x43reateMessageRequest\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12-\n\nsenderType\x18\x02 \x01(\x0e\x32\x19.communication.SenderType\x12*\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32\x17.google.protobuf.StructH\x00\x88\x01\x01\x12\x17\n\nworkflowId\x18\x04 \x01(\tH\x01\x88\x01\x01\x12S\n\x10workflowResponse\x18\x05 \x03(\x0b\x32\x39.communication.CreateMessageRequest.WorkflowResponseEntry\x12\x0e\n\x06userId\x18\x06 \x01(\t\x1aM\n\x15WorkflowResponseEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any:\x02\x38\x01\x42\x07\n\x05_dataB\r\n\x0b_workflowId\"9\n\x14\x44\x65leteMessageRequest\x12\x11\n\tmessageId\x18\x01 \x01(\t\x12\x0e\n\x06userId\x18\x02 \x01(\t\"Z\n\x13ListMessagesRequest\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06userId\x18\x04 \x01(\t\"q\n\x14ListMessagesResponse\x12$\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x16.communication.Message\x12\x33\n\x08metadata\x18\x02 \x01(\x0b\x32!.communication.PaginationMetadata\"\xd2\x02\n\x04Task\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12 \n\x18globalChatConversationId\x18\x03 \x01(\t\x12\x1b\n\x13\x61gentConversationId\x18\x04 \x01(\t\x12\x0f\n\x07\x61gentId\x18\x05 \x01(\t\x12\x1a\n\rcorrelationId\x18\x06 \x01(\tH\x00\x88\x01\x01\x12-\n\ntaskStatus\x18\x07 \x01(\x0e\x32\x19.communication.TaskStatus\x12\x16\n\tsessionId\x18\x08 \x01(\tH\x01\x88\x01\x01\x12-\n\tcreatedAt\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tupdatedAt\x18\n \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x10\n\x0e_correlationIdB\x0c\n\n_sessionId\"\x85\x02\n\x11\x43reateTaskRequest\x12 \n\x18globalChatConversationId\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x1b\n\x13\x61gentConversationId\x18\x03 \x01(\t\x12\x0f\n\x07\x61gentId\x18\x04 \x01(\t\x12\x1a\n\rcorrelationId\x18\x05 \x01(\tH\x00\x88\x01\x01\x12-\n\ntaskStatus\x18\x06 \x01(\x0e\x32\x19.communication.TaskStatus\x12\x16\n\tsessionId\x18\x07 \x01(\tH\x01\x88\x01\x01\x12\x0e\n\x06userId\x18\x08 \x01(\tB\x10\n\x0e_correlationIdB\x0c\n\n_sessionId\"3\n\x11\x44\x65leteTaskRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\t\x12\x0e\n\x06userId\x18\x02 \x01(\t\"h\n\x17UpdateTaskStatusRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\t\x12-\n\ntaskStatus\x18\x02 \x01(\x0e\x32\x19.communication.TaskStatus\x12\x0e\n\x06userId\x18\x03 \x01(\t\"a\n\x10ListTasksRequest\x12 \n\x18globalChatConversationId\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06userId\x18\x04 \x01(\t\"k\n\x11ListTasksResponse\x12!\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x13.communication.Task\x12\x33\n\x08metadata\x18\x02 \x01(\x0b\x32!.communication.PaginationMetadata*P\n\x08\x43hatType\x12\x19\n\x15\x43HAT_TYPE_UNSPECIFIED\x10\x00\x12\x13\n\x0f\x43HAT_TYPE_AGENT\x10\x01\x12\x14\n\x10\x43HAT_TYPE_GLOBAL\x10\x02*Z\n\nSenderType\x12\x1b\n\x17SENDER_TYPE_UNSPECIFIED\x10\x00\x12\x14\n\x10SENDER_TYPE_USER\x10\x01\x12\x19\n\x15SENDER_TYPE_ASSISTANT\x10\x02*\xa8\x01\n\nTaskStatus\x12\x1b\n\x17TASK_STATUS_UNSPECIFIED\x10\x00\x12\x17\n\x13TASK_STATUS_RUNNING\x10\x01\x12\x19\n\x15TASK_STATUS_COMPLETED\x10\x02\x12\x16\n\x12TASK_STATUS_FAILED\x10\x03\x12\x16\n\x12TASK_STATUS_PAUSED\x10\x04\x12\x19\n\x15TASK_STATUS_CANCELLED\x10\x05\x32\x94\x08\n\x14\x43ommunicationService\x12[\n\x12\x63reateConversation\x12(.communication.CreateConversationRequest\x1a\x1b.communication.Conversation\x12U\n\x0fgetConversation\x12%.communication.GetConversationRequest\x1a\x1b.communication.Conversation\x12V\n\x12\x64\x65leteConversation\x12(.communication.DeleteConversationRequest\x1a\x16.google.protobuf.Empty\x12\x66\n\x11listConversations\x12\'.communication.ListConversationsRequest\x1a(.communication.ListConversationsResponse\x12\x62\n\x18updateConversationTokens\x12..communication.UpdateConversationTokensRequest\x1a\x16.google.protobuf.Empty\x12L\n\rcreateMessage\x12#.communication.CreateMessageRequest\x1a\x16.communication.Message\x12L\n\rdeleteMessage\x12#.communication.DeleteMessageRequest\x1a\x16.google.protobuf.Empty\x12W\n\x0clistMessages\x12\".communication.ListMessagesRequest\x1a#.communication.ListMessagesResponse\x12\x43\n\ncreateTask\x12 .communication.CreateTaskRequest\x1a\x13.communication.Task\x12\x46\n\ndeleteTask\x12 .communication.DeleteTaskRequest\x1a\x16.google.protobuf.Empty\x12N\n\tlistTasks\x12\x1f.communication.ListTasksRequest\x1a .communication.ListTasksResponse\x12R\n\x10updateTaskStatus\x12&.communication.UpdateTaskStatusRequest\x1a\x16.google.protobuf.Emptyb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'communication_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_MESSAGE_WORKFLOWRESPONSEENTRY']._loaded_options = None
  _globals['_MESSAGE_WORKFLOWRESPONSEENTRY']._serialized_options = b'8\001'
  _globals['_CREATEMESSAGEREQUEST_WORKFLOWRESPONSEENTRY']._loaded_options = None
  _globals['_CREATEMESSAGEREQUEST_WORKFLOWRESPONSEENTRY']._serialized_options = b'8\001'
  _globals['_CHATTYPE']._serialized_start=3294
  _globals['_CHATTYPE']._serialized_end=3374
  _globals['_SENDERTYPE']._serialized_start=3376
  _globals['_SENDERTYPE']._serialized_end=3466
  _globals['_TASKSTATUS']._serialized_start=3469
  _globals['_TASKSTATUS']._serialized_end=3637
  _globals['_CONVERSATION']._serialized_start=158
  _globals['_CONVERSATION']._serialized_end=414
  _globals['_CREATECONVERSATIONREQUEST']._serialized_start=416
  _globals['_CREATECONVERSATIONREQUEST']._serialized_end=536
  _globals['_GETCONVERSATIONREQUEST']._serialized_start=538
  _globals['_GETCONVERSATIONREQUEST']._serialized_end=602
  _globals['_DELETECONVERSATIONREQUEST']._serialized_start=604
  _globals['_DELETECONVERSATIONREQUEST']._serialized_end=671
  _globals['_LISTCONVERSATIONSREQUEST']._serialized_start=674
  _globals['_LISTCONVERSATIONSREQUEST']._serialized_end=822
  _globals['_UPDATECONVERSATIONTOKENSREQUEST']._serialized_start=825
  _globals['_UPDATECONVERSATIONTOKENSREQUEST']._serialized_end=984
  _globals['_PAGINATIONMETADATA']._serialized_start=987
  _globals['_PAGINATIONMETADATA']._serialized_end=1127
  _globals['_LISTCONVERSATIONSRESPONSE']._serialized_start=1129
  _globals['_LISTCONVERSATIONSRESPONSE']._serialized_end=1252
  _globals['_MESSAGE']._serialized_start=1255
  _globals['_MESSAGE']._serialized_end=1685
  _globals['_MESSAGE_WORKFLOWRESPONSEENTRY']._serialized_start=1584
  _globals['_MESSAGE_WORKFLOWRESPONSEENTRY']._serialized_end=1661
  _globals['_CREATEMESSAGEREQUEST']._serialized_start=1688
  _globals['_CREATEMESSAGEREQUEST']._serialized_end=2054
  _globals['_CREATEMESSAGEREQUEST_WORKFLOWRESPONSEENTRY']._serialized_start=1584
  _globals['_CREATEMESSAGEREQUEST_WORKFLOWRESPONSEENTRY']._serialized_end=1661
  _globals['_DELETEMESSAGEREQUEST']._serialized_start=2056
  _globals['_DELETEMESSAGEREQUEST']._serialized_end=2113
  _globals['_LISTMESSAGESREQUEST']._serialized_start=2115
  _globals['_LISTMESSAGESREQUEST']._serialized_end=2205
  _globals['_LISTMESSAGESRESPONSE']._serialized_start=2207
  _globals['_LISTMESSAGESRESPONSE']._serialized_end=2320
  _globals['_TASK']._serialized_start=2323
  _globals['_TASK']._serialized_end=2661
  _globals['_CREATETASKREQUEST']._serialized_start=2664
  _globals['_CREATETASKREQUEST']._serialized_end=2925
  _globals['_DELETETASKREQUEST']._serialized_start=2927
  _globals['_DELETETASKREQUEST']._serialized_end=2978
  _globals['_UPDATETASKSTATUSREQUEST']._serialized_start=2980
  _globals['_UPDATETASKSTATUSREQUEST']._serialized_end=3084
  _globals['_LISTTASKSREQUEST']._serialized_start=3086
  _globals['_LISTTASKSREQUEST']._serialized_end=3183
  _globals['_LISTTASKSRESPONSE']._serialized_start=3185
  _globals['_LISTTASKSRESPONSE']._serialized_end=3292
  _globals['_COMMUNICATIONSERVICE']._serialized_start=3640
  _globals['_COMMUNICATIONSERVICE']._serialized_end=4684
# @@protoc_insertion_point(module_scope)
