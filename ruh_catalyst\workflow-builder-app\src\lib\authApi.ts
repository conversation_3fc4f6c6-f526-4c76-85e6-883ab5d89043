/**
 * Authentication API client for workflow-builder-poc
 * This module provides functions for authentication operations.
 * Matches the implementation in ruh-app-fe for consistency.
 */

import { authApi as centralizedAuth<PERSON><PERSON> } from "@/utils/axios";
import { clearAuthCookies, getAccessToken, setAuthCookies } from "./cookies";
import {
  getClientAccessToken,
  checkClientAccessToken,
  setClientAuthCookies,
  clearClientAuthCookies,
} from "./clientCookies";
import { LoginType, SignupType, ResetPasswordType } from "./schemas/auth";
import { useUserStore } from "@/store/userStore";
import { LoginResponse, SignupResponse, LoginResult, TokenResponse } from "@/shared/interfaces";
import { API_BASE_URL, API_ENDPOINTS } from "./apiConfig";
import { workflowsRoute, loginRoute } from "@/shared/routes";
/**
 * Checks if a user has completed the onboarding process
 * by verifying they have both department and jobRole fields.
 *
 * @returns {Promise<boolean>} True if onboarding is complete, false otherwise
 */
export async function hasCompletedOnboarding(): Promise<boolean> {
  try {
    const userDetails = await authApi.getCurrentUser();
    return Boolean(userDetails?.department && userDetails?.jobRole);
  } catch (error: any) {
    // Check if this request has already been through the axios interceptor
    const originalRequest = error.config as any;
    if (originalRequest && originalRequest._retry) {
      // This request has already been through the interceptor and still failed
      // This means token refresh failed, so we should redirect to login
      throw error;
    }

    // For other errors, return false to allow normal flow
    return false;
  }
}

// Authentication API functions
export const authApi = {
  /**
   * Login user with email and password
   * @param data LoginType from zod schema
   */
  login: async (data: LoginType): Promise<LoginResult> => {
    const { email, password } = data;
    try {
      // Prepare login payload
      const payload = {
        email,
        password,
      };

      const response = await centralizedAuthApi.post<LoginResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        payload,
      );

      if (!response.data.access_token) {
        throw new Error("Login failed: Unexpected response from server.");
      }

      // Set auth cookies after successful login (both server-side and client-side)
      await setAuthCookies(
        response.data.access_token,
        response.data.refresh_token,
        response.data.accessTokenAge || 36000,
        response.data.refreshTokenAge || 86400,
      );

      // Also set client-side cookies for immediate access
      if (typeof window !== "undefined") {
        setClientAuthCookies(
          response.data.access_token,
          response.data.refresh_token,
          response.data.accessTokenAge || 36000,
          response.data.refreshTokenAge || 86400,
        );
        console.log("Client-side auth cookies set after login");
      }

      let redirectPath = workflowsRoute; // Use workflows route directly
      try {
        // Fetch user details after successful login
        const userDetails = await authApi.getCurrentUser();

        // Update the user store with user data and access token
        useUserStore.getState().setUser({
          fullName: userDetails.fullName,
          email: userDetails.email,
          accessToken: response.data.access_token,
        });

        // Keep using workflows route
        redirectPath = workflowsRoute;
      } catch (userError) {
        console.error("Failed to fetch user details:", userError);
        // Keep redirectPath as '/workflows' if fetching user details fails
      }

      // Return both login data and the determined redirect path
      return {
        loginData: response.data,
        redirectPath,
      };
    } catch (error: any) {
      // Clear user store on login failure
      useUserStore.getState().clearUser();

      if (error.response?.status === 404) {
        throw new Error("User not found.");
      }
      if (error.response?.status === 412) {
        throw new Error("Account inactive. Please check your email for verification.");
      }
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Invalid Credentials",
      );
    }
  },

  /**
   * Register new user
   * @param data SignupType from zod schema
   */
  signup: async (data: SignupType): Promise<SignupResponse> => {
    try {
      const { email, fullName, password } = data;
      const payload = {
        full_name: fullName,
        email,
        password,
      };
      const response = await centralizedAuthApi.post<SignupResponse>(
        API_ENDPOINTS.AUTH.REGISTER,
        payload,
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        throw new Error(error.response?.data?.detail || "Email already registered.");
      }
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Signup failed",
      );
    }
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    try {
      // Clear server-side auth cookies
      await clearAuthCookies();

      // Clear client-side auth cookies
      if (typeof window !== "undefined") {
        clearClientAuthCookies();
        console.log("Client-side auth cookies cleared during logout");
      }

      // Clear user store (including accessToken)
      useUserStore.getState().clearUser();
      console.log("User store cleared during logout");

      // Redirect to login page
      if (typeof window !== "undefined") {
        window.location.href = loginRoute;
      }
    } catch (error: any) {
      console.error("Error during logout:", error);

      // Even if there's an error, try to clear everything
      await clearAuthCookies();
      if (typeof window !== "undefined") {
        clearClientAuthCookies();
      }
      useUserStore.getState().clearUser();

      if (typeof window !== "undefined") {
        window.location.href = loginRoute;
      }
    }
  },

  /**
   * Forgot password
   * @param email Email address for password reset
   */
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    try {
      const response = await centralizedAuthApi.post<{ message: string }>(
        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        null, // No request body for this POST request
        { params: { email } }, // Pass email as query parameter in the config object
      );
      return response.data; // Return the success message
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || // Use detail field if available
          error.response?.data?.message ||
          "Failed to send password reset email",
      );
    }
  },

  /**
   * Reset user's password using OTP token
   * @param token The OTP token from the reset link
   * @param data ResetPasswordType containing newPassword and confirmNewPassword
   */
  resetPassword: async (token: string, data: ResetPasswordType): Promise<{ message: string }> => {
    const { newPassword, confirmNewPassword } = data;
    try {
      // Construct the payload for the API
      const payload = {
        token,
        new_password: newPassword,
        confirm_new_password: confirmNewPassword,
      };

      // Call the actual API endpoint
      const response = await centralizedAuthApi.post<{ message: string }>(
        API_ENDPOINTS.AUTH.UPDATE_PASSWORD,
        payload,
      );

      return response.data; // Return the success message from the API
    } catch (error: any) {
      throw new Error(
        // Use error details provided by the API response preferentially
        error.response?.data?.detail || error.response?.data?.message || "Password reset failed",
      );
    }
  },

  /**
   * Update user's password
   * @param data Object containing token and password
   */
  updatePassword: async (data: {
    token: string;
    password: string;
  }): Promise<{ message: string }> => {
    try {
      // Construct the payload for the API
      const payload = {
        token: data.token,
        new_password: data.password,
        confirm_new_password: data.password,
      };

      // Call the actual API endpoint
      const response = await centralizedAuthApi.post<{ message: string }>(
        API_ENDPOINTS.AUTH.UPDATE_PASSWORD,
        payload,
      );

      return response.data; // Return the success message from the API
    } catch (error: any) {
      throw new Error(
        // Use error details provided by the API response preferentially
        error.response?.data?.detail || error.response?.data?.message || "Password update failed",
      );
    }
  },

  /**
   * Verify email using OTP token
   * @param token The OTP token from the verification link
   */
  verifyEmailOtp: async (token: string): Promise<{ message: string }> => {
    try {
      const payload = { token };
      const response = await centralizedAuthApi.post<{ message: string }>(
        API_ENDPOINTS.AUTH.VERIFY_EMAIL_OTP,
        payload,
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Email verification failed",
      );
    }
  },

  /**
   * Get current user information
   */
  getCurrentUser: async () => {
    try {
      console.log("Making request to /users/me");
      // The centralized auth API will automatically handle authorization headers
      const response = await centralizedAuthApi.get(`/users/me`);
      console.log("Successfully retrieved user data");
      return response.data;
    } catch (error: any) {
      console.error("Get current user error:", error);

      // Check if this is a 403 Forbidden error
      if (error.response?.status === 403) {
        console.log("Authentication error: 403 Forbidden. Token may be invalid or expired.");
        // Clear user state and cookies to force re-login
        useUserStore.getState().clearUser();
        if (typeof window !== "undefined") {
          clearClientAuthCookies();
        }
      }

      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to fetch user details",
      );
    }
  },

  /**
   * Verify current auth session by attempting to fetch user data.
   */
  isLoggedIn: async (): Promise<boolean> => {
    try {
      await authApi.getCurrentUser(); // Attempt to fetch user data
      return true; // If successful, user is considered logged in
    } catch (error) {
      // Any error (including 401/403 from getCurrentUser) means session is not valid
      return false;
    }
  },

  /**
   * Check if user is authenticated based on cookie presence
   * Uses client-side cookie access when in browser environment
   */
  isAuthenticated: async (): Promise<boolean> => {
    // Check if we're in a browser environment
    if (typeof window !== "undefined") {
      // Use client-side cookie access
      const isAuth = checkClientAccessToken();
      console.log("Client-side isAuthenticated check:", isAuth);
      return isAuth;
    } else {
      // Use server-side cookie access
      const token = await getAccessToken();
      return !!token;
    }
  },

  /**
   * Get the access token from cookies
   * Uses client-side cookie access when in browser environment
   */
  getAccessToken: async (): Promise<string> => {
    // Check if we're in a browser environment
    if (typeof window !== "undefined") {
      // Use client-side cookie access
      return getClientAccessToken();
    } else {
      // Use server-side cookie access
      return (await getAccessToken()) || "";
    }
  },

  /**
   * Generate new access token using refresh token
   * @param refreshToken The refresh token to use for generating a new access token
   */
  generateAccessToken: async (refreshToken: string): Promise<TokenResponse> => {
    try {
      const response = await centralizedAuthApi.post<TokenResponse>(
        `/auth/access-token`,
        {},
        {
          params: { refresh_token: refreshToken },
        },
      );

      // Update the access token in cookies if successful
      if (response.data.success && response.data.access_token) {
        // Calculate token age in seconds from tokenExpireAt
        const expireAt = new Date(response.data.tokenExpireAt).getTime();
        const now = new Date().getTime();
        const accessTokenAge = Math.floor((expireAt - now) / 1000);

        await setAuthCookies(response.data.access_token, null, accessTokenAge, null);

        // Also update the access token in the user store
        const currentUser = useUserStore.getState().user;
        if (currentUser) {
          useUserStore.getState().setUser({
            ...currentUser,
            accessToken: response.data.access_token,
          });
        }
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to generate new access token",
      );
    }
  },

  /**
   * Initiates the Google OAuth login flow by redirecting the browser.
   */
  googleLogin: async (): Promise<void> => {
    try {
      // Construct the full URL to the backend's Google login initiation endpoint.
      let googleLoginUrl = `${API_BASE_URL}/auth/google-login`;

      // Redirect the user's browser.
      window.location.href = googleLoginUrl;
    } catch (error) {
      console.error("Error during Google login:", error);
      // Fallback to basic Google login without FCM token
      window.location.href = `${API_BASE_URL}/auth/google-login`;
    }
  },

  /**
   * Handles the final steps after Google OAuth callback.
   * Assumes the backend set auth cookies before redirecting back to the frontend.
   */
  finalizeGoogleLogin: async (): Promise<void> => {
    try {
      // Fetch user details using the cookies potentially set by the backend callback
      const userDetails = await authApi.getCurrentUser();

      // Get the access token from cookies
      const accessToken = await getAccessToken();

      // Update global user store with user details and access token
      useUserStore.getState().setUser({
        fullName: userDetails.fullName,
        email: userDetails.email,
        accessToken: accessToken,
      });
    } catch (error) {
      useUserStore.getState().clearUser(); // Reset state on error
      console.error("Failed to retrieve user details after Google login:", error);
    }
  },
};

export default authApi;
