/**
 * ToolHandle component for rendering individual tool handles
 */

import React, { useState, useCallback, memo } from "react";
import { Handle, Position } from "reactflow";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export interface ToolHandleProps {
  id: string;
  displayName: string;
  position: "left" | "right";
  isConnectable?: boolean;
  isConnected?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  index?: number;
  totalHandles?: number;
  spacing?: number;
  onConnect?: (handleId: string) => void;
  onHover?: (handleId: string, isHovering: boolean) => void;
  onFocus?: (handleId: string, isFocused: boolean) => void;
}

const ToolHandle = memo(({
  id,
  displayName,
  position,
  isConnectable = true,
  isConnected = false,
  hasError = false,
  errorMessage,
  index = 0,
  totalHandles = 1,
  spacing = 30,
  onConnect,
  onHover,
  onFocus,
}: ToolHandleProps) => {
  const [isHovering, setIsHovering] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Calculate position
  const top = spacing * (index + 1);

  // Handle mouse events
  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
    onHover?.(id, true);
  }, [id, onHover]);

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
    onHover?.(id, false);
  }, [id, onHover]);

  // Handle focus events
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocus?.(id, true);
  }, [id, onFocus]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onFocus?.(id, false);
  }, [id, onFocus]);

  // Handle click/connection
  const handleClick = useCallback(() => {
    if (isConnectable && onConnect) {
      onConnect(id);
    }
  }, [id, isConnectable, onConnect]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  // Generate CSS classes
  const handleClasses = [
    "tool-handle",
    isHovering && "tool-handle-hover",
    isFocused && "tool-handle-focus",
    isConnected && "tool-handle-connected",
    !isConnectable && "tool-handle-disabled",
    hasError && "tool-handle-error",
  ].filter(Boolean).join(" ");

  const containerClasses = [
    "tool-handle-container",
    position === "right" && "tool-handle-right",
  ].filter(Boolean).join(" ");

  const labelClasses = [
    "tool-handle-label",
    isConnected && "tool-handle-label-connected",
  ].filter(Boolean).join(" ");

  return (
    <div
      data-testid={`tool-handle-container-${id}`}
      className={containerClasses}
      style={{ top }}
    >
      <TooltipProvider delayDuration={150}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="group flex items-center">
              <Handle
                type="target"
                position={position === "left" ? Position.Left : Position.Right}
                id={id}
                data-testid={`tool-handle-${id}`}
                data-tool-handle="true"
                className={handleClasses}
                style={{
                  width: "10px",
                  height: "10px",
                  borderRadius: "5px",
                  border: "2px solid var(--background)",
                  [position === "left" ? "left" : "right"]: "-5px",
                  zIndex: 50,
                }}
                isConnectable={isConnectable}
                tabIndex={isConnectable ? 0 : -1}
                role="button"
                aria-label={`Tool handle: ${displayName}`}
                aria-describedby={`tool-handle-desc-${id}`}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onClick={handleClick}
                onKeyDown={handleKeyDown}
              />
              
              {/* Handle label */}
              <div
                data-testid={`tool-handle-label-${id}`}
                className={labelClasses}
                style={{
                  marginLeft: position === "left" ? "8px" : "0",
                  marginRight: position === "right" ? "8px" : "0",
                }}
              >
                {displayName}
              </div>
            </div>
          </TooltipTrigger>
          
          <TooltipContent
            side={position === "left" ? "left" : "right"}
            className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
          >
            <div className="font-medium">{displayName}</div>
            <div className="text-muted-foreground text-[10px]">
              Connect workflow components as tools
            </div>
            {hasError && errorMessage && (
              <div className="text-destructive text-[10px] mt-1">
                {errorMessage}
              </div>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Hidden description for screen readers */}
      <div
        id={`tool-handle-desc-${id}`}
        className="sr-only"
      >
        Connect workflow components as tools to this AgenticAI node
      </div>

      {/* Error message display */}
      {hasError && errorMessage && (
        <div className="text-destructive text-[9px] mt-1">
          {errorMessage}
        </div>
      )}
    </div>
  );
});

ToolHandle.displayName = "ToolHandle";

export default ToolHandle;
