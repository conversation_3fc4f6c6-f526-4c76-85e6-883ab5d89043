import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { validateWorkflow } from "./workflowValidation";
import { ValidationResult, WorkflowValidationOptions } from "./types";

/**
 * Validate a workflow before saving
 * 
 * This validation is less strict - it allows saving workflows with missing fields
 * but still checks for structural issues.
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A validation result
 */
export function validateBeforeSave(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ValidationResult {
  const options: WorkflowValidationOptions = {
    validateConnectivity: true,
    collectMissingFields: false, // Don't collect missing fields for save
    validateFieldTypes: true,
    validateCycles: true,
  };

  return validateWorkflow(nodes, edges, options);
}

/**
 * Validate a workflow before execution
 * 
 * This validation is more strict - it collects missing fields and checks
 * for connectivity issues.
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A validation result with missing fields
 */
export function validateBeforeExecution(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ValidationResult {
  const options: WorkflowValidationOptions = {
    validateConnectivity: true,
    collectMissingFields: true, // Collect missing fields for execution
    validateFieldTypes: true,
    validateCycles: true,
  };

  return validateWorkflow(nodes, edges, options);
}

/**
 * Validate a workflow in real-time during editing
 * 
 * This validation is lightweight - it checks for structural issues
 * but doesn't collect missing fields or validate connectivity.
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A validation result
 */
export function validateDuringEditing(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ValidationResult {
  const options: WorkflowValidationOptions = {
    validateConnectivity: false, // Skip connectivity validation during editing
    collectMissingFields: false, // Don't collect missing fields during editing
    validateFieldTypes: true,
    validateCycles: false, // Skip cycle detection during editing
  };

  return validateWorkflow(nodes, edges, options);
}
