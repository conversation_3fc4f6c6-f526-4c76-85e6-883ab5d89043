# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import agent_graph_pb2 as agent__graph__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in agent_graph_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AgentGraphServiceStub(object):
    """The AgentGraph service definition
    This service focuses on graph-specific operations for agents
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.createAgent = channel.unary_unary(
                '/agent_graph.AgentGraphService/createAgent',
                request_serializer=agent__graph__pb2.CreateAgentRequest.SerializeToString,
                response_deserializer=agent__graph__pb2.CreateAgentResponse.FromString,
                _registered_method=True)
        self.getAgent = channel.unary_unary(
                '/agent_graph.AgentGraphService/getAgent',
                request_serializer=agent__graph__pb2.GetAgentRequest.SerializeToString,
                response_deserializer=agent__graph__pb2.AgentResponse.FromString,
                _registered_method=True)
        self.listAgentsByUserId = channel.unary_unary(
                '/agent_graph.AgentGraphService/listAgentsByUserId',
                request_serializer=agent__graph__pb2.ListAgentsByUserIdRequest.SerializeToString,
                response_deserializer=agent__graph__pb2.ListAgentsResponse.FromString,
                _registered_method=True)
        self.getAllAgentsFromOrganisation = channel.unary_unary(
                '/agent_graph.AgentGraphService/getAllAgentsFromOrganisation',
                request_serializer=agent__graph__pb2.GetAllAgentsFromOrganisationRequest.SerializeToString,
                response_deserializer=agent__graph__pb2.ListAgentsResponse.FromString,
                _registered_method=True)
        self.getAgentsFromDepartment = channel.unary_unary(
                '/agent_graph.AgentGraphService/getAgentsFromDepartment',
                request_serializer=agent__graph__pb2.GetAgentsFromDepartmentRequest.SerializeToString,
                response_deserializer=agent__graph__pb2.ListAgentsResponse.FromString,
                _registered_method=True)
        self.checkUserAgentAccess = channel.unary_unary(
                '/agent_graph.AgentGraphService/checkUserAgentAccess',
                request_serializer=agent__graph__pb2.CheckUserAgentAccessRequest.SerializeToString,
                response_deserializer=agent__graph__pb2.CheckAccessResponse.FromString,
                _registered_method=True)


class AgentGraphServiceServicer(object):
    """The AgentGraph service definition
    This service focuses on graph-specific operations for agents
    """

    def createAgent(self, request, context):
        """Create a new agent with relationships to owner, department, and users
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAgent(self, request, context):
        """Get agent details by ID
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listAgentsByUserId(self, request, context):
        """Get all agents that a user has access to
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAllAgentsFromOrganisation(self, request, context):
        """Get all agents from all departments of an organization
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAgentsFromDepartment(self, request, context):
        """Get all agents from a specific department
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def checkUserAgentAccess(self, request, context):
        """Check if a user has access to a specific agent
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AgentGraphServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'createAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.createAgent,
                    request_deserializer=agent__graph__pb2.CreateAgentRequest.FromString,
                    response_serializer=agent__graph__pb2.CreateAgentResponse.SerializeToString,
            ),
            'getAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.getAgent,
                    request_deserializer=agent__graph__pb2.GetAgentRequest.FromString,
                    response_serializer=agent__graph__pb2.AgentResponse.SerializeToString,
            ),
            'listAgentsByUserId': grpc.unary_unary_rpc_method_handler(
                    servicer.listAgentsByUserId,
                    request_deserializer=agent__graph__pb2.ListAgentsByUserIdRequest.FromString,
                    response_serializer=agent__graph__pb2.ListAgentsResponse.SerializeToString,
            ),
            'getAllAgentsFromOrganisation': grpc.unary_unary_rpc_method_handler(
                    servicer.getAllAgentsFromOrganisation,
                    request_deserializer=agent__graph__pb2.GetAllAgentsFromOrganisationRequest.FromString,
                    response_serializer=agent__graph__pb2.ListAgentsResponse.SerializeToString,
            ),
            'getAgentsFromDepartment': grpc.unary_unary_rpc_method_handler(
                    servicer.getAgentsFromDepartment,
                    request_deserializer=agent__graph__pb2.GetAgentsFromDepartmentRequest.FromString,
                    response_serializer=agent__graph__pb2.ListAgentsResponse.SerializeToString,
            ),
            'checkUserAgentAccess': grpc.unary_unary_rpc_method_handler(
                    servicer.checkUserAgentAccess,
                    request_deserializer=agent__graph__pb2.CheckUserAgentAccessRequest.FromString,
                    response_serializer=agent__graph__pb2.CheckAccessResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'agent_graph.AgentGraphService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('agent_graph.AgentGraphService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AgentGraphService(object):
    """The AgentGraph service definition
    This service focuses on graph-specific operations for agents
    """

    @staticmethod
    def createAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_graph.AgentGraphService/createAgent',
            agent__graph__pb2.CreateAgentRequest.SerializeToString,
            agent__graph__pb2.CreateAgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_graph.AgentGraphService/getAgent',
            agent__graph__pb2.GetAgentRequest.SerializeToString,
            agent__graph__pb2.AgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listAgentsByUserId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_graph.AgentGraphService/listAgentsByUserId',
            agent__graph__pb2.ListAgentsByUserIdRequest.SerializeToString,
            agent__graph__pb2.ListAgentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAllAgentsFromOrganisation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_graph.AgentGraphService/getAllAgentsFromOrganisation',
            agent__graph__pb2.GetAllAgentsFromOrganisationRequest.SerializeToString,
            agent__graph__pb2.ListAgentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAgentsFromDepartment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_graph.AgentGraphService/getAgentsFromDepartment',
            agent__graph__pb2.GetAgentsFromDepartmentRequest.SerializeToString,
            agent__graph__pb2.ListAgentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def checkUserAgentAccess(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_graph.AgentGraphService/checkUserAgentAccess',
            agent__graph__pb2.CheckUserAgentAccessRequest.SerializeToString,
            agent__graph__pb2.CheckAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
