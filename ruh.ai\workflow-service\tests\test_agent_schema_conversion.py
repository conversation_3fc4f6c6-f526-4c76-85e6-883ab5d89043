#!/usr/bin/env python3
"""
Test script to verify agent node schema conversion works correctly.
"""

import json
import sys
from pathlib import Path

# Add the app directory to the path
sys.path.append(str(Path(__file__).parent / "app"))

from services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema


def test_agent_node_conversion():
    """Test that agent nodes are converted correctly to transition schema."""
    
    # Sample workflow with an agent node
    sample_workflow = {
        "nodes": [
            {
                "id": "start-1",
                "data": {
                    "type": "start",
                    "originalType": "StartNode",
                    "definition": {
                        "name": "Start",
                        "inputs": [],
                        "outputs": [{"name": "output", "output_type": "string"}]
                    }
                }
            },
            {
                "id": "agent-1", 
                "data": {
                    "type": "agent",
                    "definition": {
                        "name": "Customer Support Agent",
                        "inputs": [
                            {"name": "query", "input_type": "string", "required": True},
                            {"name": "context", "input_type": "object", "required": False}
                        ],
                        "outputs": [
                            {"name": "response", "output_type": "string"},
                            {"name": "session_data", "output_type": "object"}
                        ]
                    },
                    "config": {
                        "agent_type": "component",
                        "execution_type": "interactive", 
                        "query": "How can I help you today?",
                        "agent_config": {
                            "id": "support-agent-001",
                            "name": "Customer Support Assistant",
                            "model_config": {
                                "model": "gpt-4o-mini",
                                "temperature": 0.7
                            }
                        },
                        "session_timeout": 1800,
                        "variables": {"department": "support"},
                        "use_knowledge": True
                    }
                }
            },
            {
                "id": "output-1",
                "data": {
                    "type": "output",
                    "originalType": "OutputNode", 
                    "definition": {
                        "name": "Output",
                        "inputs": [{"name": "result", "input_type": "string"}],
                        "outputs": []
                    }
                }
            }
        ],
        "edges": [
            {
                "id": "edge-1",
                "source": "start-1",
                "target": "agent-1",
                "sourceHandle": "output_output",
                "targetHandle": "query_input"
            },
            {
                "id": "edge-2", 
                "source": "agent-1",
                "target": "output-1",
                "sourceHandle": "response_output",
                "targetHandle": "result_input"
            }
        ],
        "mcp_configs": []
    }
    
    print("🧪 Testing Agent Node Schema Conversion")
    print("=" * 50)
    
    try:
        # Convert the workflow
        result = convert_workflow_to_transition_schema(sample_workflow)
        
        print("✅ Conversion successful!")
        print("\n📋 Converted Schema:")
        print(json.dumps(result, indent=2))
        
        # Verify agent transition exists
        agent_transition = None
        for transition in result.get("transitions", []):
            if transition["id"] == "transition-agent-1":
                agent_transition = transition
                break
                
        if not agent_transition:
            print("❌ Agent transition not found!")
            return False
            
        print(f"\n🎯 Agent Transition Details:")
        print(f"   Execution Type: {agent_transition['execution_type']}")
        print(f"   Node ID: {agent_transition['node_info']['node_id']}")
        
        # Verify execution type is 'agent'
        if agent_transition["execution_type"] != "agent":
            print(f"❌ Expected execution_type 'agent', got '{agent_transition['execution_type']}'")
            return False
            
        # Verify tool configuration
        tools = agent_transition["node_info"]["tools_to_use"]
        if not tools:
            print("❌ No tools found in agent transition!")
            return False
            
        agent_tool = tools[0]
        print(f"   Tool Name: {agent_tool['tool_name']}")
        print(f"   Tool Parameters: {len(agent_tool['tool_params']['items'])} items")
        
        # Verify required agent parameters
        param_names = [item["field_name"] for item in agent_tool["tool_params"]["items"]]
        required_params = ["agent_type", "execution_type", "query", "agent_config"]
        
        for param in required_params:
            if param not in param_names:
                print(f"❌ Missing required parameter: {param}")
                return False
                
        print(f"   Parameters: {', '.join(param_names)}")
        
        # Verify agent-specific parameters
        for item in agent_tool["tool_params"]["items"]:
            if item["field_name"] == "execution_type":
                if item["field_value"] != "interactive":
                    print(f"❌ Expected execution_type 'interactive', got '{item['field_value']}'")
                    return False
            elif item["field_name"] == "agent_type":
                if item["field_value"] != "component":
                    print(f"❌ Expected agent_type 'component', got '{item['field_value']}'")
                    return False
                    
        print("\n✅ All agent node conversion tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_agent_node_conversion()
    sys.exit(0 if success else 1)
