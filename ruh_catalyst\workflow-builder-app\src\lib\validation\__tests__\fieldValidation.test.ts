import { describe, it, expect } from "jest";
import { validate<PERSON>ield, validate<PERSON>ields, collectMissingRequiredFields } from "../fieldValidation";
import { ValidationErrorCode } from "../types";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { InputDefinition, InputRequirementRule } from "@/types";

describe("Field Validation", () => {
  describe("validateField", () => {
    it("should validate a valid string field", () => {
      const inputDef: InputDefinition = {
        name: "text",
        display_name: "Text",
        input_type: "string",
        required: true,
        min_length: 3,
        max_length: 10,
      };

      const value = "Hello";
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(0);
    });

    it("should detect missing required field", () => {
      const inputDef: InputDefinition = {
        name: "text",
        display_name: "Text",
        input_type: "string",
        required: true,
      };

      const value = undefined;
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_REQUIRED);
    });

    it("should validate optional field with no value", () => {
      const inputDef: InputDefinition = {
        name: "text",
        display_name: "Text",
        input_type: "string",
        required: false,
      };

      const value = undefined;
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(0);
    });

    it("should detect string length below minimum", () => {
      const inputDef: InputDefinition = {
        name: "text",
        display_name: "Text",
        input_type: "string",
        required: true,
        min_length: 5,
      };

      const value = "Hi";
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_STRING_LENGTH);
    });

    it("should detect string length above maximum", () => {
      const inputDef: InputDefinition = {
        name: "text",
        display_name: "Text",
        input_type: "string",
        required: true,
        max_length: 5,
      };

      const value = "Hello World";
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_STRING_LENGTH);
    });

    it("should validate a valid number field", () => {
      const inputDef: InputDefinition = {
        name: "number",
        display_name: "Number",
        input_type: "int",
        required: true,
        min_value: 1,
        max_value: 10,
      };

      const value = 5;
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(0);
    });

    it("should detect number below minimum", () => {
      const inputDef: InputDefinition = {
        name: "number",
        display_name: "Number",
        input_type: "int",
        required: true,
        min_value: 5,
      };

      const value = 3;
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_NUMBER_RANGE);
    });

    it("should detect number above maximum", () => {
      const inputDef: InputDefinition = {
        name: "number",
        display_name: "Number",
        input_type: "int",
        required: true,
        max_value: 5,
      };

      const value = 10;
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_NUMBER_RANGE);
    });

    it("should validate a valid pattern match", () => {
      const inputDef: InputDefinition = {
        name: "email",
        display_name: "Email",
        input_type: "string",
        required: true,
        pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
      };

      const value = "<EMAIL>";
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(0);
    });

    it("should detect pattern mismatch", () => {
      const inputDef: InputDefinition = {
        name: "email",
        display_name: "Email",
        input_type: "string",
        required: true,
        pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
      };

      const value = "not-an-email";
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_PATTERN_MISMATCH);
    });

    it("should validate object with required keys", () => {
      const inputDef: InputDefinition = {
        name: "config",
        display_name: "Configuration",
        input_type: "object",
        required: true,
        required_keys: ["name", "value"],
      };

      const value = { name: "test", value: 123 };
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(0);
    });

    it("should detect missing required keys in object", () => {
      const inputDef: InputDefinition = {
        name: "config",
        display_name: "Configuration",
        input_type: "object",
        required: true,
        required_keys: ["name", "value"],
      };

      const value = { name: "test" }; // Missing 'value' key
      const errors = validateField(inputDef, value, "node-1");
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.FIELD_MISSING_REQUIRED_KEYS);
    });
  });

  describe("validateFields", () => {
    it("should validate all fields in a node", () => {
      const node = {
        id: "node-1",
        data: {
          definition: {
            inputs: [
              {
                name: "text",
                display_name: "Text",
                input_type: "string",
                required: true,
              },
              {
                name: "number",
                display_name: "Number",
                input_type: "int",
                required: true,
              },
            ],
          },
          config: {
            text: "Hello",
            number: 5,
          },
        },
      } as Node<WorkflowNodeData>;

      const result = validateFields(node);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should detect invalid fields in a node", () => {
      const node = {
        id: "node-1",
        data: {
          definition: {
            inputs: [
              {
                name: "text",
                display_name: "Text",
                input_type: "string",
                required: true,
              },
              {
                name: "number",
                display_name: "Number",
                input_type: "int",
                required: true,
              },
            ],
          },
          config: {
            text: "", // Empty string for required field
            number: 5,
          },
        },
      } as Node<WorkflowNodeData>;

      const result = validateFields(node);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.FIELD_REQUIRED);
    });
  });

  describe("collectMissingRequiredFields", () => {
    it("should collect missing required fields", () => {
      const nodes = [
        {
          id: "node-1",
          data: {
            label: "Node 1",
            definition: {
              name: "TestNode",
              inputs: [
                {
                  name: "text",
                  display_name: "Text",
                  input_type: "string",
                  required: true,
                },
                {
                  name: "number",
                  display_name: "Number",
                  input_type: "int",
                  required: true,
                },
              ],
            },
            config: {
              // Missing both required fields
            },
          },
        },
      ] as Node<WorkflowNodeData>[];

      const connectedNodes = new Set(["node-1"]);
      const missingFields = collectMissingRequiredFields(nodes, connectedNodes);

      expect(missingFields).toHaveLength(2);
      expect(missingFields[0].nodeId).toBe("node-1");
      expect(missingFields[0].name).toBe("text");
      expect(missingFields[1].nodeId).toBe("node-1");
      expect(missingFields[1].name).toBe("number");
    });

    it("should only collect fields from connected nodes", () => {
      const nodes = [
        {
          id: "node-1",
          data: {
            label: "Node 1",
            definition: {
              name: "TestNode",
              inputs: [
                {
                  name: "text",
                  display_name: "Text",
                  input_type: "string",
                  required: true,
                },
              ],
            },
            config: {
              // Missing required field
            },
          },
        },
        {
          id: "node-2", // Not connected
          data: {
            label: "Node 2",
            definition: {
              name: "TestNode",
              inputs: [
                {
                  name: "number",
                  display_name: "Number",
                  input_type: "int",
                  required: true,
                },
              ],
            },
            config: {
              // Missing required field
            },
          },
        },
      ] as Node<WorkflowNodeData>[];

      const connectedNodes = new Set(["node-1"]);
      const missingFields = collectMissingRequiredFields(nodes, connectedNodes);

      expect(missingFields).toHaveLength(1);
      expect(missingFields[0].nodeId).toBe("node-1");
      expect(missingFields[0].name).toBe("text");
    });
  });

  describe("Conditional Requirements", () => {
    it("should make field required when requirement rules are satisfied", () => {
      const requirementRules: InputRequirementRule[] = [
        { field_name: "method", field_value: "POST" },
        { field_name: "method", field_value: "PUT" },
        { field_name: "method", field_value: "PATCH" },
      ];

      const inputDef: InputDefinition = {
        name: "body",
        display_name: "Request Body",
        input_type: "dict",
        required: false, // Not explicitly required
        requirement_rules: requirementRules,
        requirement_logic: "OR",
      };

      // Test with POST method - should be required
      const nodeWithPost = {
        id: "api-node",
        data: {
          label: "API Request",
          config: {
            method: "POST",
            body: {}, // Empty body
          },
          definition: {
            inputs: [inputDef],
          },
        },
      } as Node<WorkflowNodeData>;

      const errorsPost = validateField(inputDef, {}, "api-node", nodeWithPost);
      expect(errorsPost).toHaveLength(1);
      expect(errorsPost[0].code).toBe(ValidationErrorCode.FIELD_REQUIRED);

      // Test with GET method - should not be required
      const nodeWithGet = {
        id: "api-node",
        data: {
          label: "API Request",
          config: {
            method: "GET",
            body: {}, // Empty body
          },
          definition: {
            inputs: [inputDef],
          },
        },
      } as Node<WorkflowNodeData>;

      const errorsGet = validateField(inputDef, {}, "api-node", nodeWithGet);
      expect(errorsGet).toHaveLength(0); // Should not be required for GET
    });

    it("should handle AND logic for requirement rules", () => {
      const requirementRules: InputRequirementRule[] = [
        { field_name: "method", field_value: "POST" },
        { field_name: "has_auth", field_value: true },
      ];

      const inputDef: InputDefinition = {
        name: "auth_token",
        display_name: "Auth Token",
        input_type: "string",
        required: false,
        requirement_rules: requirementRules,
        requirement_logic: "AND",
      };

      // Test with POST and has_auth=true - should be required
      const nodeWithBothConditions = {
        id: "api-node",
        data: {
          label: "API Request",
          config: {
            method: "POST",
            has_auth: true,
            auth_token: "", // Empty token
          },
          definition: {
            inputs: [inputDef],
          },
        },
      } as Node<WorkflowNodeData>;

      const errorsBoth = validateField(inputDef, "", "api-node", nodeWithBothConditions);
      expect(errorsBoth).toHaveLength(1);
      expect(errorsBoth[0].code).toBe(ValidationErrorCode.FIELD_REQUIRED);

      // Test with POST but has_auth=false - should not be required
      const nodeWithOnlyPost = {
        id: "api-node",
        data: {
          label: "API Request",
          config: {
            method: "POST",
            has_auth: false,
            auth_token: "", // Empty token
          },
          definition: {
            inputs: [inputDef],
          },
        },
      } as Node<WorkflowNodeData>;

      const errorsOnlyPost = validateField(inputDef, "", "api-node", nodeWithOnlyPost);
      expect(errorsOnlyPost).toHaveLength(0); // Should not be required when AND condition not met
    });
  });
});
