"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "./button";
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "./dialog";

interface NotificationProps {
  title: string;
  message: string;
  isOpen: boolean;
  onClose: () => void;
  preserveState?: boolean; // Add this to preserve state when closing
}

export function Notification({
  title,
  message,
  isOpen,
  onClose,
  preserveState = true,
}: NotificationProps) {
  const handleClose = React.useCallback(() => {
    // Call the onClose callback
    onClose();
  }, [onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p className="text-muted-foreground text-sm">{message}</p>
        </div>
        <DialogFooter>
          <Button onClick={handleClose} className="w-full">
            OK
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
