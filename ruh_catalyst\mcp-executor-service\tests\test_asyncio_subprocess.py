#!/usr/bin/env python3
"""
Test using asyncio subprocess instead of regular subprocess.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.config.config import settings
from app.services.ssh_manager import initialize_global_ssh_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_asyncio_subprocess():
    """Test using asyncio subprocess."""
    logger.info("=== Testing Asyncio Subprocess ===")
    
    # Initialize SSH key
    if settings.ssh_key_content:
        initialize_global_ssh_key(settings.ssh_key_content)
    
    container_name = "35441857-f358-4595-9993-33abd9afee06_91a237fd-0225-4e02-9e9f-805eff073b07"
    key_file = "E:\\RapidInnovation\\Automation Projects\\Ruh\\ruh_catalyst\\mcp-executor-service\\mcp_ssh_key.pem"
    
    # Build SSH command
    ssh_command = [
        "ssh",
        "-i", key_file,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_name} node dist/index.js"
    ]
    
    logger.info(f"SSH command: {' '.join(ssh_command)}")
    
    try:
        # Create asyncio subprocess
        process = await asyncio.create_subprocess_exec(
            *ssh_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        logger.info("Asyncio subprocess created")
        
        # Send initialize message
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {"listChanged": True},
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "asyncio-test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        message_json = json.dumps(init_message) + "\n"
        logger.info(f"Sending: {message_json.strip()}")
        
        # Write message
        process.stdin.write(message_json.encode())
        await process.stdin.drain()
        
        logger.info("Message sent, waiting for response...")
        
        # Read response with timeout
        try:
            response_line = await asyncio.wait_for(
                process.stdout.readline(),
                timeout=10.0
            )
            
            if response_line:
                response_text = response_line.decode().strip()
                logger.info(f"Received: {response_text}")
                
                try:
                    response_json = json.loads(response_text)
                    logger.info(f"Parsed response: {response_json}")
                    
                    if response_json.get("result"):
                        logger.info("✅ Asyncio subprocess approach works!")
                        return True
                    else:
                        logger.error(f"❌ Unexpected response: {response_json}")
                        return False
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Failed to parse JSON: {e}")
                    return False
            else:
                logger.error("❌ No response received")
                return False
                
        except asyncio.TimeoutError:
            logger.error("❌ Response timeout")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False
    
    finally:
        if 'process' in locals():
            try:
                process.terminate()
                await process.wait()
            except:
                pass


async def main():
    """Main test function."""
    logger.info("Starting Asyncio Subprocess Test")
    
    success = await test_asyncio_subprocess()
    
    logger.info("\n" + "="*60)
    logger.info("ASYNCIO SUBPROCESS TEST SUMMARY:")
    logger.info("="*60)
    
    if success:
        logger.info("🎉 Asyncio subprocess approach works!")
        logger.info("💡 This is the correct approach for async MCP communication")
    else:
        logger.error("💥 Asyncio subprocess approach failed")


if __name__ == "__main__":
    asyncio.run(main())
