#!/usr/bin/env python3
"""
Test Server Type Differentiation

Tests the execution router's ability to differentiate between:
1. SSE (Server-Sent Events) servers
2. STDIO (Standard Input/Output) servers
3. HTTP (Streamable HTTP) servers

And verify the MCP client works correctly with each type.

Usage:
    poetry run python tests/test_server_type_differentiation.py
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.mcp_config_client import MCPConfigClient
from app.services.execution_router import ExecutionRouter
from app.core_.client import MCPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ServerTypeTester:
    """Test class for server type differentiation."""

    def __init__(self):
        self.mcp_config_client = MCPConfigClient()
        self.execution_router = ExecutionRouter()

    def analyze_url_entry(self, url_entry: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a URL entry to determine its type and characteristics."""
        analysis = {
            "entry": url_entry,
            "has_image_name": "image_name" in url_entry,
            "has_url": "url" in url_entry,
            "type": url_entry.get("type", "unknown").lower(),
            "server_category": None,
            "execution_method": None,
            "priority_score": 0,
        }

        # Determine server category and execution method
        if analysis["has_image_name"]:
            analysis["server_category"] = "container"
            analysis["execution_method"] = "container"
            analysis["priority_score"] = 100

            if analysis["type"] == "stdio":
                analysis["server_category"] = "stdio_container"
                analysis["priority_score"] = 150
        elif analysis["has_url"]:
            analysis["execution_method"] = "url"
            analysis["priority_score"] = 50

            if analysis["type"] == "sse":
                analysis["server_category"] = "sse_server"
                analysis["priority_score"] = 75
            elif analysis["type"] == "http" or analysis["type"] == "streamable_http":
                analysis["server_category"] = "http_server"
                analysis["priority_score"] = 60
            else:
                analysis["server_category"] = "unknown_url_server"

        return analysis

    async def test_url_categorization(self, mcp_id: str) -> Dict[str, Any]:
        """Test URL categorization and type detection."""
        logger.info(f"🧪 Testing URL categorization for MCP ID: {mcp_id}")

        try:
            # Fetch MCP configuration
            config = await self.mcp_config_client.get_mcp_config(mcp_id)
            urls_array = config.get("urls", [])

            if not urls_array:
                return {
                    "test": "url_categorization",
                    "success": False,
                    "error": "No URLs found in configuration",
                }

            # Analyze each URL entry
            url_analyses = []
            server_types_found = set()

            for i, url_entry in enumerate(urls_array):
                analysis = self.analyze_url_entry(url_entry)
                analysis["index"] = i
                url_analyses.append(analysis)

                if analysis["server_category"]:
                    server_types_found.add(analysis["server_category"])

            # Test the actual parsing logic
            parsed_result = self.mcp_config_client.parse_urls(urls_array)

            result = {
                "test": "url_categorization",
                "mcp_id": mcp_id,
                "success": True,
                "total_urls": len(urls_array),
                "server_types_found": list(server_types_found),
                "url_analyses": url_analyses,
                "selected_method": parsed_result.execution_method,
                "selected_config": parsed_result.config,
                "selected_category": None,
                "error": None,
            }

            # Determine the category of the selected configuration
            selected_analysis = self.analyze_url_entry(parsed_result.config)
            result["selected_category"] = selected_analysis["server_category"]

            logger.info(f"✅ URL categorization completed")
            logger.info(f"   Server types found: {server_types_found}")
            logger.info(
                f"   Selected: {result['selected_method']} ({result['selected_category']})"
            )

            return result

        except Exception as e:
            logger.error(f"❌ URL categorization failed: {e}")
            return {
                "test": "url_categorization",
                "mcp_id": mcp_id,
                "success": False,
                "error": str(e),
            }

    async def test_priority_logic(self, mcp_id: str) -> Dict[str, Any]:
        """Test the priority logic for different server types."""
        logger.info(f"🧪 Testing priority logic for MCP ID: {mcp_id}")

        try:
            config = await self.mcp_config_client.get_mcp_config(mcp_id)
            urls_array = config.get("urls", [])

            if not urls_array:
                return {
                    "test": "priority_logic",
                    "success": False,
                    "error": "No URLs found in configuration",
                }

            # Analyze and sort by priority
            url_analyses = []
            for i, url_entry in enumerate(urls_array):
                analysis = self.analyze_url_entry(url_entry)
                analysis["index"] = i
                url_analyses.append(analysis)

            # Sort by priority score (highest first)
            sorted_by_priority = sorted(
                url_analyses, key=lambda x: x["priority_score"], reverse=True
            )

            # Test actual selection
            parsed_result = self.mcp_config_client.parse_urls(urls_array)
            selected_analysis = self.analyze_url_entry(parsed_result.config)

            # Check if the highest priority was selected
            highest_priority = sorted_by_priority[0] if sorted_by_priority else None
            priority_correct = (
                highest_priority
                and selected_analysis["server_category"]
                == highest_priority["server_category"]
            )

            result = {
                "test": "priority_logic",
                "mcp_id": mcp_id,
                "success": True,
                "priority_order": [
                    {
                        "index": analysis["index"],
                        "category": analysis["server_category"],
                        "type": analysis["type"],
                        "priority_score": analysis["priority_score"],
                    }
                    for analysis in sorted_by_priority
                ],
                "selected_category": selected_analysis["server_category"],
                "selected_type": selected_analysis["type"],
                "priority_logic_correct": priority_correct,
                "error": None,
            }

            logger.info(f"✅ Priority logic test completed")
            logger.info(
                f"   Priority order: {[p['category'] for p in result['priority_order']]}"
            )
            logger.info(f"   Selected: {result['selected_category']}")
            logger.info(f"   Priority logic correct: {priority_correct}")

            return result

        except Exception as e:
            logger.error(f"❌ Priority logic test failed: {e}")
            return {
                "test": "priority_logic",
                "mcp_id": mcp_id,
                "success": False,
                "error": str(e),
            }

    async def test_client_type_selection(self, mcp_id: str) -> Dict[str, Any]:
        """Test that the correct MCP client type is selected for each server type."""
        logger.info(f"🧪 Testing client type selection for MCP ID: {mcp_id}")

        try:
            # Get execution strategy
            strategy = await self.execution_router.determine_execution_method(mcp_id)
            execution_params = self.execution_router.extract_execution_parameters(
                strategy
            )

            # Analyze what type of client should be created
            client_analysis = {
                "execution_method": strategy.method,
                "config": strategy.config,
                "execution_params": execution_params,
                "expected_client_type": None,
                "connection_details": {},
            }

            if strategy.method == "container":
                client_analysis["expected_client_type"] = "stdio_container"
                client_analysis["connection_details"] = {
                    "image_name": execution_params.get("image_name"),
                    "container_type": execution_params.get("container_type"),
                    "uses_container_api": True,
                }
            elif strategy.method == "url":
                connection_type = execution_params.get("connection_type", "sse")
                if connection_type == "sse":
                    client_analysis["expected_client_type"] = "sse_client"
                elif connection_type in ["http", "streamable_http"]:
                    client_analysis["expected_client_type"] = "http_client"
                else:
                    client_analysis["expected_client_type"] = "unknown_url_client"

                client_analysis["connection_details"] = {
                    "server_url": execution_params.get("server_script_path"),
                    "connection_type": connection_type,
                    "uses_container_api": False,
                }

            result = {
                "test": "client_type_selection",
                "mcp_id": mcp_id,
                "success": True,
                "client_analysis": client_analysis,
                "error": None,
            }

            logger.info(f"✅ Client type selection test completed")
            logger.info(
                f"   Expected client type: {client_analysis['expected_client_type']}"
            )
            logger.info(f"   Execution method: {strategy.method}")

            return result

        except Exception as e:
            logger.error(f"❌ Client type selection test failed: {e}")
            return {
                "test": "client_type_selection",
                "mcp_id": mcp_id,
                "success": False,
                "error": str(e),
            }

    async def test_server_type_compatibility(self, mcp_id: str) -> Dict[str, Any]:
        """Test compatibility between server types and execution methods."""
        logger.info(f"🧪 Testing server type compatibility for MCP ID: {mcp_id}")

        try:
            config = await self.mcp_config_client.get_mcp_config(mcp_id)
            urls_array = config.get("urls", [])

            compatibility_tests = []

            for i, url_entry in enumerate(urls_array):
                analysis = self.analyze_url_entry(url_entry)

                # Test if this entry would be compatible with different execution methods
                compatibility = {
                    "index": i,
                    "entry": url_entry,
                    "category": analysis["server_category"],
                    "type": analysis["type"],
                    "compatible_with": {
                        "container_execution": analysis["has_image_name"],
                        "url_execution": analysis["has_url"],
                        "sse_protocol": analysis["type"] == "sse"
                        and analysis["has_url"],
                        "stdio_protocol": analysis["type"] == "stdio"
                        and analysis["has_image_name"],
                        "http_protocol": analysis["type"] in ["http", "streamable_http"]
                        and analysis["has_url"],
                    },
                }

                compatibility_tests.append(compatibility)

            # Test the selected configuration's compatibility
            parsed_result = self.mcp_config_client.parse_urls(urls_array)
            selected_analysis = self.analyze_url_entry(parsed_result.config)

            result = {
                "test": "server_type_compatibility",
                "mcp_id": mcp_id,
                "success": True,
                "compatibility_tests": compatibility_tests,
                "selected_config_compatibility": {
                    "category": selected_analysis["server_category"],
                    "execution_method": parsed_result.execution_method,
                    "is_compatible": self._check_compatibility(
                        selected_analysis, parsed_result.execution_method
                    ),
                },
                "error": None,
            }

            logger.info(f"✅ Server type compatibility test completed")
            logger.info(f"   Tested {len(compatibility_tests)} configurations")
            logger.info(
                f"   Selected config compatible: {result['selected_config_compatibility']['is_compatible']}"
            )

            return result

        except Exception as e:
            logger.error(f"❌ Server type compatibility test failed: {e}")
            return {
                "test": "server_type_compatibility",
                "mcp_id": mcp_id,
                "success": False,
                "error": str(e),
            }

    def _check_compatibility(
        self, analysis: Dict[str, Any], execution_method: str
    ) -> bool:
        """Check if the analysis is compatible with the execution method."""
        if execution_method == "container":
            return analysis["has_image_name"]
        elif execution_method == "url":
            return analysis["has_url"]
        return False

    async def run_comprehensive_tests(self, mcp_id: str) -> Dict[str, Any]:
        """Run all server type differentiation tests."""
        logger.info("🚀 Starting comprehensive server type differentiation tests")
        logger.info("=" * 70)

        all_results = {"mcp_id": mcp_id, "timestamp": time.time(), "tests": {}}

        # Test 1: URL Categorization
        logger.info("\n📋 Test 1: URL Categorization")
        all_results["tests"]["url_categorization"] = await self.test_url_categorization(
            mcp_id
        )

        # Test 2: Priority Logic
        logger.info("\n🎯 Test 2: Priority Logic")
        all_results["tests"]["priority_logic"] = await self.test_priority_logic(mcp_id)

        # Test 3: Client Type Selection
        logger.info("\n🔧 Test 3: Client Type Selection")
        all_results["tests"]["client_type_selection"] = (
            await self.test_client_type_selection(mcp_id)
        )

        # Test 4: Server Type Compatibility
        logger.info("\n🔄 Test 4: Server Type Compatibility")
        all_results["tests"]["server_type_compatibility"] = (
            await self.test_server_type_compatibility(mcp_id)
        )

        # Calculate overall success
        successful_tests = sum(
            1 for test in all_results["tests"].values() if test.get("success", False)
        )
        total_tests = len(all_results["tests"])
        all_results["overall_success"] = successful_tests == total_tests
        all_results["success_rate"] = successful_tests / total_tests

        return all_results

    def print_test_summary(self, results: Dict[str, Any]):
        """Print comprehensive test summary."""
        print("\n" + "=" * 80)
        print("📊 SERVER TYPE DIFFERENTIATION TEST SUMMARY")
        print("=" * 80)

        print(f"MCP ID: {results['mcp_id']}")
        print(f"Overall Success: {'✅' if results['overall_success'] else '❌'}")
        print(f"Success Rate: {results['success_rate']:.1%}")

        print(f"\n📋 Individual Test Results:")
        for test_name, test_result in results["tests"].items():
            status = "✅" if test_result.get("success", False) else "❌"
            print(f"   {status} {test_name}: {test_result.get('error', 'Success')}")

            # Print specific details
            if test_name == "url_categorization" and test_result.get("success"):
                print(
                    f"      → Server types found: {test_result.get('server_types_found', [])}"
                )
                print(
                    f"      → Selected category: {test_result.get('selected_category')}"
                )

            if test_name == "priority_logic" and test_result.get("success"):
                print(
                    f"      → Priority logic correct: {test_result.get('priority_logic_correct')}"
                )
                priority_order = test_result.get("priority_order", [])
                if priority_order:
                    print(f"      → Top priority: {priority_order[0]['category']}")

            if test_name == "client_type_selection" and test_result.get("success"):
                client_analysis = test_result.get("client_analysis", {})
                print(
                    f"      → Expected client: {client_analysis.get('expected_client_type')}"
                )

        print("=" * 80)


async def main():
    """Main test execution."""
    # Test parameters
    mcp_id = "********-f358-4595-9993-33abd9afee06"

    print("🧪 Server Type Differentiation Tests")
    print("   Testing SSE, STDIO, and HTTP server type detection and routing")
    print("")

    tester = ServerTypeTester()

    try:
        results = await tester.run_comprehensive_tests(mcp_id)
        tester.print_test_summary(results)

        # Save results to file
        results_file = Path(__file__).parent / "server_type_test_results.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Test results saved to: {results_file}")

        return 0 if results["overall_success"] else 1

    except KeyboardInterrupt:
        logger.info("⏹️ Tests cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Tests cancelled")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
