import axios, { AxiosError, AxiosRequestConfig } from "axios";
import {
  getAccessToken,
  getRefreshToken,
  clearAuthCookies,
} from "@/services/authCookies";
import { getAccessTokenRoute, loginRoute } from "@/app/shared/routes";

// Import the auth API dynamically to avoid circular dependency
const getAuthApi = async () => {
  const { authApi } = await import("@/app/api/auth");
  return authApi;
};

// // Helper function to clear user store
// const clearUserStore = () => {
//   useUserStore.getState().clearUser();
// };

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

// Request interceptor to add authorization header
api.interceptors.request.use(
  async (config) => {
    const token = await getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    config.headers["ngrok-skip-browser-warning"] = "true";
    return config;
  },
  (error: any) => {
    return Promise.reject(
      new Error(
        `Request interceptor error: ${error.message || "Unknown error"}`
      )
    );
  }
);

// Response interceptor for handling token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    if (originalRequest.url?.includes(getAccessTokenRoute)) {
      await clearAuthCookies();
      if (typeof window !== "undefined") {
        // Check if already on login page to prevent redirect loop if login page itself makes an API call
        if (window.location.pathname !== loginRoute) {
          window.location.href = loginRoute;
        }
      }
      return Promise.reject(new Error("Invalid or expired refresh token."));
    }

    // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
    if (
      (error.response?.status === 401 || error.response?.status === 403) &&
      !originalRequest._retry
    ) {
      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      try {
        // Get the refresh token
        const refreshToken = await getRefreshToken();

        if (!refreshToken) {
          await clearAuthCookies();
          //   clearUserStore();
          window.location.href = loginRoute;
          return Promise.reject(new Error("No refresh token available"));
        }

        // Get the auth API and generate a new access token
        const authApi = await getAuthApi();
        try {
          const tokenResponse = await authApi.generateAccessToken(refreshToken);

          if (tokenResponse.success && tokenResponse.access_token) {
            // Update the authorization header with the new token
            originalRequest.headers = {
              ...originalRequest.headers,
              Authorization: `Bearer ${tokenResponse.access_token}`,
            };

            // Retry the original request with the new token
            return api(originalRequest);
          }
        } catch (tokenError: any) {
          await clearAuthCookies();
          //   clearUserStore();
          window.location.href = loginRoute;
          return Promise.reject(tokenError);
        }

        // If we get here, token refresh failed
        await clearAuthCookies();
        // clearUserStore();
        window.location.href = loginRoute;
        return Promise.reject(new Error("Token refresh failed"));
      } catch (refreshError) {
        // Clear cookies and redirect to login on refresh error
        await clearAuthCookies();
        // clearUserStore();
        window.location.href = loginRoute;
        return Promise.reject(refreshError);
      }
    }

    // For other errors, just reject the promise
    return Promise.reject(error);
  }
);

export default api;
