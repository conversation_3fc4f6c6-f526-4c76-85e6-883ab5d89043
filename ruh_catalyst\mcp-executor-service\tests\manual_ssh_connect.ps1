DEFAULT_SSH_KEY_CONTENT=""

# Write key to a temporary file
TEMP_KEY_FILE=$(mktemp)
echo "$DEFAULT_SSH_KEY_CONTENT" | base64 -d > "$TEMP_KEY_FILE"
chmod 600 "$TEMP_KEY_FILE"

# Use the key in an SSH command
ssh -i "$TEMP_KEY_FILE" \
    -o StrictHostKeyChecking=no \
    -o UserKnownHostsFile=/dev/null \
    -o ConnectTimeout=30 \
    -o IdentitiesOnly=yes \
    -p 22 ubuntu@************ \
    "echo 'SSH connection successful'"

# Clean up
rm -f "$TEMP_KEY_FILE"
