import React from "react";
import { <PERSON><PERSON><PERSON>ircle, AlertTriangle, Info } from "lucide-react";
import { ValidationError } from "@/lib/validation/types";
import { cn } from "@/lib/utils";

interface ValidationErrorsProps {
  errors: ValidationError[];
  warnings?: ValidationError[];
  infos?: ValidationError[];
  className?: string;
}

/**
 * Component for displaying validation errors, warnings, and info messages
 */
export function ValidationErrors({
  errors,
  warnings = [],
  infos = [],
  className,
}: ValidationErrorsProps) {
  // If there are no messages, don't render anything
  if (errors.length === 0 && warnings.length === 0 && infos.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {errors.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-destructive flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            Validation Errors
          </h3>
          <ul className="list-disc pl-5 text-xs space-y-1 text-destructive">
            {errors.map((error, index) => (
              <li key={`error-${index}`}>
                {error.message}
                {error.nodeId && (
                  <span className="text-muted-foreground ml-1">
                    (Node: {error.nodeId})
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {warnings.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-warning flex items-center gap-1">
            <AlertTriangle className="h-4 w-4" />
            Warnings
          </h3>
          <ul className="list-disc pl-5 text-xs space-y-1 text-warning">
            {warnings.map((warning, index) => (
              <li key={`warning-${index}`}>
                {warning.message}
                {warning.nodeId && (
                  <span className="text-muted-foreground ml-1">
                    (Node: {warning.nodeId})
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {infos.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-info flex items-center gap-1">
            <Info className="h-4 w-4" />
            Information
          </h3>
          <ul className="list-disc pl-5 text-xs space-y-1 text-info">
            {infos.map((info, index) => (
              <li key={`info-${index}`}>
                {info.message}
                {info.nodeId && (
                  <span className="text-muted-foreground ml-1">
                    (Node: {info.nodeId})
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

interface ValidationErrorBadgeProps {
  count: number;
  type: "error" | "warning" | "info";
  className?: string;
}

/**
 * Component for displaying a badge with the count of validation issues
 */
export function ValidationErrorBadge({
  count,
  type,
  className,
}: ValidationErrorBadgeProps) {
  if (count === 0) return null;

  const Icon = type === "error" 
    ? AlertCircle 
    : type === "warning" 
      ? AlertTriangle 
      : Info;

  const baseClasses = "flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium";
  const typeClasses = type === "error" 
    ? "bg-destructive/10 text-destructive" 
    : type === "warning" 
      ? "bg-warning/10 text-warning" 
      : "bg-info/10 text-info";

  return (
    <div className={cn(baseClasses, typeClasses, className)}>
      <Icon className="h-3 w-3" />
      <span>{count}</span>
    </div>
  );
}
