import math
import shutil
from pathlib import Path
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import RedirectResponse
from dotenv import load_dotenv
import httpx
import git
from app.core.auth_guard import role_required
from app.services.mcp_service import MCPServiceClient
from app.services.user_service import UserServiceClient
from app.core.config import settings
from app.schemas.github import (
    PaginatedRepositoriesResponse,
    PaginationMetadata,
    RepositoryBasicInfo,
    RepositoryBranchesResponse,
    BranchInfo,
)
from app.utils.constants import (
    GITHUB_AUTH_URL,
    GITHUB_TOKEN_URL,
    GITHUB_API_URL,
)

load_dotenv()
mcp_client = MCPServiceClient()
user_service = UserServiceClient()

CLIENT_ID = settings.CLIENT_ID
CLIENT_SECRET = settings.CLIENT_SECRET
REDIRECT_URI = settings.REDIRECT_URI

auth_router = APIRouter(prefix="/auth/github", tags=["Authentication"])


async def make_github_api_request(
    url: str, token: str, method: str = "GET", params: Dict = None, json_data: Dict = None
) -> httpx.Response:
    headers = {
        "Authorization": f"Bearer {token}",
        "Accept": "application/vnd.github.v3+json",
    }
    async with httpx.AsyncClient() as client:
        try:
            if method.upper() == "GET":
                response = await client.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = await client.post(url, headers=headers, json=json_data, params=params)
            else:
                raise HTTPException(status_code=500, detail=f"Unsupported HTTP method: {method}")
            response.raise_for_status()
            return response
        except httpx.HTTPStatusError as e:
            status_code = e.response.status_code
            error_detail = f"GitHub API error: {status_code}"
            try:
                error_json = e.response.json()
                if "message" in error_json:
                    error_detail += f" - {error_json['message']}"
                if "errors" in error_json:
                    error_detail += f" Details: {error_json['errors']}"
                if "error_description" in error_json:
                    error_detail += f" Description: {error_json['error_description']}"
            except Exception:
                pass

            # Normalize 401/403 to 400 so frontend knows it's a token issue, not an app-level auth problem
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid or expired GitHub token. Please re-authenticate with GitHub.",
                )

            raise HTTPException(status_code=status_code, detail=error_detail)


@auth_router.get("/")
async def auth_login_route(
    user_id: str = Query(..., description="User ID to authenticate with GitHub")
):
    """
    Redirects the user to GitHub for authentication.
    Takes user_id as input parameter and passes it through the OAuth flow.
    Effective URL: /auth/github/?user_id=<user_id>
    """

    print(
        f"Attempting to redirect to GitHub. ClientID: {CLIENT_ID}, RedirectURI: {REDIRECT_URI}, UserID: {user_id}"
    )

    if not CLIENT_ID:
        raise HTTPException(
            status_code=500, detail="Server configuration error: CLIENT_ID not set."
        )

    if not user_id:
        raise HTTPException(status_code=400, detail="User ID parameter is required.")

    # Include user ID in state parameter to pass it through OAuth flow
    import urllib.parse

    state = urllib.parse.quote(user_id)

    return RedirectResponse(
        url=f"{GITHUB_AUTH_URL}?client_id={CLIENT_ID}&redirect_uri={REDIRECT_URI}&scope=read:user%20repo&state={state}"
    )


@auth_router.get("/callback")
async def auth_callback_route(code: str, state: str = None):
    """
    GitHub redirects here after successful authentication.
    Exchanges the authorization code for an access token.
    Gets user ID from state parameter passed through OAuth flow.
    Effective URL: /auth/github/callback
    """
    print(f"Received callback from GitHub with code: {code}, state: {state}")

    if not CLIENT_ID or not CLIENT_SECRET:
        raise HTTPException(
            status_code=500,
            detail="Server configuration error: CLIENT_ID or CLIENT_SECRET not set.",
        )

    if not state:
        raise HTTPException(
            status_code=400,
            detail="State parameter missing. User ID not provided in OAuth flow.",
        )

    # Decode the user ID from state parameter
    import urllib.parse

    user_id = urllib.parse.unquote(state)
    print(f"User ID from state: {user_id}")

    # Get user details from user service using user_id
    try:
        user_response = await user_service.validate_user(user_id)
        if not user_response.get("success"):
            raise HTTPException(status_code=404, detail=f"User not found with ID: {user_id}")
        current_user = user_response.get("user")
        print(f"Found user: {current_user.get('email')}")
    except Exception as e:
        print(f"Error getting user by ID: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving user information")

    async with httpx.AsyncClient() as client:
        try:
            token_resp = await client.post(
                GITHUB_TOKEN_URL,
                headers={"Accept": "application/json"},
                data={
                    "client_id": CLIENT_ID,
                    "client_secret": CLIENT_SECRET,
                    "code": code,
                    "redirect_uri": REDIRECT_URI,
                },
            )
            token_resp.raise_for_status()
            token_data = token_resp.json()
            print(f"Token data: {token_data}")
        except httpx.HTTPStatusError as e:
            error_detail = f"GitHub token exchange failed: {e.response.status_code}."
            try:
                gh_error = e.response.json()
                error_detail += (
                    f" GitHub Reason: {gh_error.get('error_description', e.response.text)}"
                )
            except Exception:
                error_detail += f" Raw Response: {e.response.text}"
            print(f"ERROR: {error_detail}")
            raise HTTPException(status_code=400, detail=error_detail)
        except httpx.RequestError as e:
            print(f"ERROR: Network error during token exchange: {e}")
            raise HTTPException(status_code=500, detail=f"Network error during token exchange: {e}")

        access_token = token_data.get("access_token")

        if not access_token:
            print(f"ERROR: Access token not found in GitHub response. Data: {token_data}")
            raise HTTPException(
                status_code=400, detail="Failed to retrieve access token from GitHub response."
            )

        print(f"Access token successfully obtained: {access_token}")

        try:
            response = await user_service.update_user_git_tokens(
                user_id=user_id, access_token=access_token
            )
            if not response.success:
                print(f"[ERROR] : {response.message}")
                raise HTTPException(
                    status_code=400, detail="Error saving git details into user-service"
                )
        except HTTPException as e:
            print(f"[ERROR OCCERED WHEN SAVING ACCESS TOKEN] {e}")
            raise e
        user_data = {}
        try:
            user_response = await make_github_api_request(f"{GITHUB_API_URL}/user", access_token)
            user_data = user_response.json()
            print(f"User details fetched for: {user_data.get('login')}")
        except HTTPException as e:
            print(f"WARNING: Could not fetch user details post-callback: {e.detail}")
            # user_data remains {}

        # Redirect to frontend with success status
        print(f"GitHub OAuth successful for user: {current_user.get('email')}")
        return RedirectResponse(settings.REDIRECT_URI_AFTER_GITHUB_OAUTH)


@auth_router.get("/user/me", tags=["User Operations"])
async def get_authenticated_user_details(
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    user_id = current_user["user_id"]
    github_token_str = await user_service.get_user_github_token(user_id)
    print(f"github_token_str: {github_token_str}")
    if not github_token_str:
        raise HTTPException(
            status_code=400,
            detail="GitHub token not found for user. Please authenticate with GitHub first.",
        )
    response = await make_github_api_request(f"{GITHUB_API_URL}/user", github_token_str)
    return response.json()


@auth_router.get(
    "/user/repos", response_model=PaginatedRepositoriesResponse, tags=["User Operations"]
)
async def list_user_repositories_paginated(
    current_user: dict = Depends(role_required(["user", "admin"])),
    type: str = Query("all", description="Type of repositories to list (all, owner, member)"),
    sort: str = Query(
        "updated", description="Sort repositories by (created, updated, pushed, full_name)"
    ),
    direction: str = Query("desc", description="Sort direction (asc, desc)"),
    search: git.Optional[str] = Query(None, description="Search term to filter repository names"),
    page: int = Query(1, ge=1, description="Page number for pagination"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
):
    """
    Lists GitHub repositories for the authenticated user with search and pagination.
    """
    try:
        user_id = current_user["user_id"]
        # This function in user_service should return the raw token string
        github_token_str = await user_service.get_user_github_token(user_id)

        if not github_token_str:  # Ensure token exists
            raise HTTPException(
                status_code=400,  # Or 401
                detail="GitHub token not found for user. Please authenticate with GitHub first.",
            )

        # Step 1: Fetch ALL relevant repositories from GitHub
        all_fetched_repos_from_github = []
        current_page_github = 1
        github_per_page = 100  # Fetch in larger chunks from GitHub to minimize API calls

        while True:
            github_params = {
                "type": type,
                "sort": sort,
                "direction": direction,
                "page": current_page_github,
                "per_page": github_per_page,
            }
            print(f"[DEBUG] Making GitHub API request to: {GITHUB_API_URL}/user/repos")
            print(f"[DEBUG] Request params: {github_params}")

            response = await make_github_api_request(
                f"{GITHUB_API_URL}/user/repos", github_token_str, params=github_params
            )
            repos_page_from_github = response.json()
            if not repos_page_from_github:
                break
            all_fetched_repos_from_github.extend(repos_page_from_github)
            if len(repos_page_from_github) < github_per_page:
                break
            current_page_github += 1

        # Step 2: Filter by search term (in-memory) if provided
        if search:
            search_term_lower = search.lower()
            filtered_repos = [
                repo
                for repo in all_fetched_repos_from_github
                if search_term_lower in repo["name"].lower()
            ]
        else:
            filtered_repos = all_fetched_repos_from_github

        # Step 3: Paginate the filtered list
        total_items = len(filtered_repos)
        total_pages = math.ceil(total_items / page_size) if total_items > 0 else 1

        # Ensure current page is within valid range
        current_page_validated = min(max(1, page), total_pages)

        start_index = (current_page_validated - 1) * page_size
        end_index = start_index + page_size
        paginated_items_raw = filtered_repos[start_index:end_index]

        # Step 4: Transform to Pydantic model
        repositories_basic_info = [
            RepositoryBasicInfo.from_github_dict(repo) for repo in paginated_items_raw
        ]

        # Step 5: Create pagination metadata
        metadata = PaginationMetadata(
            total_items=total_items,
            total_pages=total_pages,
            current_page=current_page_validated,
            page_size=page_size,
            has_next_page=current_page_validated < total_pages,
            has_previous_page=current_page_validated > 1,
        )

        return PaginatedRepositoriesResponse(
            message=f"Retrieved {len(repositories_basic_info)} repositories (page {current_page_validated}/{total_pages})",
            repositories=repositories_basic_info,
            metadata=metadata,
        )

    except HTTPException as e:
        # Specific error for token not found or invalid
        if e.status_code == 401 or e.status_code == 403:  # Unauthorized or Forbidden from GitHub
            raise HTTPException(
                status_code=400,  # Client error
                detail="Invalid or expired GitHub token. Please re-authenticate with GitHub.",
            )
        if (
            e.status_code == 404 and "GitHub token not found" in str(e.detail).lower()
        ):  # From your custom logic
            raise HTTPException(
                status_code=400,
                detail="GitHub token not found. Please authenticate with GitHub first.",
            )
        raise e  # Re-raise other HTTPExceptions
    except Exception as e:
        print(f"Error fetching repositories: {type(e).__name__} - {e}")
        # import traceback
        # print(traceback.format_exc()) # For more detailed debugging
        raise HTTPException(status_code=500, detail="Failed to fetch repositories from GitHub")


@auth_router.get(
    "/repos/{repo_name}/branches",
    response_model=RepositoryBranchesResponse,
    tags=["Repository Operations"],
)
async def list_repository_branches_formatted(
    repo_name: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Lists branches for a specific GitHub repository.
    """
    try:
        user_id = current_user["user_id"]
        github_token_str = await user_service.get_user_github_token(user_id)

        if not github_token_str:
            raise HTTPException(
                status_code=400,
                detail="GitHub token not found. Please authenticate with GitHub first.",
            )

        # Get the authenticated GitHub user to determine the owner
        # This is necessary because the /repos/{owner}/{repo} endpoint requires the owner.
        user_info_response = await make_github_api_request(
            f"{GITHUB_API_URL}/user", github_token_str
        )
        github_user_details = user_info_response.json()
        owner_login = github_user_details.get("login")

        if not owner_login:
            raise HTTPException(
                status_code=500, detail="Could not determine GitHub username from token."
            )

        # Fetch branches - GitHub API for branches is paginated by default (30 per page)
        # For simplicity, if a repo usually has <100 branches, fetch all. Otherwise, implement pagination.
        all_branches_raw = []
        current_page_github = 1
        github_per_page = 100
        while True:
            branch_params = {"page": current_page_github, "per_page": github_per_page}
            response = await make_github_api_request(
                f"{GITHUB_API_URL}/repos/{owner_login}/{repo_name}/branches",
                github_token_str,
                params=branch_params,
            )
            branches_page_raw = response.json()
            if (
                not branches_page_raw
            ):  # No more branches or an error occurred that didn't raise HTTPStatusError
                break
            all_branches_raw.extend(branches_page_raw)
            if len(branches_page_raw) < github_per_page:
                break
            current_page_github += 1

        if not all_branches_raw and current_page_github == 1:  # No branches found at all
            # This might happen for an empty repo or if the repo doesn't exist under this owner/token combo,
            # make_github_api_request should ideally raise 404 from response.raise_for_status()
            # but as a fallback:
            pass  # Or return empty list: branches_info = []

        branches_info = [BranchInfo.from_github_dict(branch) for branch in all_branches_raw]

        return RepositoryBranchesResponse(
            message=f"Retrieved {len(branches_info)} branches for {owner_login}/{repo_name}",
            repository_full_name=f"{owner_login}/{repo_name}",
            owner_login=owner_login,
            branches=branches_info,
        )

    except HTTPException as e:
        if e.status_code == 401 or e.status_code == 403:
            raise HTTPException(
                status_code=400,
                detail="Invalid or expired GitHub token. Please re-authenticate with GitHub.",
            )
        if e.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail=f"Repository '{owner_login}/{repo_name}' not found or access denied.",
            )
        raise e
    except Exception as e:
        print(f"Error fetching branches for {repo_name}: {type(e).__name__} - {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to fetch branches for repository '{repo_name}'"
        )
